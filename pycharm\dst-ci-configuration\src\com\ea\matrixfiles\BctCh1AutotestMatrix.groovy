package com.ea.matrixfiles

import com.ea.lib.model.autotest.AutotestCategory
import com.ea.lib.model.autotest.FrostedAutotestCategory
import com.ea.lib.model.autotest.Name
import com.ea.lib.model.autotest.Platform
import com.ea.lib.model.autotest.Region
import com.ea.lib.model.autotest.TestInfo
import com.ea.lib.model.autotest.TestSuite

/**
 * See <a href='https://docs.google.com/document/d/1pTxwXhm0T5Mstbg4uaBkjWruC5ntz7pZxqGsmBEcTwk/preview'>Flow Overview</a>
 * See <a href='https://docs.google.com/document/d/1mTfOtPPX93529M7EgmmaGBpmcuMoDyhZ7ZlYCKoVUzw/preview'>Jenkins Config</a>
 **/
class BctCh1AutotestMatrix extends AutotestMatrix {
    private static final String CH1_CODE_DEV = 'CH1-code-dev'
    private static final String CH1_CONTENT_DEV = 'CH1-content-dev'
    private static final String CH1_TO_TRUNK = 'CH1-to-trunk'
    private static final String CH1_2024_TO_CH1 = '2024_1_dev-bf-to-CH1'
    private static final String CH1_CONTENT_DEV_SANITIZERS = 'CH1-content-dev-sanitizers'
    private static final String CH1_STAGE = 'CH1-stage'
    private static final String CH1_RELEASE = 'CH1-release'
    private static final String TASK2 = 'task2'
    private static final String ECS_SPLINES = 'ecs-splines'
    private static final String CH1_CONTENT_DEV_C1S2B1 = 'CH1-content-dev-C1S2B1'

    /* ********** ********** ********** ********** **********
   Some default values that can be used across lots of tests
    */
    private static final List EXTRA_ARGS_V1 = ['--rest-port', '5143', '--timeout-client-level-load', '00:15:00', '--runtime-connect-timeout', 360, '--timeout-client-deploy', '01:10:00', '--atf-rest-heartbeat-enabled', 'true', '--xbsx-skip-defer-exec', 'true']
    private static final List EXTRA_ARGS_V2 = ['--timeout-client-level-load', '00:12:00', '--runtime-connect-timeout', 360, '--timeout-client-deploy', '01:10:00', '--atf-rest-heartbeat-enabled', 'true', '--xbsx-skip-defer-exec', 'true', '--kill-subprocesses-on-exit', 'true']
    private static final List EXTRA_ARGS_SANITIZERS = ['--runtime-connect-timeout', 360, '--crash-dump-timeout', 300, '--timeout-client-level-load', '00:30:00', '--timeout-server-level-load', '00:30:00', '--level-load-timeout', 5, '--timeout-client-start', '00:30:00', '--timeout-client-deploy', '01:10:00', '--xbsx-skip-defer-exec', 'true', '--atf-rest-heartbeat-enabled', 'true', '--kill-subprocesses-on-exit', 'true', '--process-timeout', '1200', '--heartbeat-timeout', '180']
    private static final List EXTRA_ARGS_SANITIZERS_PS5 = EXTRA_ARGS_SANITIZERS + ['--ps5-deployment-method', 'Push', '--ps5-run-options', 'flexibleMemory:2048', '--atf-test-groups', 'ps5_devmode_pool', '--default-client-target-group-overrides', 'ps5_devmode_pool']
    private static final List EXTRA_ARGS_V3 = ['--timeout-client-level-load', '00:12:00', '--runtime-connect-timeout', 360, '--ps5-deployment-method', 'Push', '--timeout-client-deploy', '01:10:00', '--atf-rest-heartbeat-enabled', 'true', '--xbsx-skip-defer-exec', 'true', '--kill-subprocesses-on-exit', 'true', '--xbapp-deployment-print-summary', 'true', '--test-state-change-timeout', 120]
    private static final List EXTRA_ARGS_SOAK = ['--timeout-client-level-load', '00:12:00', '--runtime-connect-timeout', 360, '--atf-rest-heartbeat-enabled', 'true', '--xbsx-skip-defer-exec', 'true', '--kill-subprocesses-on-exit', 'true', '--test-state-change-timeout', 2880, '--spin-server-runtime-restart-with-existing-pod', true, '--spin-thin-client-runtime-restart-with-existing-pod', true, '--spin-archive-fleets-after-time-span', '08:00:00']
    private static final List EXTRA_ARGS_SPIN = [
        '--max-parallel-tests', 1,
        '--additional-pool-types', 'spin',
        '--spin-token-generator-iam-credentials-path', '//filer.dice.ad.ea.com/QVE/qve_certificates/gsh-dice-glacier-iam.json',
        '--spin-server-cluster-ensemble-hostname', '**************',
        '--spin-thin-client-cluster-ensemble-hostname', '*************',
        '--ensemble-server', 'a2c7831395c0b45f08208dd00c0cb5bf-d0e71edd0c218b13.elb.eu-central-1.amazonaws.com',
        '--ensemble-port', 7333,
        '--ensemble-grpc', true,
        '--ensemble-session-heartbeat-enabled', true,
        '--resource-pool-get-target-timeout', 3600,
        '--enable-ensemble-pub-sub-verification-heartbeat', true,
        '--enable-ensemble-rpc-verification-heartbeat', true,
        '--spin-server-chart-version', '3.1.10-glacier',
        '--spin-thin-client-chart-version', '1.0.2',
        '--runtime-shutdown-timeout', 600, // mharnetty - temporarily increasing this to work around issue with missing attachments in Spin deployments
        '--abort-test-runner-on-test-state-change-timeout', false,
    ]
    private static final List EXTRA_ARGS_L_SCALE = EXTRA_ARGS_SPIN + [
        '--default-server-platform', 'linux',
        '--default-server-acquisition-strategy', 'spin',
        '--default-thin-client-acquisition-strategy', 'spin',
        '--spin-thin-client-cpu-request', '3538m',
    ]
    private static final List EXTRA_ARGS_L_SCALE_MP = EXTRA_ARGS_L_SCALE + [
        '--spin-server-cpu-request', '7600m',
        '--spin-server-memory-request', '4800Mi',
        '--spin-server-memory-limit', '4800Mi',
        '--spin-server-node-pool', 'dice-glacier-aow-c6a-4xlarge-hton',
    ]
    private static final List EXTRA_ARGS_L_SCALE_MP_DENSITY1 = EXTRA_ARGS_L_SCALE + [
        '--spin-server-cpu-request', '14000m',
        '--spin-server-memory-request', '4800Mi',
        '--spin-server-memory-limit', '4800Mi',
        '--spin-server-node-pool', 'dice-glacier-aow-c6a-4xlarge-hton',
    ]
    private static final List EXTRA_ARGS_L_SCALE_MP_DENSITY2 = EXTRA_ARGS_L_SCALE + [
        '--spin-thin-client-cpu-request', '3538m',
        '--spin-server-cpu-request', '23000m',
        '--spin-server-memory-request', '4800Mi',
        '--spin-server-memory-limit', '4800Mi',
        '--spin-server-node-pool', 'dice-glacier-c6a-12xlarge-hton-full-box',
    ]
    /*
    private static final List EXTRA_ARGS_L_SCALE_F2P = EXTRA_ARGS_L_SCALE + [
        '--spin-server-cpu-request', '7600m',
        '--spin-server-memory-request', '6800Mi',
        '--spin-server-memory-limit', '6800Mi',
        '--spin-server-node-pool', 'dice-glacier-f2p-c6a-12xlarge-hton',
    ]
    */
    private static final List EXTRA_ARGS_L_SCALE_F2P_DENSITY1 = EXTRA_ARGS_L_SCALE + [
        '--spin-server-cpu-request', '46000m',
        '--spin-server-memory-request', '6800Mi',
        '--spin-server-memory-limit', '6800Mi',
        '--spin-server-node-pool', 'dice-glacier-f2p-c6a-12xlarge-hton',
    ]
    private static final List EXTRA_ARGS_SPIN_SERVER = EXTRA_ARGS_SPIN + [
        '--default-server-platform', 'linux',
        '--default-server-acquisition-strategy', 'spin',
        '--spin-server-cpu-request', '7600m',
        '--spin-server-memory-request', '4800Mi',
        '--spin-server-memory-limit', '4800Mi',
        '--spin-server-node-pool', 'dice-glacier-c6a-12xlarge-hton-qv',
    ]
    /*
    private static final List EXTRA_ARGS_SOAK_3ROUNDS = [
        '--soaking-test-stop-mode', 'round',
        '--soaking-test-total-rounds', 3,
        '--soaking-test-running-time', '02:00:00', // timer guard setting to ensure the test don't exceed this amount of time if any issue
    ]
    */
    private static final List EXTRA_ARGS_SHIFT_BASE = [
        '--build-indexer-names', 'ShiftFBBuildIndexer',
        '--shift-delivery-recipient-username', '<EMAIL>',
        '--shift-jwt-token-file-path', '//filer.dice.ad.ea.com/QVE/qve_certificates/shift_token_svc_qve_bf_shift.txt',
        '--additional-users-to-grant-permission', 'svc_RPMShiftDelivery@AD',
        '--symbol-resolve-method', 'Kobold',
        '--kobold-url', 'https://kobold.qve.dice.se/',
        '--ps5-build-predeployed', true,
        '--xbox-installed-package-name', 'ElectronicArtsMobile.93317BAB558E',
        '--xbox-installed-application-id', 'BattlefieldGame.Main',
        '--timeout-client-deploy', '04:00:00',
    ]

    /*private static final List EXTRA_ARGS_SHIFT_CH1_CODE_DEV = EXTRA_ARGS_SHIFT_BASE + [
        '--shift-server-platforms-to-sku-uuids', 'windows:4ed24fd5-860c-439a-b7c8-09bd64c05293',
        '--shift-client-platforms-to-sku-uuids', 'windows:5ad726ea-af0f-46b1-abd8-e9a9bbd33155;ps5:fbefd98b-ab71-4ac2-8590-c7d399769fbb;xbsx:9c8b098b-faa8-4dc8-817b-bd73e1306f5a',
    ]*/

    private static final List EXTRA_ARGS_SHIFT_CH1_CODE_DEV_PERF = EXTRA_ARGS_SHIFT_BASE + [
        '--shift-client-platforms-to-sku-uuids', 'windows:119d290e-3ecc-4798-bb65-c113d601412e;ps5:b9a3e72e-1148-443c-bcbb-2f170110f3aa;xbsx:114eb996-7b61-456d-9cbe-3bed1bdb177c',
    ]

    // TODO fill in the sku-uuids later when we have a job on ch1-content-dev to run with shift deployment
    private static final List EXTRA_ARGS_SHIFT_CH1_CONTENT_DEV = EXTRA_ARGS_SHIFT_BASE + [
        '--shift-server-platforms-to-sku-uuids', 'windows:e5a8b355-41dd-4c4a-827b-56f892917160',
        '--shift-client-platforms-to-sku-uuids', 'windows:5b361a76-f69b-4c19-b6cc-a46d1db23e06;ps5:5f82776c-648f-483b-8f93-c6de3703b49a;xbsx:6b609f15-0013-407e-a278-389ec7bb9f74',
    ]

    private static final List EXTRA_ARGS_SHIFT_CH1_STAGE = EXTRA_ARGS_SHIFT_BASE + [
        '--shift-server-platforms-to-sku-uuids', 'windows:14a453b5-0019-40dc-b83a-b0fff15b2eaf',
        '--shift-client-platforms-to-sku-uuids', 'windows:a3f300e5-75ee-459f-8386-5ca418b5dce0;ps5:17695d17-ef26-48a6-9865-707d145b74dd;xbsx:c4873883-7e4b-46b0-b791-0404106493ae',
    ]

    private static final List EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING = [
        '--use-remote-file-share-handling', true,
        '--use-force-file-share-cleanup', true,
    ]

    private static final List EXTRA_ARGS_TEMP = [
        '--atf-endpoint-list', 'windows:re-atf.la.ad.ea.com;xbsx:re-atf.la.ad.ea.com;ps5:re-atf.la.ad.ea.com;linux:re-atf.la.ad.ea.com',
    ]

    private static final List EXTRA_ARGS_CRITERION = [
        '--atf-endpoint-list', 'windows:dice-qve-atf-01;xb1:dice-qve-atf-01;ps4:dice-qve-atf-01;xbsx:dice-qve-atf-01;ps5:dice-qve-atf-01;linux:dice-qve-atf-01',
    ]

    private static final List<Platform> BCT_PLATFORMS = [
        new Platform(name: Name.PS5),
        new Platform(name: Name.WIN64),
        new Platform(name: Name.XBSX),
    ]

    private static final List<Platform> REGIONAL_BCT_PLATFORMS = [
        new Platform(name: Name.PS5, region: Region.DEV),
        new Platform(name: Name.WIN64, region: Region.WW),
        new Platform(name: Name.XBSX, region: Region.WW),
    ]

    private static final List<Platform> REGIONAL_BCT_PLATFORMS_CONSOLE_ONLY = [
        new Platform(name: Name.PS5, region: Region.DEV),
        new Platform(name: Name.XBSX, region: Region.WW),
    ]

    private final Map bctSlackSettings = [
        channels                  : ['#bf-sqr-bct-notify'],
        skip_for_multiple_failures: false,
    ]

    /* ********** ********** ********** ********** **********
    Define sets of tests to run for different types of autotests.
    Add more setups here if needed.
    */

    private final TestInfo lkg_auto = new TestInfo(
        parallelLimit: 6,
        hasPinnedAgents: true,
        runLevelsInParallel: true,
        customTestSuiteData: 'show_result_in_nicesyncer:true',
        testGroup: 'lkg_auto',
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 6,
        testNames: ['LKG_CoreCombat', 'LKG_MMT', 'LKG_F2P', 'LKG_SP'],
        extraArgs: EXTRA_ARGS_V1,
        platforms: BCT_PLATFORMS,
    )

    private final TestInfo lkg_auto_ScratchDisk = new TestInfo(
        parallelLimit: 6,
        hasPinnedAgents: true,
        runLevelsInParallel: true,
        customTestSuiteData: 'show_result_in_nicesyncer:true',
        testGroup: 'lkg_auto',
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 6,
        testNames: ['LKG_CoreCombat', 'LKG_MMT', 'LKG_F2P', 'LKG_SP'],
        extraArgs: [EXTRA_ARGS_V1,'--default-log-collection-strategies', 'ScratchDisk;Ensemble'].flatten(),
        platforms: BCT_PLATFORMS,
    )

    private final TestInfo lkg_auto_ECS = new TestInfo(
        parallelLimit: 3,
        runLevelsInParallel: true,
        customTestSuiteData: 'show_result_in_nicesyncer:true',
        testGroup: 'lkg_auto',
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 6,
        testNames: ['LKG_CoreCombat', 'LKG_MMT', 'LKG_F2P', 'LKG_SP'],
        extraArgs: EXTRA_ARGS_V1,
        platforms: BCT_PLATFORMS,
    )

    private final TestInfo lkg_auto_release = new TestInfo(
        parallelLimit: 6,
        hasPinnedAgents: true,
        runLevelsInParallel: true,
        customTestSuiteData: 'show_result_in_nicesyncer:true',
        testGroup: 'lkg_auto',
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 6,
        testNames: ['LKG_CoreCombat', 'LKG_MMT', 'LKG_F2P'],
        extraArgs: EXTRA_ARGS_V1,
        platforms: BCT_PLATFORMS,
    )

    private final TestInfo lkg_auto_la_test = new TestInfo(
        parallelLimit: 3,
        hasPinnedAgents: true,
        runLevelsInParallel: true,
        customTestSuiteData: 'show_result_in_nicesyncer:true',
        testGroup: 'lkg_auto',
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 6,
        testNames: ['LKG_CoreCombat', 'LKG_MMT', 'LKG_F2P', 'LKG_SP'],
        extraArgs: EXTRA_ARGS_V1,
        platforms: BCT_PLATFORMS,
    )

    private final TestInfo lkg_bootanddeploy = new TestInfo(
        parallelLimit: 3,
        hasPinnedAgents: true,
        runLevelsInParallel: true,
        customTestSuiteData: 'show_result_in_nicesyncer:true',
        testGroup: 'lkg_bootanddeploy',
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 6,
        testNames: ['LKG_BootAndDeploy'],
        extraArgs: EXTRA_ARGS_V1,
        platforms: REGIONAL_BCT_PLATFORMS,
    )

    private final TestInfo lkg_bootanddeploy_ScratchDisk = new TestInfo(
        parallelLimit: 3,
        hasPinnedAgents: true,
        runLevelsInParallel: true,
        customTestSuiteData: 'show_result_in_nicesyncer:true',
        testGroup: 'lkg_bootanddeploy',
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 6,
        testNames: ['LKG_BootAndDeploy'],
        extraArgs: [EXTRA_ARGS_V1,'--default-log-collection-strategies', 'ScratchDisk;Ensemble'].flatten(),
        platforms: REGIONAL_BCT_PLATFORMS,
    )

    private final TestInfo lkg_bootanddeploy_tool = new TestInfo(
        parallelLimit: 3,
        hasPinnedAgents: true,
        runLevelsInParallel: true,
        customTestSuiteData: 'show_result_in_nicesyncer:true',
        testGroup: 'lkg_bootanddeploy',
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 7,
        testNames: ['LKG_BootAndDeploy'],
        extraArgs: EXTRA_ARGS_V1,
        platforms: [
            new Platform(name: Name.TOOL, region: Region.WW)
        ],
    )
/*
    private final TestInfo lkg_bootanddeploy_tool_trunk = new TestInfo(
        parallelLimit: 3,
        hasPinnedAgents: true,
        runLevelsInParallel: true,
        customTestSuiteData: 'show_result_in_nicesyncer:true',
        testGroup: 'lkg_bootanddeploy',
        slackChannel: '#bf-sqr-bct-notify',
        remoteLabel: 'eala',
        timeoutHours: 5,
        testNames: ['LKG_BootAndDeploy'],
        extraArgs: EXTRA_ARGS_V1,
        platforms: [
            new Platform(name: Name.TOOL, region: Region.WW)
        ],
    )*/

    private final TestInfo lkg_audiotestsuite = new TestInfo(
        parallelLimit: 1,
        runLevelsInParallel: true,
        customTestSuiteData: 'show_result_in_nicesyncer:true',
        testGroup: 'lkg_audio',
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 2,
        testNames: ['LKG_AudioTestSuite'],
        extraArgs: EXTRA_ARGS_V1,
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW)
        ],
    )

    private final TestInfo lkg_qv_ch1 = new TestInfo(
        parallelLimit: 3,
        runLevelsInParallel: true,
        testGroup: 'lkg_qv',
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 9,
        testNames: ['LKG_QV', 'LKG_AutoPlayers'],
        extraArgs: EXTRA_ARGS_V1,
        platforms: BCT_PLATFORMS,
    )

    private final TestInfo lkg_qv_la = new TestInfo(
        parallelLimit: 3,
        runLevelsInParallel: true,
        remoteLabel: 'eala',
        testGroup: 'lkg_qv',
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 9,
        testNames: ['LKG_QV', 'LKG_AutoPlayers'],
        extraArgs: EXTRA_ARGS_V1,
        platforms: BCT_PLATFORMS,
    )

    private final TestInfo lkg_checkmate_la = new TestInfo(
        platforms: [
            new Platform(name: Name.WIN64),
        ],
        parallelLimit: 1,
        runLevelsInParallel: true,
        testGroup: 'lkgcheckmate',
        slackChannel: '#bf-sqr-bct-notify',
        customTestSuiteData: 'show_result_in_nicesyncer:true',
        timeoutHours: 3,
        tests: [
            new TestSuite(name: 'CheckMate_Mandatory_TestSuite', extraArgs: [EXTRA_ARGS_V1, EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING].flatten(), needGameServer: false),
        ]
    )

    private final TestInfo lkg_checkmate_pinned = new TestInfo(
        platforms: [
            new Platform(name: Name.WIN64),
        ],
        parallelLimit: 2,
        hasPinnedAgents: true,
        runLevelsInParallel: true,
        customTestSuiteData: 'show_result_in_nicesyncer:true',
        testGroup: 'lkgcheckmate',
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 4,
        numTestRuns: 3,
        tests: [
            new TestSuite(name: 'CheckMate_Mandatory_TestSuite', extraArgs: [EXTRA_ARGS_V1, EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING].flatten(), needGameServer: false)
        ]
    )

    private final TestInfo lkg_checkmate = new TestInfo(
        platforms: [
            new Platform(name: Name.WIN64),
        ],
        parallelLimit: 1,
        runLevelsInParallel: true,
        testGroup: 'lkgcheckmate',
        slackChannel: '#bf-sqr-bct-notify',
        customTestSuiteData: 'show_result_in_nicesyncer:true',
        timeoutHours: 3,
        tests: [
            new TestSuite(name: 'CheckMate_Mandatory_TestSuite', extraArgs: [EXTRA_ARGS_V1, EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING].flatten(), needGameServer: false),
        ]
    )

    private final TestInfo pt_func_playtest = new TestInfo(
        platforms: REGIONAL_BCT_PLATFORMS,
        parallelLimit: 3,
        runLevelsInParallel: true,
        testGroup: 'pt_func_playtest',
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 9,
        tests: [
            new TestSuite(name: 'Systemic_PlaytestValidation', extraArgs: EXTRA_ARGS_V2),
        ]
    )

    private final TestInfo pt_crossplay = new TestInfo(
        timeoutHours: 6,
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW)
        ],
        parallelLimit: 1,
        clientPlatforms: ['ps5', 'xbsx', 'win64'],
        testGroup: 'pt_func_playtest',
        tests: [
            new TestSuite(
                name: 'Playtestability_Crossplay',
                timeoutHours: 6,
                extraArgs: [EXTRA_ARGS_SHIFT_CH1_CONTENT_DEV, EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING, EXTRA_ARGS_CRITERION, '--default-server-platform', 'windows', '--default-server-acquisition-strategy', 'atfrest', '--default-server-target-group-overrides', 'dedicated_server_pool', '--test-state-change-timeout', 240, '--trace-local', true, '--default-log-collection-strategies', 'ScratchDisk'].flatten(),
            ),
        ]
    )

    private final TestInfo pt_crossplay_spin = new TestInfo(
        timeoutHours: 11,
        platforms: [
            new Platform(name: Name.LINUX64, region: Region.WW)
        ],
        parallelLimit: 1,
        clientPlatforms: ['ps5', 'xbsx', 'win64'],
        testGroup: 'pt_func_playtest',
        tests: [
            new TestSuite(
                name: 'Playtestability_Crossplay',
                timeoutHours: 6,
                extraArgs: [EXTRA_ARGS_SPIN_SERVER, EXTRA_ARGS_SHIFT_CH1_CONTENT_DEV, EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING, EXTRA_ARGS_CRITERION, '--default-server-platform', 'linux', '--default-server-acquisition-strategy', 'spin', '--test-state-change-timeout', 240, '--trace-local', true].flatten(),
            ),
        ]
    )

    private final TestInfo frostedTestsCH1ContentDev = new TestInfo(
        testGroup: 'frostedtests',
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 6,
        platforms: [new Platform(name: Name.WIN64)],
        testNames: [
            'FrostEdTest_BFWorkflowsTechArt',
        ],
        extraArgs: ['--frosted-runtime-args', '"-includeTestDataModules true -QV.Enable false"', '--frosted-launch-timeout', 600, '--frosted-test-timeout', 480, '--alltests', '--databaseId', "BattlefieldGameData.${CH1_CONTENT_DEV}.Win32.Debug"],
        poolType: ''
    )

    private final TestInfo frostedTestsECS = new TestInfo(
        testGroup: 'frostedtests',
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 6,
        platforms: [new Platform(name: Name.WIN64)],
        testNames: [
            'FrostEdTest_BFWorkflowsTechArt',
        ],
        extraArgs: ['--frosted-runtime-args', '"-includeTestDataModules true -QV.Enable false"', '--frosted-launch-timeout', 600, '--frosted-test-timeout', 480, '--alltests', '--databaseId', "BattlefieldGameData.${ECS_SPLINES}.Win32.Debug"],
        poolType: '',
    )

    private final TestInfo frostedTestsCH1CODEDEVExtended = new TestInfo(
        testGroup: 'frostedtests_extended',
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 6,
        platforms: [new Platform(name: Name.WIN64)],
        testNames: ['frostedtest_EcsOrbitalPerformance'],
        extraArgs: ['--frosted-runtime-args', '"-includeTestDataModules true -QV.Enable false"', '--frosted-launch-timeout', 600, '--frosted-test-timeout', 3000, '--frosted-test-timeout', 3000, '--frosted-telemetry-threshold-ms', 1, '--frosted-test-additional-options', '"ForwardFrostEdPerfDataToReport:true"', '--alltests', '--ensemble-grpc', 'true', '--databaseId', "BattlefieldGameData.${CH1_CODE_DEV}.Win32.Debug"],
        poolType: ''
    )

    private final TestInfo frostedTestsECSSplines = new TestInfo(
        testGroup: 'frostedtests_extended',
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 6,
        platforms: [new Platform(name: Name.WIN64)],
        tests: [
            new TestSuite(
                name: 'frostedtest_EcsOrbitalPerformance',
                extraArgs: ['--frosted-runtime-args', '"-includeTestDataModules true -QV.Enable false"', '--frosted-launch-timeout', 600, '--frosted-test-timeout', 3000, '--frosted-telemetry-threshold-ms', 1, '--frosted-test-additional-options', '"ForwardFrostEdPerfDataToReport:true"', '--alltests', '--ensemble-grpc', 'true', '--databaseId', "BattlefieldGameData.${ECS_SPLINES}.Win32.Debug"],
                poolType: ''
            ),
        ]
    )

/*
    private final TestInfo pt_perf_highpriority_UltSpec_final_setup = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 1,
        runLevelsInParallel: true,
        timeoutHours: 8,
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW)
        ],
        testGroup: 'pt_perf',
        testNames: ['MP_Perf_Flythrough_Seasons_Detailed_UltSpec'],
        extraArgs: [EXTRA_ARGS_V3, '--override-server-build-configuration', 'final', EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING].flatten(),
        needGameServer: true,
    )*/

    private final TestInfo pt_perf_recspec_high_performance_setup = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 1,
        runLevelsInParallel: true,
        timeoutHours: 4,
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW)
        ],
        testGroup: 'pt_perf',
        testNames: ['MP_Perf_Flythrough_RecSpec'],
        extraArgs: [EXTRA_ARGS_V3, '--override-server-build-configuration', 'final', EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING].flatten(),
        needGameServer: true,
    )

    private final TestInfo pt_perf_recspec_meta_performance_setup = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 1,
        runLevelsInParallel: true,
        timeoutHours: 4,
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW)
        ],
        testGroup: 'pt_perf',
        testNames: ['Meta_Perf_FrontEnd_RecSpec'],
        extraArgs: [EXTRA_ARGS_V3, '--override-server-build-configuration', 'final', '--max-parallel-tests', 1, EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING].flatten(),
        needGameServer: true,
    )

    private final TestInfo pt_perf_performance_meta_setup_trinity = new TestInfo(
        parallelLimit: 2,
        runLevelsInParallel: true,
        slackChannel: '#bf-sqr-bct-notify',
        remoteLabel: 'eala',
        timeoutHours: 10,
        platforms: [
            new Platform(name: Name.PS5, region: Region.DEV),
        ],
        testGroup: 'pt_perf',
        testNames: [
            'Meta_Perf_FrontEnd_Trinity',
        ],
        extraArgs: [EXTRA_ARGS_V3, '--override-server-build-configuration', 'final', EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING].flatten(),
        needGameServer: true,
    )

    private final TestInfo pt_perf_performance_setup_trinity = new TestInfo(
        parallelLimit: 2,
        runLevelsInParallel: true,
        slackChannel: '#bf-sqr-bct-notify',
        remoteLabel: 'eala',
        timeoutHours: 10,
        platforms: [
            new Platform(name: Name.PS5, region: Region.DEV),
        ],
        testGroup: 'pt_perf',
        testNames: [
            'MP_Perf_Gameplay_Trinity',
        ],
        extraArgs: [EXTRA_ARGS_V3, '--override-server-build-configuration', 'final', EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING].flatten(),
        needGameServer: true,
    )

    private final TestInfo pt_perf_performance_setup_trinity_two = new TestInfo(
        parallelLimit: 2,
        runLevelsInParallel: true,
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 14,
        platforms: [
            new Platform(name: Name.PS5, region: Region.DEV),
        ],
        testGroup: 'pt_perf',
        testNames: [
            'F2P_Perf_Gameplay_Trinity',
            'F2P_Perf_Portal_Trinity',
            'F2P_Perf_Gameplay_GauntletOnLargest_Trinity',
        ],
        extraArgs: [EXTRA_ARGS_V3, '--override-server-build-configuration', 'final', EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING].flatten(),
        needGameServer: true,
    )

    private final TestInfo pt_perf_performance_setup_trinity_release = new TestInfo(
        parallelLimit: 1,
        runLevelsInParallel: true,
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 11,
        platforms: [
            new Platform(name: Name.PS5, region: Region.DEV),
        ],
        testGroup: 'pt_perf',
        testNames: ['F2P_Perf_Gameplay_Trinity',],
        extraArgs: [EXTRA_ARGS_V3, '--override-server-build-configuration', 'final', EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING, '--max-parallel-tests', 2].flatten(),
        needGameServer: true,
    )

    private final TestInfo pt_perf_performance_setup_trinity_stage_AlphaValidation_Trinity_BFLabs = new TestInfo(
        parallelLimit: 1,
        runLevelsInParallel: true,
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 13,
        remoteLabel: 'eala',
        platforms: [
            new Platform(name: Name.PS5, region: Region.DEV),
        ],
        testGroup: 'pt_perf',
        testNames: ['MP_Perf_Gameplay_AlphaValidation_Trinity_BFLabs'],
        extraArgs: [EXTRA_ARGS_V3, '--override-server-build-configuration', 'final', EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING].flatten(),
        needGameServer: true,
    )

    private final TestInfo pt_perf_performance_setup_trinity_release_AlphaValidation_Trinity_BFLabs = new TestInfo(
        parallelLimit: 1,
        runLevelsInParallel: true,
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 13,
        remoteLabel: 'eala',
        platforms: [
            new Platform(name: Name.PS5, region: Region.DEV),
        ],
        testGroup: 'pt_perf',
        testNames: ['MP_Perf_Gameplay_AlphaValidation_Trinity_BFLabs'],
        extraArgs: [EXTRA_ARGS_V3, '--override-server-build-configuration', 'final', EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING].flatten(),
        needGameServer: true,
    )
/*
    private final TestInfo pt_perf_performance_setup_trinity_stage_AlphaValidation_Trinity_Coverage = new TestInfo(
        parallelLimit: 1,
        runLevelsInParallel: true,
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 13,
        platforms: [
            new Platform(name: Name.PS5, region: Region.DEV),
        ],
        testGroup: 'pt_perf',
        testNames: ['MP_Perf_Gameplay_AlphaValidation_Trinity_Coverage'],
        extraArgs: [EXTRA_ARGS_V3, '--override-server-build-configuration', 'final', EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING].flatten(),
        needGameServer: true,
    )

    private final TestInfo pt_perf_performance_setup_trinity_stage_AlphaValidation_Trinity_HighPiro = new TestInfo(
        parallelLimit: 1,
        runLevelsInParallel: true,
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 13,
        platforms: [
            new Platform(name: Name.PS5, region: Region.DEV),
        ],
        testGroup: 'pt_perf',
        testNames: ['MP_Perf_Gameplay_AlphaValidation_Trinity_HighPrio'],
        extraArgs: [EXTRA_ARGS_V3, '--override-server-build-configuration', 'final', EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING].flatten(),
        needGameServer: true,
    )

    private final TestInfo pt_perf_performance_setup_trinity_release_AlphaValidation_Trinity_HighPiro = new TestInfo(
        parallelLimit: 1,
        runLevelsInParallel: true,
        slackChannel: '#bf-sqr-bct-notify',
        remoteLabel: 'eala',
        timeoutHours: 13,
        platforms: [
            new Platform(name: Name.PS5, region: Region.DEV),
        ],
        testGroup: 'pt_perf',
        testNames: ['MP_Perf_Gameplay_AlphaValidation_Trinity_HighPrio:AutoPlayTest_MP_Aftermath_Conquest0_Fidelity:AutoPlayTest_MP_Badlands_Conquest0_Fidelity:AutoPlayTest_MP_Dumbo_Conquest0_Fidelity:AutoPlayTest_MP_Firestorm_Conquest0_Fidelity:AutoPlayTest_MP_Outskirts_Conquest0_Fidelity:AutoPlayTest_MP_Tungsten_Conquest0_Fidelity:AutoPlayTest_MP_Aftermath_ Conquest0_Performance:AutoPlayTest_MP_Badlands_Conquest0_ Performance:AutoPlayTest_MP_Dumbo_Conquest0_ Performance:AutoPlayTest_MP_Firestorm_ Conquest0_Performance:AutoPlayTest_MP_Outskirts_ Conquest0_Performance:AutoPlayTest_MP_Tungsten_ Conquest0_Performance'],
        extraArgs: [EXTRA_ARGS_V3, '--override-server-build-configuration', 'final', EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING].flatten(),
        needGameServer: true,
    )*/

    private final TestInfo pt_perf_performance_setup_trinity_detailed = new TestInfo(
        parallelLimit: 1,
        runLevelsInParallel: true,
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 6,
        platforms: [
            new Platform(name: Name.PS5, region: Region.DEV),
        ],
        testGroup: 'pt_perf',
        testNames: ['F2P_Perf_Portal_Trinity_Detailed'],
        extraArgs: [EXTRA_ARGS_V3, '--override-server-build-configuration', 'final', EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING].flatten(),
        needGameServer: true,
    )

    private final TestInfo ch1_asan_windows = new TestInfo(
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW),
        ],
        parallelLimit: 2,
        runLevelsInParallel: true,
        testGroup: 'featuretests',
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 8,
        disableAutomaticRetry: true, // the jobs sometimes stall because of Icepick issues, just move on for now...
        testNames: ['CheckMate_Mandatory_TestSuite(sanitizer=ASAN)', 'LKG_AutoPlayers(sanitizer=ASAN)', 'LKG_CoreCombat(sanitizer=ASAN)', 'LKG_F2P(sanitizer=ASAN)', 'LKG_MMT(sanitizer=ASAN)', 'LKG_SP(sanitizer=ASAN)',],
        extraArgs: EXTRA_ARGS_SANITIZERS,
    )

    private final TestInfo ch1_asan_consoles = new TestInfo(
        platforms: REGIONAL_BCT_PLATFORMS_CONSOLE_ONLY,
        parallelLimit: 2,
        runLevelsInParallel: true,
        testGroup: 'featuretests',
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 8,
        disableAutomaticRetry: true, // the jobs sometimes stall because of Icepick issues, just move on for now...
        tests: [
            new TestSuite(name: 'CheckMate_Mandatory_TestSuite(sanitizer=ASAN)',
                platforms: [new Platform(name: Name.XBSX)],
                extraArgs: EXTRA_ARGS_SANITIZERS),
            new TestSuite(name: 'CheckMate_Mandatory_TestSuite(sanitizer=ASAN)',
                platforms: [new Platform(name: Name.PS5)],
                extraArgs: EXTRA_ARGS_SANITIZERS_PS5),
            new TestSuite(name: 'LKG_AutoPlayers(sanitizer=ASAN)',
                platforms: [new Platform(name: Name.XBSX)],
                extraArgs: EXTRA_ARGS_SANITIZERS),
            new TestSuite(name: 'LKG_AutoPlayers(sanitizer=ASAN)',
                platforms: [new Platform(name: Name.PS5)],
                extraArgs: EXTRA_ARGS_SANITIZERS_PS5),
            new TestSuite(name: 'LKG_CoreCombat(sanitizer=ASAN)',
                platforms: [new Platform(name: Name.XBSX)],
                extraArgs: EXTRA_ARGS_SANITIZERS),
            new TestSuite(name: 'LKG_CoreCombat(sanitizer=ASAN)',
                platforms: [new Platform(name: Name.PS5)],
                extraArgs: EXTRA_ARGS_SANITIZERS_PS5),
            new TestSuite(name: 'LKG_F2P(sanitizer=ASAN)',
                platforms: [new Platform(name: Name.XBSX)],
                extraArgs: EXTRA_ARGS_SANITIZERS),
            new TestSuite(name: 'LKG_F2P(sanitizer=ASAN)',
                platforms: [new Platform(name: Name.PS5)],
                extraArgs: EXTRA_ARGS_SANITIZERS_PS5),
            new TestSuite(name: 'LKG_MMT(sanitizer=ASAN)',
                platforms: [new Platform(name: Name.XBSX)],
                extraArgs: EXTRA_ARGS_SANITIZERS),
            new TestSuite(name: 'LKG_MMT(sanitizer=ASAN)',
                platforms: [new Platform(name: Name.PS5)],
                extraArgs: EXTRA_ARGS_SANITIZERS_PS5),
            new TestSuite(name: 'LKG_SP(sanitizer=ASAN)',
                platforms: [new Platform(name: Name.XBSX)],
                extraArgs: EXTRA_ARGS_SANITIZERS),
            new TestSuite(name: 'LKG_SP(sanitizer=ASAN)',
                platforms: [new Platform(name: Name.PS5)],
                extraArgs: EXTRA_ARGS_SANITIZERS_PS5),
        ]
    )

    private final TestInfo ch1_ubsan_consoles = new TestInfo(
        platforms: [
            new Platform(name: Name.PS5, region: Region.DEV),
        ],
        parallelLimit: 1,
        runLevelsInParallel: true,
        testGroup: 'featuretests',
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 8,
        disableAutomaticRetry: true, // the jobs sometimes stall because of Icepick issues, just move on for now...
        tests: [
            new TestSuite(name: 'CheckMate_Mandatory_TestSuite(sanitizer=UBSAN)',
                platforms: [new Platform(name: Name.PS5)],
                extraArgs: EXTRA_ARGS_SANITIZERS_PS5),
            new TestSuite(name: 'LKG_AutoPlayers(sanitizer=UBSAN)',
                platforms: [new Platform(name: Name.PS5)],
                extraArgs: EXTRA_ARGS_SANITIZERS_PS5),
            new TestSuite(name: 'LKG_CoreCombat(sanitizer=UBSAN)',
                platforms: [new Platform(name: Name.PS5)],
                extraArgs: EXTRA_ARGS_SANITIZERS_PS5),
            new TestSuite(name: 'LKG_F2P(sanitizer=UBSAN)',
                platforms: [new Platform(name: Name.PS5)],
                extraArgs: EXTRA_ARGS_SANITIZERS_PS5),
            new TestSuite(name: 'LKG_MMT(sanitizer=UBSAN)',
                platforms: [new Platform(name: Name.PS5)],
                extraArgs: EXTRA_ARGS_SANITIZERS_PS5),
            new TestSuite(name: 'LKG_SP(sanitizer=UBSAN)',
                platforms: [new Platform(name: Name.PS5)],
                extraArgs: EXTRA_ARGS_SANITIZERS_PS5),
        ]
    )

    private final TestInfo pt_perf_performance_setup = new TestInfo(
        parallelLimit: 2,
        runLevelsInParallel: true,
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 12,
        platforms: REGIONAL_BCT_PLATFORMS_CONSOLE_ONLY,
        testGroup: 'pt_perf',
        testNames: ['Common_Perf_Features', 'SP_Perf_Flythrough', 'MP_Perf_Flythrough'],
        extraArgs: [EXTRA_ARGS_V3, '--xbapp-deployment-print-summary', 'true', '--test-state-change-timeout', 120, '--xbapp-deployment-timeout', 7200, '--override-server-build-configuration', 'final'].flatten(),
        needGameServer: true,
    )

    private final TestInfo pt_perf_meta_performance_setup = new TestInfo(
        parallelLimit: 2,
        runLevelsInParallel: true,
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 12,
        platforms: REGIONAL_BCT_PLATFORMS_CONSOLE_ONLY,
        testGroup: 'pt_perf',
        testNames: ['Meta_Perf_FrontEnd'],
        extraArgs: [EXTRA_ARGS_V3, '--xbapp-deployment-print-summary', 'true', '--test-state-change-timeout', 120, '--xbapp-deployment-timeout', 7200, '--override-server-build-configuration', 'final'].flatten(),
        needGameServer: true,
    )

    private final TestInfo pt_perf_highpriority_final_setup = new TestInfo(
        parallelLimit: 3,
        runLevelsInParallel: true,
        slackChannel: '#bf-sqr-bct-notify',
        testGroup: 'pt_perf_highprio',
        timeoutHours: 8,
        platforms: REGIONAL_BCT_PLATFORMS,
        testNames: ['F2P_Perf_Flythrough_Detailed', 'MP_Perf_Flythrough_Detailed', 'SP_Perf_Flythrough_Detailed',],
        extraArgs: [EXTRA_ARGS_V3, '--xbapp-deployment-print-summary', 'true', '--test-state-change-timeout', 120, '--xbapp-deployment-timeout', 7200, '--override-server-build-configuration', 'final'].flatten(),
        needGameServer: true,
    )

    private final TestInfo pt_perf_highpriority_performance_setup_win = new TestInfo(
        parallelLimit: 1,
        runLevelsInParallel: true,
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 8,
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW),
        ],
        testGroup: 'pt_perf',
        testNames: ['Common_Perf_Features', 'SP_Perf_Flythrough', 'MP_Perf_Flythrough'],
        extraArgs: [EXTRA_ARGS_V3, '--xbapp-deployment-print-summary', 'true', '--test-state-change-timeout', 120, '--xbapp-deployment-timeout', 7200, '--override-server-build-configuration', 'final'].flatten(),
        needGameServer: true,
    )

    private final TestInfo pt_perf_highpriority_performance_setup = new TestInfo(
        parallelLimit: 2,
        runLevelsInParallel: true,
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 10,
        platforms: REGIONAL_BCT_PLATFORMS_CONSOLE_ONLY,
        testGroup: 'pt_perf_highprio',
        testNames: [
            'F2P_Perf_Gameplay_GauntletComparingLayouts',
            'F2P_Perf_Gameplay_GauntletOnLargest',
        ],
        extraArgs: [EXTRA_ARGS_V3, '--xbapp-deployment-print-summary', 'true', '--test-state-change-timeout', 120, '--xbapp-deployment-timeout', 7200, '--override-server-build-configuration', 'final'].flatten(),
        needGameServer: true,
    )

    private final TestInfo pt_perf_highpriority_performance_setup_two = new TestInfo(
        parallelLimit: 4,
        runLevelsInParallel: true,
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 12,
        platforms: REGIONAL_BCT_PLATFORMS_CONSOLE_ONLY,
        testGroup: 'pt_perf_highprio',
        testNames: [
            'MP_Perf_Gameplay',
            'F2P_Perf_Gameplay',
            'F2P_Perf_Flythrough',
        ],
        extraArgs: [EXTRA_ARGS_V3, '--xbapp-deployment-print-summary', 'true', '--test-state-change-timeout', 120, '--xbapp-deployment-timeout', 7200, '--override-server-build-configuration', 'final'].flatten(),
        needGameServer: true,
    )

    private final TestInfo pt_perf_highpriority_performance_setup_spin_server = new TestInfo(
        parallelLimit: 1,
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 12,
        platforms: [
            new Platform(name: Name.XBSX, region: Region.WW),
        ],
        testGroup: 'pt_perf_highprio',
        testNames: [
            'MP_Perf_Gameplay',
        ],
        extraArgs: [EXTRA_ARGS_SPIN_SERVER, EXTRA_ARGS_SHIFT_CH1_CODE_DEV_PERF, '--journal-server', 'localhost', '--server-runtime-debug-args', '"-tracelocal -Core.EnsembleLoggingEnabled false"', EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING, EXTRA_ARGS_CRITERION, '--test-state-change-timeout', 240].flatten(),
    )

    private final TestInfo pt_perf_highpriority_performance_setup_ch1_release = new TestInfo(
        parallelLimit: 3,
        runLevelsInParallel: true,
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 12,
        platforms: REGIONAL_BCT_PLATFORMS,
        testGroup: 'pt_perf_highprio',
        testNames: [
            'F2P_Perf_Gameplay',
        ],
        extraArgs: [EXTRA_ARGS_V3, '--xbapp-deployment-print-summary', 'true', '--test-state-change-timeout', 120, '--xbapp-deployment-timeout', 7200, '--override-server-build-configuration', 'final', '--max-parallel-tests', 2].flatten(),
        needGameServer: true,
    )

    private final TestInfo pt_perf_performance_setup_ch1_content_validation = new TestInfo(
        parallelLimit: 2,
        runLevelsInParallel: true,
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 7,
        platforms: REGIONAL_BCT_PLATFORMS_CONSOLE_ONLY,
        testGroup: 'pt_perf_highprio',
        testNames: [
            'MP_Perf_Gameplay:AutoPlayTest_MP_Abbasid_Conquest0_Fidelity',
        ],
        extraArgs: [EXTRA_ARGS_V3, '--xbapp-deployment-print-summary', 'true', '--test-state-change-timeout', 120, '--xbapp-deployment-timeout', 7200, '--override-server-build-configuration', 'final'].flatten(),
        needGameServer: true,
    )

    private final TestInfo pt_perf_final_setup_seasons = new TestInfo(
        parallelLimit: 2,
        runLevelsInParallel: true,
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 7,
        platforms: REGIONAL_BCT_PLATFORMS_CONSOLE_ONLY,
        testGroup: 'pt_perf_highprio',
        testNames: [
            'MP_Perf_Gameplay_Seasons',
        ],
        extraArgs: [EXTRA_ARGS_V3, '--xbapp-deployment-print-summary', 'true', '--test-state-change-timeout', 120, '--xbapp-deployment-timeout', 7200, '--override-server-build-configuration', 'final'].flatten(),
        needGameServer: true,
    )

    private final TestInfo pt_perf_performance_setup_seasons = new TestInfo(
        parallelLimit: 2,
        runLevelsInParallel: true,
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 7,
        platforms: REGIONAL_BCT_PLATFORMS_CONSOLE_ONLY,
        testGroup: 'pt_perf_highprio',
        testNames: [
            'MP_Perf_Gameplay_Seasons',
        ],
        extraArgs: [EXTRA_ARGS_V3, '--xbapp-deployment-print-summary', 'true', '--test-state-change-timeout', 120, '--xbapp-deployment-timeout', 7200, '--override-server-build-configuration', 'final'].flatten(),
        needGameServer: true,
    )

    private final TestInfo pt_perf_final_setup_seasons_MinSpec = new TestInfo(
        parallelLimit: 2,
        runLevelsInParallel: true,
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 7,
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW),
        ],
        testGroup: 'pt_perf_highprio',
        testNames: [
            'MP_Perf_Gameplay_Seasons_MinSpec',
        ],
        extraArgs: [EXTRA_ARGS_V3, '--xbapp-deployment-print-summary', 'true', '--test-state-change-timeout', 120, '--xbapp-deployment-timeout', 7200, '--override-server-build-configuration', 'final'].flatten(),
        needGameServer: true,
    )

    private final TestInfo pt_perf_performance_setup_seasons_MinSpec = new TestInfo(
        parallelLimit: 2,
        runLevelsInParallel: true,
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 7,
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW),
        ],
        testGroup: 'pt_perf_highprio',
        testNames: [
            'MP_Perf_Gameplay_Seasons_MinSpec',
        ],
        extraArgs: [EXTRA_ARGS_V3, '--xbapp-deployment-print-summary', 'true', '--test-state-change-timeout', 120, '--xbapp-deployment-timeout', 7200, '--override-server-build-configuration', 'final'].flatten(),
        needGameServer: true,
    )

    private final TestInfo pt_perf_final_setup_seasons_RecSpec = new TestInfo(
        parallelLimit: 2,
        runLevelsInParallel: true,
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 7,
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW),
        ],
        testGroup: 'pt_perf_highprio',
        testNames: [
            'MP_Perf_Gameplay_Seasons_RecSpec',
        ],
        extraArgs: [EXTRA_ARGS_V3, '--xbapp-deployment-print-summary', 'true', '--test-state-change-timeout', 120, '--xbapp-deployment-timeout', 7200, '--override-server-build-configuration', 'final'].flatten(),
        needGameServer: true,
    )

    private final TestInfo pt_perf_performance_setup_seasons_RecSpec = new TestInfo(
        parallelLimit: 2,
        runLevelsInParallel: true,
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 7,
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW),
        ],
        testGroup: 'pt_perf_highprio',
        testNames: [
            'MP_Perf_Gameplay_Seasons_RecSpec',
        ],
        extraArgs: [EXTRA_ARGS_V3, '--xbapp-deployment-print-summary', 'true', '--test-state-change-timeout', 120, '--xbapp-deployment-timeout', 7200, '--override-server-build-configuration', 'final'].flatten(),
        needGameServer: true,
    )

    private final TestInfo pt_perf_final_setup_seasons_Trinity = new TestInfo(
        parallelLimit: 2,
        runLevelsInParallel: true,
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 7,
        platforms: [
            new Platform(name: Name.PS5, region: Region.DEV),
        ],
        testGroup: 'pt_perf_highprio',
        testNames: [
            'MP_Perf_Gameplay_Seasons_Trinity',
        ],
        extraArgs: [EXTRA_ARGS_V3, '--xbapp-deployment-print-summary', 'true', '--test-state-change-timeout', 120, '--xbapp-deployment-timeout', 7200, '--override-server-build-configuration', 'final'].flatten(),
        needGameServer: true,
    )

    private final TestInfo pt_perf_performance_setup_seasons_Trinity = new TestInfo(
        parallelLimit: 2,
        runLevelsInParallel: true,
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 7,
        platforms: [
            new Platform(name: Name.PS5, region: Region.DEV),
        ],
        testGroup: 'pt_perf_highprio',
        testNames: [
            'MP_Perf_Gameplay_Seasons_Trinity',
        ],
        extraArgs: [EXTRA_ARGS_V3, '--xbapp-deployment-print-summary', 'true', '--test-state-change-timeout', 120, '--xbapp-deployment-timeout', 7200, '--override-server-build-configuration', 'final'].flatten(),
        needGameServer: true,
    )

    private final TestInfo pt_perf_final_setup_seasons_UltSpec = new TestInfo(
        parallelLimit: 2,
        runLevelsInParallel: true,
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 7,
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW),
        ],
        testGroup: 'pt_perf_highprio',
        testNames: [
            'MP_Perf_Gameplay_Seasons_UltSpec',
        ],
        extraArgs: [EXTRA_ARGS_V3, '--xbapp-deployment-print-summary', 'true', '--test-state-change-timeout', 120, '--xbapp-deployment-timeout', 7200, '--override-server-build-configuration', 'final'].flatten(),
        needGameServer: true,
    )

    private final TestInfo pt_perf_performance_setup_seasons_UltSpec = new TestInfo(
        parallelLimit: 2,
        runLevelsInParallel: true,
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 7,
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW),
        ],
        testGroup: 'pt_perf_highprio',
        testNames: [
            'MP_Perf_Gameplay_Seasons_UltSpec',
        ],
        extraArgs: [EXTRA_ARGS_V3, '--xbapp-deployment-print-summary', 'true', '--test-state-change-timeout', 120, '--xbapp-deployment-timeout', 7200, '--override-server-build-configuration', 'final'].flatten(),
        needGameServer: true,
    )

    private final TestInfo pt_perf_final_setup_seasons_XBSS = new TestInfo(
        parallelLimit: 2,
        runLevelsInParallel: true,
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 7,
        platforms: [
            new Platform(name: Name.XBSX, region: Region.WW),
        ],
        testGroup: 'pt_perf_highprio',
        testNames: [
            'MP_Perf_Gameplay_Seasons_XBSS',
        ],
        extraArgs: [EXTRA_ARGS_V3, '--xbapp-deployment-print-summary', 'true', '--test-state-change-timeout', 120, '--xbapp-deployment-timeout', 7200, '--override-server-build-configuration', 'final'].flatten(),
        needGameServer: true,
    )

    private final TestInfo pt_perf_performance_setup_seasons_XBSS = new TestInfo(
        parallelLimit: 2,
        runLevelsInParallel: true,
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 7,
        platforms: [
            new Platform(name: Name.XBSX, region: Region.WW),
        ],
        testGroup: 'pt_perf_highprio',
        testNames: [
            'MP_Perf_Gameplay_Seasons_XBSS',
        ],
        extraArgs: [EXTRA_ARGS_V3, '--xbapp-deployment-print-summary', 'true', '--test-state-change-timeout', 120, '--xbapp-deployment-timeout', 7200, '--override-server-build-configuration', 'final'].flatten(),
        needGameServer: true,
    )
/*
    private final TestInfo pt_perf_highpriority_performance_setup_AlphaValidation_Coverage = new TestInfo(
        parallelLimit: 2,
        runLevelsInParallel: true,
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 15,
        platforms: [
            new Platform(name: Name.PS5, region: Region.DEV),
            new Platform(name: Name.XBSX, region: Region.WW),
        ],
        testGroup: 'pt_perf_highprio',
        testNames: [
            'MP_Perf_Gameplay_AlphaValidation_Coverage',
        ],
        extraArgs: [EXTRA_ARGS_V3, '--xbapp-deployment-print-summary', 'true', '--test-state-change-timeout', 120, '--xbapp-deployment-timeout', 7200, '--override-server-build-configuration', 'final'].flatten(),
        needGameServer: true,
    )

    private final TestInfo pt_perf_highpriority_performance_setup_AlphaValidation_HighPrio = new TestInfo(
        parallelLimit: 2,
        runLevelsInParallel: true,
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 15,
        platforms: [
            new Platform(name: Name.PS5, region: Region.DEV),
            new Platform(name: Name.XBSX, region: Region.WW),
        ],
        testGroup: 'pt_perf_highprio',
        testNames: [
            'MP_Perf_Gameplay_AlphaValidation_HighPrio',
        ],
        extraArgs: [EXTRA_ARGS_V3, '--xbapp-deployment-print-summary', 'true', '--test-state-change-timeout', 120, '--xbapp-deployment-timeout', 7200, '--override-server-build-configuration', 'final'].flatten(),
        needGameServer: true,
    )

    private final TestInfo pt_perf_highpriority_performance_setup_AlphaValidation_HighPrio_ch1_release = new TestInfo(
        parallelLimit: 2,
        runLevelsInParallel: true,
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 15,
        platforms: [
            new Platform(name: Name.PS5, region: Region.DEV),
            new Platform(name: Name.XBSX, region: Region.WW),
        ],
        testGroup: 'pt_perf_highprio',
        testNames: [
            'MP_Perf_Gameplay_AlphaValidation_HighPrio:AutoPlayTest_MP_Aftermath_Conquest0_Fidelity:AutoPlayTest_MP_Badlands_Conquest0_Fidelity:AutoPlayTest_MP_Dumbo_Conquest0_Fidelity:AutoPlayTest_MP_Firestorm_Conquest0_Fidelity:AutoPlayTest_MP_Outskirts_Conquest0_Fidelity:AutoPlayTest_MP_Tungsten_Conquest0_Fidelity:AutoPlayTest_MP_Aftermath_Conquest0_Performance:AutoPlayTest_MP_Badlands_Conquest0_Performance:AutoPlayTest_MP_Dumbo_Conquest0_Performance:AutoPlayTest_MP_Firestorm_Conquest0_Performance:AutoPlayTest_MP_Outskirts_Conquest0_Performance:AutoPlayTest_MP_Tungsten_Conquest0_Performance',
        ],
        extraArgs: [EXTRA_ARGS_V3, '--xbapp-deployment-print-summary', 'true', '--test-state-change-timeout', 120, '--xbapp-deployment-timeout', 7200, '--override-server-build-configuration', 'final'].flatten(),
        needGameServer: true,
    )*/

    private final TestInfo pt_perf_highpriority_performance_setup_AlphaValidation_BFLabs = new TestInfo(
        parallelLimit: 2,
        runLevelsInParallel: true,
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 15,
        platforms: REGIONAL_BCT_PLATFORMS_CONSOLE_ONLY,
        testGroup: 'pt_perf_highprio',
        testNames: [
            'MP_Perf_Gameplay_AlphaValidation_BFLabs',
        ],
        extraArgs: [EXTRA_ARGS_V3, '--xbapp-deployment-print-summary', 'true', '--test-state-change-timeout', 120, '--xbapp-deployment-timeout', 7200, '--override-server-build-configuration', 'final', '--max-parallel-tests', 2].flatten(),
        needGameServer: true,
    )

    private final TestInfo pt_perf_highpriority_performance_setup_AlphaValidation_BFLabs_release = new TestInfo(
        parallelLimit: 2,
        runLevelsInParallel: true,
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 15,
        platforms: REGIONAL_BCT_PLATFORMS_CONSOLE_ONLY,
        testGroup: 'pt_perf_highprio',
        testNames: [
            'MP_Perf_Gameplay_AlphaValidation_BFLabs',
        ],
        extraArgs: [EXTRA_ARGS_V3, '--xbapp-deployment-print-summary', 'true', '--test-state-change-timeout', 120, '--xbapp-deployment-timeout', 7200, '--override-server-build-configuration', 'final', '--max-parallel-tests', 2].flatten(),
        needGameServer: true,
    )

    private final TestInfo pt_perf_f2p_highpriority_performance_setup = new TestInfo(
        parallelLimit: 3,
        runLevelsInParallel: true,
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 7,
        platforms: REGIONAL_BCT_PLATFORMS,
        testGroup: 'pt_perf',
        tests: [
            new TestSuite(name: 'F2P_Perf_Portal', timeoutHours: 3, extraArgs: [EXTRA_ARGS_V3, '--xbapp-deployment-timeout', 7200, '--xbapp-deployment-print-summary', 'true', '--override-server-build-configuration', 'final'].flatten(), needGameServer: true),
        ]
    )

    private final TestInfo pt_perf_f2p_highpriority_performance_setup_detailed = new TestInfo(
        parallelLimit: 3,
        runLevelsInParallel: true,
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 5,
        platforms: REGIONAL_BCT_PLATFORMS,
        testGroup: 'pt_perf',
        tests: [
            new TestSuite(name: 'F2P_Perf_Portal_Detailed', timeoutHours: 3, extraArgs: [EXTRA_ARGS_V3, '--xbapp-deployment-timeout', 7200, '--xbapp-deployment-print-summary', 'true', '--override-server-build-configuration', 'final'].flatten(), needGameServer: true),
        ]
    )

    private final TestInfo pt_perf_highpriority_flythroughs_final_setup = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 2,
        runLevelsInParallel: true,
        timeoutHours: 9,
        platforms: REGIONAL_BCT_PLATFORMS_CONSOLE_ONLY,
        testGroup: 'pt_perf',
        tests: [
            new TestSuite(name: 'MP_Mem_Gameplay', timeoutHours: 3, extraArgs: [EXTRA_ARGS_V3, '--xbapp-deployment-timeout', 7200, '--xbapp-deployment-print-summary'].flatten(), needGameServer: true),
        ]
    )

    private final TestInfo pt_perf_mem_final_ch1_stage = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 3,
        runLevelsInParallel: true,
        timeoutHours: 9,
        platforms: REGIONAL_BCT_PLATFORMS,
        testGroup: 'pt_perf',
        testNames: [
			'MP_Mem_Gameplay:MemoryPlayTest_MP_Abbasid_Breakthrough0:MemoryPlayTest_MP_Abbasid_Conquest0:MemoryPlayTest_MP_Abbasid_Rush0:MemoryPlayTest_MP_Abbasid_SquadDeathMatch0:MemoryPlayTest_MP_Abbasid_KingOfTheHill0:MemoryPlayTest_MP_Abbasid_Domination0',
			'MP_Mem_Gameplay:MemoryPlayTest_MP_Aftermath_Conquest0:MemoryPlayTest_MP_Aftermath_Rush0:MemoryPlayTest_MP_Aftermath_KingOfTheHill0:MemoryPlayTest_MP_Aftermath_Breakthrough0:MemoryPlayTest_MP_Aftermath_Domination0',
			'MP_Mem_Gameplay:MemoryPlayTest_MP_Battery_Rush0:MemoryPlayTest_MP_Battery_SquadDeathMatch0:MemoryPlayTest_MP_Battery_KingOfTheHill0:MemoryPlayTest_MP_Battery_Domination0:MemoryPlayTest_MP_Battery_Conquest0',
			'MP_Mem_Gameplay:MemoryPlayTest_MP_Capstone_Conquest0:MemoryPlayTest_MP_Capstone_Breakthrough0:MemoryPlayTest_MP_Capstone_Rush0:MemoryPlayTest_MP_Capstone_SquadDeathMatch0:MemoryPlayTest_MP_Capstone_KingOfTheHill0:MemoryPlayTest_MP_Capstone_Domination0',
        ],
        extraArgs: [EXTRA_ARGS_V3, '--xbapp-deployment-timeout', 7200, '--xbapp-deployment-print-summary'].flatten(),
        needGameServer: true,
    )

    private final TestInfo pt_perf_mem_final_ch1_release = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 3,
        runLevelsInParallel: true,
        timeoutHours: 9,
        platforms: REGIONAL_BCT_PLATFORMS,
        testGroup: 'pt_perf',
        testNames: [
			'MP_Mem_Gameplay:MemoryPlayTest_MP_Abbasid_Breakthrough0:MemoryPlayTest_MP_Abbasid_Conquest0:MemoryPlayTest_MP_Abbasid_Rush0:MemoryPlayTest_MP_Abbasid_SquadDeathMatch0:MemoryPlayTest_MP_Abbasid_KingOfTheHill0:MemoryPlayTest_MP_Abbasid_Domination0',
			'MP_Mem_Gameplay:MemoryPlayTest_MP_Aftermath_Conquest0:MemoryPlayTest_MP_Aftermath_Rush0:MemoryPlayTest_MP_Aftermath_KingOfTheHill0:MemoryPlayTest_MP_Aftermath_Breakthrough0:MemoryPlayTest_MP_Aftermath_Domination0',
			'MP_Mem_Gameplay:MemoryPlayTest_MP_Battery_Rush0:MemoryPlayTest_MP_Battery_SquadDeathMatch0:MemoryPlayTest_MP_Battery_KingOfTheHill0:MemoryPlayTest_MP_Battery_Domination0:MemoryPlayTest_MP_Battery_Conquest0',
			'MP_Mem_Gameplay:MemoryPlayTest_MP_Capstone_Conquest0:MemoryPlayTest_MP_Capstone_Breakthrough0:MemoryPlayTest_MP_Capstone_Rush0:MemoryPlayTest_MP_Capstone_SquadDeathMatch0:MemoryPlayTest_MP_Capstone_KingOfTheHill0:MemoryPlayTest_MP_Capstone_Domination0',
        ],
        extraArgs: [EXTRA_ARGS_V3, '--xbapp-deployment-timeout', 7200, '--xbapp-deployment-print-summary'].flatten(),
        needGameServer: true,
    )

    private final TestInfo pt_perf_flythroughs_final_implicit_setup = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 2,
        runLevelsInParallel: true,
        timeoutHours: 9,
        platforms: REGIONAL_BCT_PLATFORMS_CONSOLE_ONLY,
        testGroup: 'pt_perf',
        tests: [
            new TestSuite(name: 'SP_Perf_Flythrough:Flythrough_DSUB_SP_Prologue_Zone1', timeoutHours: 3, extraArgs: [EXTRA_ARGS_V3, '--xbapp-deployment-timeout', 7200, '--xbapp-deployment-print-summary'].flatten(), needGameServer: true),
        ]
    )

    private final TestInfo pt_perf_highpriority_flythroughs_final_setup_mem = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 3,
        runLevelsInParallel: true,
        timeoutHours: 4,
        platforms: REGIONAL_BCT_PLATFORMS,
        testGroup: 'pt_perf',
        tests: [
            new TestSuite(name: 'F2P_Mem_Portal', timeoutHours: 3, extraArgs: [EXTRA_ARGS_V3, '--xbapp-deployment-timeout', 7200, '--xbapp-deployment-print-summary'].flatten(), needGameServer: true),
        ]
    )

    private final TestInfo pt_perf_highpriority_flythroughs_final_setup_mem_xbss = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 3,
        runLevelsInParallel: true,
        timeoutHours: 4,
        platforms: [
            new Platform(name: Name.XBSX, region: Region.WW),
        ],
        testGroup: 'pt_perf',
        tests: [
            new TestSuite(name: 'F2P_Mem_Portal_XBSS', timeoutHours: 3, extraArgs: [EXTRA_ARGS_V3, '--xbapp-deployment-timeout', 7200, '--xbapp-deployment-print-summary'].flatten(), needGameServer: true),
        ]
    )

    private final TestInfo pt_perf_HUD = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        remoteLabel: 'eala',
        parallelLimit: 2,
        runLevelsInParallel: true,
        timeoutHours: 9,
        platforms: REGIONAL_BCT_PLATFORMS_CONSOLE_ONLY,
        testGroup: 'pt_perf',
        tests: [
            new TestSuite(name: 'MP_Perf_HUD', timeoutHours: 3, extraArgs: [EXTRA_ARGS_V3, '--xbapp-deployment-timeout', 7200, '--xbapp-deployment-print-summary'].flatten(), needGameServer: true),
        ]
    )

    private final TestInfo pt_perf_HUD_MinSpec = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 1,
        runLevelsInParallel: true,
        timeoutHours: 2,
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW),
        ],
        testGroup: 'pt_perf',
        tests: [
            new TestSuite(name: 'MP_Perf_HUD_MinSpec', timeoutHours: 3, extraArgs: EXTRA_ARGS_V3, needGameServer: true),
        ]
    )

    private final TestInfo pt_perf_HUD_RecSpec = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 1,
        runLevelsInParallel: true,
        timeoutHours: 2,
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW),
        ],
        testGroup: 'pt_perf',
        tests: [
            new TestSuite(name: 'MP_Perf_HUD_RecSpec', timeoutHours: 3, extraArgs: EXTRA_ARGS_V3, needGameServer: true),
        ]
    )

    private final TestInfo pt_perf_HUD_Trinity = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 1,
        runLevelsInParallel: true,
        timeoutHours: 2,
        platforms: [
            new Platform(name: Name.PS5, region: Region.DEV),
        ],
        testGroup: 'pt_perf',
        tests: [
            new TestSuite(name: 'MP_Perf_HUD_Trinity', timeoutHours: 3, extraArgs: EXTRA_ARGS_V3, needGameServer: true),
        ]
    )

/*
    private final TestInfo pt_perf_HUD_UltSpec = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 1,
        runLevelsInParallel: true,
        timeoutHours: 2,
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW),
        ],
        testGroup: 'pt_perf',
        tests: [
            new TestSuite(name: 'MP_Perf_HUD_UltSpec', timeoutHours: 3, extraArgs: EXTRA_ARGS_V3, needGameServer: true),
        ]
    )
*/
    private final TestInfo pt_perf_HUD_XBSS = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 1,
        runLevelsInParallel: true,
        timeoutHours: 2,
        platforms: [
            new Platform(name: Name.XBSX, region: Region.WW),
        ],
        testGroup: 'pt_perf',
        tests: [
            new TestSuite(name: 'MP_Perf_HUD_XBSS', timeoutHours: 3, extraArgs: [EXTRA_ARGS_V3, '--xbapp-deployment-timeout', 7200, '--xbapp-deployment-print-summary'].flatten(), needGameServer: true),
        ]
    )

    private final TestInfo pt_perf_highpriority_flythroughs_final_setup2 = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 2,
        runLevelsInParallel: true,
        timeoutHours: 9,
        platforms: REGIONAL_BCT_PLATFORMS_CONSOLE_ONLY,
        testGroup: 'pt_perf',
        tests: [
            new TestSuite(name: 'F2P_Mem_Gameplay_GauntletComparingLayouts', timeoutHours: 3, extraArgs: [EXTRA_ARGS_V3, '--xbapp-deployment-timeout', 7200, '--xbapp-deployment-print-summary'].flatten(), needGameServer: true),
            new TestSuite(name: 'F2P_Mem_Gameplay', timeoutHours: 3, extraArgs: [EXTRA_ARGS_V3, '--xbapp-deployment-timeout', 7200, '--xbapp-deployment-print-summary'].flatten(), needGameServer: true),
            new TestSuite(name: 'MP_Perf_Flythrough_Detailed:Flythrough_MP_Abbasid:Flythrough_MP_Battery:Flythrough_MP_Capstone:Flythrough_MP_Aftermath', timeoutHours: 3, extraArgs: [EXTRA_ARGS_V3, '--xbapp-deployment-timeout', 7200, '--xbapp-deployment-print-summary'].flatten(), needGameServer: true),
        ]
    )

    private final TestInfo pt_perf_flythroughs_final_setup = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 3,
        runLevelsInParallel: true,
        timeoutHours: 10,
        platforms: REGIONAL_BCT_PLATFORMS,
        testGroup: 'pt_perf',
        testNames: [
            'SP_Mem_Gameplay_SecondBatch',
            'SP_Mem_Gameplay_FirstBatch',
        ],
        extraArgs: [EXTRA_ARGS_V3, '--xbapp-deployment-timeout', 7200, '--xbapp-deployment-print-summary'].flatten(),
        needGameServer: true,
    )

    private final TestInfo pt_perf_flythroughs_final_setup_trinity = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 1,
        runLevelsInParallel: true,
        timeoutHours: 7,
        platforms: [
            new Platform(name: Name.PS5, region: Region.DEV),
        ],
        testGroup: 'pt_perf',
        testNames: ['SP_Perf_Flythrough_Detailed_Trinity', 'MP_Perf_Flythrough_Detailed_Trinity:Flythrough_MP_Abbasid_shared_trinity_testing_pool',],
        extraArgs: [EXTRA_ARGS_V3, '--override-server-build-configuration', 'final'].flatten(),
    )

    private final TestInfo pt_perf_flythroughs_XBSS_performance_setup = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 1,
        runLevelsInParallel: true,
        timeoutHours: 10,
        platforms: [
            new Platform(name: Name.XBSX, region: Region.WW),
        ],
        testGroup: 'pt_perf_highprio',
        testNames: [
            'MP_Perf_Gameplay_XBSS',
            'F2P_Perf_Gameplay_GauntletComparingLayouts_XBSS',
        ],
        extraArgs: [EXTRA_ARGS_V3, '--xbapp-deployment-timeout', 7200, '--xbapp-deployment-print-summary', 'true', '--override-server-build-configuration', 'final'].flatten(),
        needGameServer: true,
    )

    private final TestInfo pt_perf_flythroughs_XBSS_performance_setup_two = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 1,
        runLevelsInParallel: true,
        timeoutHours: 10,
        platforms: [
            new Platform(name: Name.XBSX, region: Region.WW),
        ],
        testGroup: 'pt_perf_highprio',
        testNames: [
            'F2P_Perf_Gameplay_GauntletOnLargest_XBSS',
            'F2P_Perf_Flythrough_XBSS_Detailed',
            'SP_Func_Playthroughs_XBSS',
        ],
        extraArgs: [EXTRA_ARGS_V3, '--xbapp-deployment-timeout', 7200, '--xbapp-deployment-print-summary', 'true', '--override-server-build-configuration', 'final'].flatten(),
        needGameServer: true,
    )
/*
    private final TestInfo pt_perf_flythroughs_XBSS_performance_setup_temp = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 1,
        runLevelsInParallel: true,
        timeoutHours: 10,
        platforms: [
            new Platform(name: Name.XBSX, region: Region.WW),
        ],
        testGroup: 'pt_perf_highprio',
        testNames: [
            'SP_Func_Playthroughs_XBSS',
        ],
        extraArgs: [EXTRA_ARGS_V3, '--xbapp-deployment-timeout', 7200, '--xbapp-deployment-print-summary', 'true', '--override-server-build-configuration', 'final'].flatten(),
        needGameServer: true,
    )
*/
    private final TestInfo pt_perf_XBSS_performance_setup_ch1_release = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 1,
        runLevelsInParallel: true,
        timeoutHours: 8,
        platforms: [
            new Platform(name: Name.XBSX, region: Region.WW),
        ],
        testGroup: 'pt_perf',
        testNames: [
            'F2P_Perf_Gameplay_XBSS',
        ],
        extraArgs: [EXTRA_ARGS_V3, '--xbapp-deployment-timeout', 7200, '--xbapp-deployment-print-summary', 'true', '--override-server-build-configuration', 'final'].flatten(),
        needGameServer: true,
    )
/*
    private final TestInfo pt_perf_flythroughs_XBSS_performance_setup_ch1_stage_AlphaValidation_XBSS_Coverage = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 1,
        runLevelsInParallel: true,
        timeoutHours: 15,
        platforms: [
            new Platform(name: Name.XBSX, region: Region.WW),
        ],
        testGroup: 'pt_perf_highprio',
        testNames: [
            'MP_Perf_Gameplay_AlphaValidation_XBSS_Coverage',
        ],
        extraArgs: [EXTRA_ARGS_V3, '--xbapp-deployment-timeout', 7200, '--xbapp-deployment-print-summary', 'true', '--override-server-build-configuration', 'final'].flatten(),
        needGameServer: true,
    )
/*
    private final TestInfo pt_perf_flythroughs_XBSS_performance_setup_ch1_stage_AlphaValidation_XBSS_HighPrio = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 1,
        runLevelsInParallel: true,
        timeoutHours: 15,
        platforms: [
            new Platform(name: Name.XBSX, region: Region.WW),
        ],
        testGroup: 'pt_perf_highprio',
        testNames: [
            'MP_Perf_Gameplay_AlphaValidation_XBSS_HighPrio',
        ],
        extraArgs: [EXTRA_ARGS_V3, '--xbapp-deployment-timeout', 7200, '--xbapp-deployment-print-summary', 'true', '--override-server-build-configuration', 'final'].flatten(),
        needGameServer: true,
    )

    private final TestInfo pt_perf_flythroughs_XBSS_performance_setup_ch1_release_AlphaValidation_XBSS_HighPrio = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 1,
        runLevelsInParallel: true,
        timeoutHours: 15,
        platforms: [
            new Platform(name: Name.XBSX, region: Region.WW),
        ],
        testGroup: 'pt_perf_highprio',
        testNames: [
            'MP_Perf_Gameplay_AlphaValidation_XBSS_HighPrio:AutoPlayTest_MP_Aftermath_Conquest0:AutoPlayTest_MP_Badlands_Conquest0:AutoPlayTest_MP_Dumbo_Conquest0:AutoPlayTest_MP_Firestorm_Conquest0:AutoPlayTest_MP_Outskirts_Conquest0:AutoPlayTest_MP_Tungsten_Conquest0:AutoPlayTest_MP_Aftermath_Conquest0:AutoPlayTest_MP_Badlands_Conquest0:AutoPlayTest_MP_Dumbo_Conquest0:AutoPlayTest_MP_Firestorm_Conquest0:AutoPlayTest_MP_Outskirts_Conquest0:AutoPlayTest_MP_Tungsten_Conquest0',
        ],
        extraArgs: [EXTRA_ARGS_V3, '--xbapp-deployment-timeout', 7200, '--xbapp-deployment-print-summary', 'true', '--override-server-build-configuration', 'final'].flatten(),
        needGameServer: true,
    )*/

    private final TestInfo pt_perf_flythroughs_XBSS_performance_setup_ch1_stage_AlphaValidation_XBSS_BFLabs = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 1,
        runLevelsInParallel: true,
        timeoutHours: 15,
        platforms: [
            new Platform(name: Name.XBSX, region: Region.WW),
        ],
        testGroup: 'pt_perf_highprio',
        testNames: [
            'MP_Perf_Gameplay_AlphaValidation_XBSS_BFLabs',
        ],
        extraArgs: [EXTRA_ARGS_V3, '--xbapp-deployment-timeout', 7200, '--xbapp-deployment-print-summary', 'true', '--override-server-build-configuration', 'final'].flatten(),
        needGameServer: true,
    )

    private final TestInfo pt_perf_flythroughs_XBSS_performance_setup_ch1_release_AlphaValidation_XBSS_BFLabs = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 1,
        runLevelsInParallel: true,
        timeoutHours: 15,
        platforms: [
            new Platform(name: Name.XBSX, region: Region.WW),
        ],
        testGroup: 'pt_perf_highprio',
        testNames: [
            'MP_Perf_Gameplay_AlphaValidation_XBSS_BFLabs',
        ],
        extraArgs: [EXTRA_ARGS_V3, '--xbapp-deployment-timeout', 7200, '--xbapp-deployment-print-summary', 'true', '--override-server-build-configuration', 'final'].flatten(),
        needGameServer: true,
    )

    private final TestInfo pt_perf_flythroughs_XBSS_CH1_code_final_setup = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 1,
        runLevelsInParallel: true,
        timeoutHours: 8,
        platforms: [
            new Platform(name: Name.XBSX, region: Region.WW),
        ],
        testGroup: 'pt_perf',
        testNames: [
            'F2P_Mem_Gameplay_XBSS', 'MP_Perf_Flythrough_Detailed_XBSS', 'F2P_Perf_Flythrough_XBSS_Detailed',
        ],
        extraArgs: [EXTRA_ARGS_V3, '--xbapp-deployment-timeout', 7200, '--xbapp-deployment-print-summary', 'true', '--override-server-build-configuration', 'final'].flatten(),
        needGameServer: true,
    )

    private final TestInfo pt_perf_flythroughs_XBSS_CH1_code_final_setup2 = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 1,
        runLevelsInParallel: true,
        timeoutHours: 9,
        platforms: [
            new Platform(name: Name.XBSX, region: Region.WW),
        ],
        testGroup: 'pt_perf',
        testNames: [
            'F2P_Mem_Gameplay_GauntletComparingLayouts_XBSS',
            'F2P_Mem_Gameplay_XBSS',
            'MP_Mem_Gameplay_XBSS',
            'SP_Perf_Flythrough_Detailed_XBSS',
        ],
        extraArgs: [EXTRA_ARGS_V3, '--xbapp-deployment-timeout', 7200, '--xbapp-deployment-print-summary', 'true', '--override-server-build-configuration', 'final'].flatten(),
        needGameServer: true,
    )

    private final TestInfo pt_perf_mem_gameplay_XBSS = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 1,
        runLevelsInParallel: true,
        timeoutHours: 7,
        platforms: [
            new Platform(name: Name.XBSX, region: Region.WW),
        ],
        testGroup: 'pt_perf',
        testNames: [
            'F2P_Mem_Gameplay_XBSS',
        ],
        extraArgs: [EXTRA_ARGS_V3, '--xbapp-deployment-timeout', 7200, '--xbapp-deployment-print-summary', 'true', '--override-server-build-configuration', 'final'].flatten(),
        needGameServer: true,
    )
/*
    private final TestInfo pt_perf_mem_gameplay_F2P_XBSS = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 1,
        runLevelsInParallel: true,
        timeoutHours: 7,
        platforms: [
            new Platform(name: Name.XBSX, region: Region.WW),
        ],
        testGroup: 'pt_perf',
        testNames: [
            'MP_Perf_Flythrough_Detailed_XBSS:Flythrough_MP_Abbasid_shared_xbss_pool:Flythrough_MP_Battery_shared_xbss_pool:Flythrough_MP_Capstone_shared_xbss_pool:Flythrough_MP_Aftermath_shared_xbss_pool',
        ],
        extraArgs: [EXTRA_ARGS_V3, '--xbapp-deployment-timeout', 7200, '--xbapp-deployment-print-summary', 'true', '--override-server-build-configuration', 'final', '--max-parallel-tests', 2].flatten(),
        needGameServer: true,
    )
*/
    private final TestInfo pt_perf_lowpriority_XBSS_performance_setup = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 1,
        runLevelsInParallel: true,
        timeoutHours: 21,
        platforms: [
            new Platform(name: Name.XBSX, region: Region.WW),
        ],
        testGroup: 'pt_perf_highprio',
        testNames: [
            'F2P_Perf_Gameplay_XBSS',
            'F2P_Perf_Flythrough_XBSS',
        ],
        extraArgs: [EXTRA_ARGS_V3, '--xbapp-deployment-timeout', 7200, '--xbapp-deployment-print-summary', 'true', '--override-server-build-configuration', 'final'].flatten(),
        needGameServer: true,
    )

    private final TestInfo pt_perf_lowpriority_XBSS_final_setup = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 1,
        runLevelsInParallel: true,
        timeoutHours: 7,
        platforms: [
            new Platform(name: Name.XBSX, region: Region.WW),
        ],
        testGroup: 'pt_perf',
        testNames: ['MP_Mem_Gameplay_XBSS'],
        extraArgs: [EXTRA_ARGS_V3, '--xbapp-deployment-print-summary', 'true', '--xbapp-deployment-timeout', 7200, '--override-server-build-configuration', 'final'].flatten(),
        needGameServer: true,
    )

    private final TestInfo pt_perf_mem_final_ch1_stage_XBSS = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 1,
        runLevelsInParallel: true,
        timeoutHours: 9,
        platforms: [
            new Platform(name: Name.XBSX, region: Region.WW),
        ],
        testGroup: 'pt_perf',
        testNames: [
			'MP_Mem_Gameplay_XBSS:MemoryPlayTest_MP_Abbasid_Breakthrough0:MemoryPlayTest_MP_Abbasid_Conquest0:MemoryPlayTest_MP_Abbasid_Rush0:MemoryPlayTest_MP_Abbasid_SquadDeathMatch0:MemoryPlayTest_MP_Abbasid_KingOfTheHill0:MemoryPlayTest_MP_Abbasid_Domination0',
			'MP_Mem_Gameplay_XBSS:MemoryPlayTest_MP_Aftermath_Conquest0:MemoryPlayTest_MP_Aftermath_Rush0:MemoryPlayTest_MP_Aftermath_KingOfTheHill0:MemoryPlayTest_MP_Aftermath_Breakthrough0:MemoryPlayTest_MP_Aftermath_Domination0',
			'MP_Mem_Gameplay_XBSS:MemoryPlayTest_MP_Battery_Rush0:MemoryPlayTest_MP_Battery_SquadDeathMatch0:MemoryPlayTest_MP_Battery_KingOfTheHill0:MemoryPlayTest_MP_Battery_Domination0:MemoryPlayTest_MP_Battery_Conquest0',
			'MP_Mem_Gameplay_XBSS:MemoryPlayTest_MP_Capstone_Conquest0:MemoryPlayTest_MP_Capstone_Breakthrough0:MemoryPlayTest_MP_Capstone_Rush0:MemoryPlayTest_MP_Capstone_SquadDeathMatch0:MemoryPlayTest_MP_Capstone_KingOfTheHill0:MemoryPlayTest_MP_Capstone_Domination0',
        ],
        extraArgs: [EXTRA_ARGS_V3, '--xbapp-deployment-print-summary', 'true', '--xbapp-deployment-timeout', 7200, '--override-server-build-configuration', 'final'].flatten(),
        needGameServer: true,
    )

    private final TestInfo pt_perf_mem_final_ch1_stage_UltSpec = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 1,
        runLevelsInParallel: true,
        timeoutHours: 13,
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW)
        ],
        testGroup: 'pt_perf',
        testNames: [
			'MP_Mem_Gameplay_UltSpec:MemoryPlayTest_MP_Abbasid_Conquest0_Fidelity:MemoryPlayTest_MP_Abbasid_Breakthrough0_Fidelity:MemoryPlayTest_MP_Abbasid_Rush0_Fidelity:MemoryPlayTest_MP_Abbasid_SquadDeathMatch0_Fidelity:MemoryPlayTest_MP_Abbasid_KingOfTheHill0_Fidelity:MemoryPlayTest_MP_Abbasid_Domination0_Fidelity',
			'MP_Mem_Gameplay_UltSpec:MemoryPlayTest_MP_Aftermath_Conquest0_Fidelity:MemoryPlayTest_MP_Aftermath_Breakthrough0_Fidelity:MemoryPlayTest_MP_Aftermath_Rush0_Fidelity:MemoryPlayTest_MP_Aftermath_KingOfTheHill0_Fidelity:MemoryPlayTest_MP_Aftermath_Domination0_Fidelity',
			'MP_Mem_Gameplay_UltSpec:MemoryPlayTest_MP_Battery_Conquest0_Fidelity:MemoryPlayTest_MP_Battery_Rush0_Fidelity:MemoryPlayTest_MP_Battery_SquadDeathMatch0_Fidelity:MemoryPlayTest_MP_Battery_KingOfTheHill0_Fidelity:MemoryPlayTest_MP_Battery_Domination0_Fidelity',
			'MP_Mem_Gameplay_UltSpec:MemoryPlayTest_MP_Capstone_Conquest0_Fidelity:MemoryPlayTest_MP_Capstone_Breakthrough0_Fidelity:MemoryPlayTest_MP_Capstone_Rush0_Fidelity:MemoryPlayTest_MP_Capstone_SquadDeathMatch0_Fidelity:MemoryPlayTest_MP_Capstone_KingOfTheHill0_Fidelity:MemoryPlayTest_MP_Capstone_Domination0_Fidelity',
			'MP_Mem_Gameplay_UltSpec:MemoryPlayTest_MP_Abbasid_Conquest0_Performance:MemoryPlayTest_MP_Abbasid_Breakthrough0_Performance:MemoryPlayTest_MP_Abbasid_Rush0_Performance:MemoryPlayTest_MP_Abbasid_SquadDeathMatch0_Performance:MemoryPlayTest_MP_Abbasid_KingOfTheHill0_Performance:MemoryPlayTest_MP_Abbasid_Domination0_Performance',
			'MP_Mem_Gameplay_UltSpec:MemoryPlayTest_MP_Aftermath_Conquest0_Performance:MemoryPlayTest_MP_Aftermath_Breakthrough0_Performance:MemoryPlayTest_MP_Aftermath_Rush0_Performance:MemoryPlayTest_MP_Aftermath_KingOfTheHill0_Performance:MemoryPlayTest_MP_Aftermath_Domination0_Performance',
			'MP_Mem_Gameplay_UltSpec:MemoryPlayTest_MP_Battery_Conquest0_Performance:MemoryPlayTest_MP_Battery_Rush0_Performance:MemoryPlayTest_MP_Battery_SquadDeathMatch0_Performance:MemoryPlayTest_MP_Battery_KingOfTheHill0_Performance:MemoryPlayTest_MP_Battery_Domination0_Performance',
			'MP_Mem_Gameplay_UltSpec:MemoryPlayTest_MP_Capstone_Conquest0_Performance:MemoryPlayTest_MP_Capstone_Breakthrough0_Performance:MemoryPlayTest_MP_Capstone_Rush0_Performance:MemoryPlayTest_MP_Capstone_SquadDeathMatch0_Performance:MemoryPlayTest_MP_Capstone_KingOfTheHill0_Performance:MemoryPlayTest_MP_Capstone_Domination0_Performance',
        ],
        extraArgs: [EXTRA_ARGS_V3, '--override-server-build-configuration', 'final', EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING].flatten(),
        needGameServer: true,
    )

    private final TestInfo pt_perf_mem_final_ch1_release_UltSpec = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 1,
        runLevelsInParallel: true,
        timeoutHours: 13,
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW)
        ],
        testGroup: 'pt_perf',
        testNames: [
			'MP_Mem_Gameplay_UltSpec:MemoryPlayTest_MP_Abbasid_Conquest0_Fidelity:MemoryPlayTest_MP_Abbasid_Breakthrough0_Fidelity:MemoryPlayTest_MP_Abbasid_Rush0_Fidelity:MemoryPlayTest_MP_Abbasid_SquadDeathMatch0_Fidelity:MemoryPlayTest_MP_Abbasid_KingOfTheHill0_Fidelity:MemoryPlayTest_MP_Abbasid_Domination0_Fidelity',
			'MP_Mem_Gameplay_UltSpec:MemoryPlayTest_MP_Aftermath_Conquest0_Fidelity:MemoryPlayTest_MP_Aftermath_Breakthrough0_Fidelity:MemoryPlayTest_MP_Aftermath_Rush0_Fidelity:MemoryPlayTest_MP_Aftermath_KingOfTheHill0_Fidelity:MemoryPlayTest_MP_Aftermath_Domination0_Fidelity',
			'MP_Mem_Gameplay_UltSpec:MemoryPlayTest_MP_Battery_Conquest0_Fidelity:MemoryPlayTest_MP_Battery_Rush0_Fidelity:MemoryPlayTest_MP_Battery_SquadDeathMatch0_Fidelity:MemoryPlayTest_MP_Battery_KingOfTheHill0_Fidelity:MemoryPlayTest_MP_Battery_Domination0_Fidelity',
			'MP_Mem_Gameplay_UltSpec:MemoryPlayTest_MP_Capstone_Conquest0_Fidelity:MemoryPlayTest_MP_Capstone_Breakthrough0_Fidelity:MemoryPlayTest_MP_Capstone_Rush0_Fidelity:MemoryPlayTest_MP_Capstone_SquadDeathMatch0_Fidelity:MemoryPlayTest_MP_Capstone_KingOfTheHill0_Fidelity:MemoryPlayTest_MP_Capstone_Domination0_Fidelity',
			'MP_Mem_Gameplay_UltSpec:MemoryPlayTest_MP_Abbasid_Conquest0_Performance:MemoryPlayTest_MP_Abbasid_Breakthrough0_Performance:MemoryPlayTest_MP_Abbasid_Rush0_Performance:MemoryPlayTest_MP_Abbasid_SquadDeathMatch0_Performance:MemoryPlayTest_MP_Abbasid_KingOfTheHill0_Performance:MemoryPlayTest_MP_Abbasid_Domination0_Performance',
			'MP_Mem_Gameplay_UltSpec:MemoryPlayTest_MP_Aftermath_Conquest0_Performance:MemoryPlayTest_MP_Aftermath_Breakthrough0_Performance:MemoryPlayTest_MP_Aftermath_Rush0_Performance:MemoryPlayTest_MP_Aftermath_KingOfTheHill0_Performance:MemoryPlayTest_MP_Aftermath_Domination0_Performance',
			'MP_Mem_Gameplay_UltSpec:MemoryPlayTest_MP_Battery_Conquest0_Performance:MemoryPlayTest_MP_Battery_Rush0_Performance:MemoryPlayTest_MP_Battery_SquadDeathMatch0_Performance:MemoryPlayTest_MP_Battery_KingOfTheHill0_Performance:MemoryPlayTest_MP_Battery_Domination0_Performance',
			'MP_Mem_Gameplay_UltSpec:MemoryPlayTest_MP_Capstone_Conquest0_Performance:MemoryPlayTest_MP_Capstone_Breakthrough0_Performance:MemoryPlayTest_MP_Capstone_Rush0_Performance:MemoryPlayTest_MP_Capstone_SquadDeathMatch0_Performance:MemoryPlayTest_MP_Capstone_KingOfTheHill0_Performance:MemoryPlayTest_MP_Capstone_Domination0_Performance',
        ],
        extraArgs: [EXTRA_ARGS_V3, '--override-server-build-configuration', 'final', EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING].flatten(),
        needGameServer: true,
    )

    private final TestInfo pt_perf_minspec_performance_setup = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 1,
        runLevelsInParallel: true,
        timeoutHours: 19,
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW)
        ],
        testGroup: 'pt_perf',
        testNames: ['Common_Perf_Features_MinSpec', 'F2P_Perf_Gameplay_GauntletComparingLayouts_MinSpec', 'F2P_Perf_Gameplay_GauntletOnLargest_MinSpec', 'F2P_Perf_Gameplay:F2P_Perf_Gameplay_MPGranite_Gauntlet_Performance_gla_min_spec:F2P_Perf_Gameplay_MPGranite_Gauntlet_CacheGrab_Performance_gla_min_spec:F2P_Perf_Gameplay_MPGranite_Gauntlet_BeaconRecovery_Performance_gla_min_spec:F2P_Perf_Gameplay_MPGranite_Gauntlet_DataExtraction_Performance_gla_min_spec:F2P_Perf_Gameplay_MPGranite_Gauntlet_Standoff_Performance_gla_min_spec:F2P_Perf_Gameplay_MPGranite_Gauntlet_Vendetta_Performance_gla_min_spec'],
        extraArgs: [EXTRA_ARGS_V3, '--xbapp-deployment-print-summary', 'true', '--test-state-change-timeout', 120, '--xbapp-deployment-timeout', 7200, '--override-server-build-configuration', 'final'].flatten(),
        needGameServer: true,
    )

    private final TestInfo pt_perf_minspec_meta_performance_setup = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 1,
        runLevelsInParallel: true,
        timeoutHours: 19,
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW)
        ],
        testGroup: 'pt_perf',
        testNames: ['Meta_Perf_FrontEnd_MinSpec'],
        extraArgs: [EXTRA_ARGS_V3, '--xbapp-deployment-print-summary', 'true', '--test-state-change-timeout', 120, '--xbapp-deployment-timeout', 7200, '--override-server-build-configuration', 'final'].flatten(),
        needGameServer: true,
    )

    private final TestInfo pt_perf_high_minspec_performance_setup = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 1,
        runLevelsInParallel: true,
        timeoutHours: 7,
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW)
        ],
        testGroup: 'pt_perf',
        testNames: ['MP_Perf_Flythrough_MinSpec'],
        extraArgs: [EXTRA_ARGS_V3, '--override-server-build-configuration', 'final', EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING, '--use-check-build-consistency', 'true', '--max-parallel-tests', 1].flatten(),
        needGameServer: true,
    )

    private final TestInfo pt_perf_flythroughs_minspec_final_setup2 = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 1,
        runLevelsInParallel: true,
        timeoutHours: 11,
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW)
        ],
        testGroup: 'pt_perf',
        testNames: ['MP_Mem_Gameplay_MinSpec'],
        extraArgs: [EXTRA_ARGS_V3, '--override-server-build-configuration', 'final', '--max-parallel-tests', 2, EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING].flatten(),
        needGameServer: true,
    )

    private final TestInfo pt_perf_mem_final_ch1_stage_MinSpec = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 1,
        runLevelsInParallel: true,
        timeoutHours: 11,
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW)
        ],
        testGroup: 'pt_perf',
        testNames: [
			'MP_Mem_Gameplay_MinSpec:MemoryPlayTest_MP_Abbasid_Conquest0:MemoryPlayTest_MP_Abbasid_Breakthrough0:MemoryPlayTest_MP_Abbasid_Rush0:MemoryPlayTest_MP_Abbasid_SquadDeathMatch0:MemoryPlayTest_MP_Abbasid_KingOfTheHill0:MemoryPlayTest_MP_Abbasid_Domination0',
			'MP_Mem_Gameplay_MinSpec:MemoryPlayTest_MP_Aftermath_Conquest0:MemoryPlayTest_MP_Aftermath_Breakthrough0:MemoryPlayTest_MP_Aftermath_Rush0:MemoryPlayTest_MP_Aftermath_KingOfTheHill0:MemoryPlayTest_MP_Aftermath_Domination0',
			'MP_Mem_Gameplay_MinSpec:MemoryPlayTest_MP_Battery_Conquest0:MemoryPlayTest_MP_Battery_Rush0:MemoryPlayTest_MP_Battery_SquadDeathMatch0:MemoryPlayTest_MP_Battery_KingOfTheHill0:MemoryPlayTest_MP_Battery_Domination0',
			'MP_Mem_Gameplay_MinSpec:MemoryPlayTest_MP_Capstone_Conquest0:MemoryPlayTest_MP_Capstone_Breakthrough0:MemoryPlayTest_MP_Capstone_Rush0:MemoryPlayTest_MP_Capstone_SquadDeathMatch0:MemoryPlayTest_MP_Capstone_KingOfTheHill0:MemoryPlayTest_MP_Capstone_Domination0',
        ],
        extraArgs: [EXTRA_ARGS_V3, '--override-server-build-configuration', 'final', EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING].flatten(),
        needGameServer: true,
    )

    private final TestInfo pt_perf_mem_final_ch1_release_MinSpec = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 1,
        runLevelsInParallel: true,
        timeoutHours: 11,
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW)
        ],
        testGroup: 'pt_perf',
        testNames: [
			'MP_Mem_Gameplay_MinSpec:MemoryPlayTest_MP_Abbasid_Conquest0:MemoryPlayTest_MP_Abbasid_Breakthrough0:MemoryPlayTest_MP_Abbasid_Rush0:MemoryPlayTest_MP_Abbasid_SquadDeathMatch0:MemoryPlayTest_MP_Abbasid_KingOfTheHill0:MemoryPlayTest_MP_Abbasid_Domination0',
			'MP_Mem_Gameplay_MinSpec:MemoryPlayTest_MP_Aftermath_Conquest0:MemoryPlayTest_MP_Aftermath_Breakthrough0:MemoryPlayTest_MP_Aftermath_Rush0:MemoryPlayTest_MP_Aftermath_KingOfTheHill0:MemoryPlayTest_MP_Aftermath_Domination0',
			'MP_Mem_Gameplay_MinSpec:MemoryPlayTest_MP_Battery_Conquest0:MemoryPlayTest_MP_Battery_Rush0:MemoryPlayTest_MP_Battery_SquadDeathMatch0:MemoryPlayTest_MP_Battery_KingOfTheHill0:MemoryPlayTest_MP_Battery_Domination0',
			'MP_Mem_Gameplay_MinSpec:MemoryPlayTest_MP_Capstone_Conquest0:MemoryPlayTest_MP_Capstone_Breakthrough0:MemoryPlayTest_MP_Capstone_Rush0:MemoryPlayTest_MP_Capstone_SquadDeathMatch0:MemoryPlayTest_MP_Capstone_KingOfTheHill0:MemoryPlayTest_MP_Capstone_Domination0',
        ],
        extraArgs: [EXTRA_ARGS_V3, '--override-server-build-configuration', 'final', EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING].flatten(),
        needGameServer: true,
    )

    private final TestInfo pt_perf_flythroughs_minspec_final_setup = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 1,
        runLevelsInParallel: true,
        timeoutHours: 3,
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW)
        ],
        testGroup: 'pt_perf',
        testNames: ['F2P_Perf_Flythrough_MinSpec_Detailed'],
        extraArgs: [EXTRA_ARGS_V3, '--override-server-build-configuration', 'final', '--max-parallel-tests', 2, EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING].flatten(),
        needGameServer: true
    )

    private final TestInfo pt_perf_recspec_performance_setup = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 1,
        runLevelsInParallel: true,
        timeoutHours: 13,
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW)
        ],
        testGroup: 'pt_perf',
        testNames: [
            'SP_Perf_Flythrough_RecSpec',
            'MP_Perf_Flythrough_RecSpec',
        ],
        extraArgs: [EXTRA_ARGS_V3, EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING, '--override-server-build-configuration', 'final', '--max-parallel-tests', 1].flatten(),
        needGameServer: true,
    )
/*
    private final TestInfo pt_perf_recspec_flythrough_final_setup = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 1,
        runLevelsInParallel: true,
        timeoutHours: 13,
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW)
        ],
        testGroup: 'pt_perf',
        testNames: [
            'MP_Perf_Flythrough_Detailed_RecSpec:Flythrough_MP_Abbasid_gla_rec_spec:Flythrough_MP_Battery_gla_rec_spec:Flythrough_MP_Capstone_gla_rec_spec:Flythrough_MP_Aftermath_gla_rec_spec', 'MP_Perf_Flythrough_Seasons_Detailed_RecSpec',
        ],
        extraArgs: [EXTRA_ARGS_V3, EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING, '--override-server-build-configuration', 'final'].flatten(),
        needGameServer: true,
    )
*/
    private final TestInfo pt_perf_flythroughs_recspec_final_setup = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 1,
        runLevelsInParallel: true,
        timeoutHours: 7,
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW)
        ],
        testGroup: 'pt_perf',
        testNames: [
            'MP_Mem_Gameplay_RecSpec',
            'F2P_Mem_Gameplay_RecSpec',
        ],
        extraArgs: [EXTRA_ARGS_V3, EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING, '--override-server-build-configuration', 'final'].flatten(),
        needGameServer: true,
    )

    private final TestInfo pt_perf_mem_final_ch1_stage_RecSpec = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 1,
        runLevelsInParallel: true,
        timeoutHours: 10,
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW)
        ],
        testGroup: 'pt_perf',
        testNames: [
			'MP_Mem_Gameplay_RecSpec:MemoryPlayTest_MP_Abbasid_Conquest0_Fidelity:MemoryPlayTest_MP_Abbasid_Breakthrough0_Fidelity:MemoryPlayTest_MP_Abbasid_Rush0_Fidelity:MemoryPlayTest_MP_Abbasid_SquadDeathMatch0_Fidelity:MemoryPlayTest_MP_Abbasid_KingOfTheHill0_Fidelity:MemoryPlayTest_MP_Abbasid_Domination0_Fidelity',
			'MP_Mem_Gameplay_RecSpec:MemoryPlayTest_MP_Aftermath_Conquest0_Fidelity:MemoryPlayTest_MP_Aftermath_Breakthrough0_Fidelity:MemoryPlayTest_MP_Aftermath_Rush0_Fidelity:MemoryPlayTest_MP_Aftermath_KingOfTheHill0_Fidelity:MemoryPlayTest_MP_Aftermath_Domination0_Fidelity',
			'MP_Mem_Gameplay_RecSpec:MemoryPlayTest_MP_Battery_Conquest0_Fidelity:MemoryPlayTest_MP_Battery_Rush0_Fidelity:MemoryPlayTest_MP_Battery_SquadDeathMatch0_Fidelity:MemoryPlayTest_MP_Battery_KingOfTheHill0_Fidelity:MemoryPlayTest_MP_Battery_Domination0_Fidelity',
			'MP_Mem_Gameplay_RecSpec:MemoryPlayTest_MP_Capstone_Conquest0_Fidelity:MemoryPlayTest_MP_Capstone_Breakthrough0_Fidelity:MemoryPlayTest_MP_Capstone_Rush0_Fidelity:MemoryPlayTest_MP_Capstone_SquadDeathMatch0_Fidelity:MemoryPlayTest_MP_Capstone_KingOfTheHill0_Fidelity:MemoryPlayTest_MP_Capstone_Domination0_Fidelity',
			'MP_Mem_Gameplay_RecSpec:MemoryPlayTest_MP_Abbasid_Conquest0_Performance:MemoryPlayTest_MP_Abbasid_Breakthrough0_Performance:MemoryPlayTest_MP_Abbasid_Rush0_Performance:MemoryPlayTest_MP_Abbasid_SquadDeathMatch0_Performance:MemoryPlayTest_MP_Abbasid_KingOfTheHill0_Performance:MemoryPlayTest_MP_Abbasid_Domination0_Performance',
			'MP_Mem_Gameplay_RecSpec:MemoryPlayTest_MP_Aftermath_Conquest0_Performance:MemoryPlayTest_MP_Aftermath_Breakthrough0_Performance:MemoryPlayTest_MP_Aftermath_Rush0_Performance:MemoryPlayTest_MP_Aftermath_KingOfTheHill0_Performance:MemoryPlayTest_MP_Aftermath_Domination0_Performance',
			'MP_Mem_Gameplay_RecSpec:MemoryPlayTest_MP_Battery_Conquest0_Performance:MemoryPlayTest_MP_Battery_Rush0_Performance:MemoryPlayTest_MP_Battery_SquadDeathMatch0_Performance:MemoryPlayTest_MP_Battery_KingOfTheHill0_Performance:MemoryPlayTest_MP_Battery_Domination0_Performance',
			'MP_Mem_Gameplay_RecSpec:MemoryPlayTest_MP_Capstone_Conquest0_Performance:MemoryPlayTest_MP_Capstone_Breakthrough0_Performance:MemoryPlayTest_MP_Capstone_Rush0_Performance:MemoryPlayTest_MP_Capstone_SquadDeathMatch0_Performance:MemoryPlayTest_MP_Capstone_KingOfTheHill0_Performance:MemoryPlayTest_MP_Capstone_Domination0_Performance',
        ],
        extraArgs: [EXTRA_ARGS_V3, EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING, '--override-server-build-configuration', 'final'].flatten(),
        needGameServer: true,
    )

    private final TestInfo pt_perf_mem_final_ch1_release_RecSpec = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 1,
        runLevelsInParallel: true,
        timeoutHours: 10,
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW)
        ],
        testGroup: 'pt_perf',
        testNames: [
			'MP_Mem_Gameplay_RecSpec:MemoryPlayTest_MP_Abbasid_Conquest0_Fidelity:MemoryPlayTest_MP_Abbasid_Breakthrough0_Fidelity:MemoryPlayTest_MP_Abbasid_Rush0_Fidelity:MemoryPlayTest_MP_Abbasid_SquadDeathMatch0_Fidelity:MemoryPlayTest_MP_Abbasid_KingOfTheHill0_Fidelity:MemoryPlayTest_MP_Abbasid_Domination0_Fidelity',
			'MP_Mem_Gameplay_RecSpec:MemoryPlayTest_MP_Aftermath_Conquest0_Fidelity:MemoryPlayTest_MP_Aftermath_Breakthrough0_Fidelity:MemoryPlayTest_MP_Aftermath_Rush0_Fidelity:MemoryPlayTest_MP_Aftermath_KingOfTheHill0_Fidelity:MemoryPlayTest_MP_Aftermath_Domination0_Fidelity',
			'MP_Mem_Gameplay_RecSpec:MemoryPlayTest_MP_Battery_Conquest0_Fidelity:MemoryPlayTest_MP_Battery_Rush0_Fidelity:MemoryPlayTest_MP_Battery_SquadDeathMatch0_Fidelity:MemoryPlayTest_MP_Battery_KingOfTheHill0_Fidelity:MemoryPlayTest_MP_Battery_Domination0_Fidelity',
			'MP_Mem_Gameplay_RecSpec:MemoryPlayTest_MP_Capstone_Conquest0_Fidelity:MemoryPlayTest_MP_Capstone_Breakthrough0_Fidelity:MemoryPlayTest_MP_Capstone_Rush0_Fidelity:MemoryPlayTest_MP_Capstone_SquadDeathMatch0_Fidelity:MemoryPlayTest_MP_Capstone_KingOfTheHill0_Fidelity:MemoryPlayTest_MP_Capstone_Domination0_Fidelity',
			'MP_Mem_Gameplay_RecSpec:MemoryPlayTest_MP_Abbasid_Conquest0_Performance:MemoryPlayTest_MP_Abbasid_Breakthrough0_Performance:MemoryPlayTest_MP_Abbasid_Rush0_Performance:MemoryPlayTest_MP_Abbasid_SquadDeathMatch0_Performance:MemoryPlayTest_MP_Abbasid_KingOfTheHill0_Performance:MemoryPlayTest_MP_Abbasid_Domination0_Performance',
			'MP_Mem_Gameplay_RecSpec:MemoryPlayTest_MP_Aftermath_Conquest0_Performance:MemoryPlayTest_MP_Aftermath_Breakthrough0_Performance:MemoryPlayTest_MP_Aftermath_Rush0_Performance:MemoryPlayTest_MP_Aftermath_KingOfTheHill0_Performance:MemoryPlayTest_MP_Aftermath_Domination0_Performance',
			'MP_Mem_Gameplay_RecSpec:MemoryPlayTest_MP_Battery_Conquest0_Performance:MemoryPlayTest_MP_Battery_Rush0_Performance:MemoryPlayTest_MP_Battery_SquadDeathMatch0_Performance:MemoryPlayTest_MP_Battery_KingOfTheHill0_Performance:MemoryPlayTest_MP_Battery_Domination0_Performance',
			'MP_Mem_Gameplay_RecSpec:MemoryPlayTest_MP_Capstone_Conquest0_Performance:MemoryPlayTest_MP_Capstone_Breakthrough0_Performance:MemoryPlayTest_MP_Capstone_Rush0_Performance:MemoryPlayTest_MP_Capstone_SquadDeathMatch0_Performance:MemoryPlayTest_MP_Capstone_KingOfTheHill0_Performance:MemoryPlayTest_MP_Capstone_Domination0_Performance',
        ],
        extraArgs: [EXTRA_ARGS_V3, EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING, '--override-server-build-configuration', 'final'].flatten(),
        needGameServer: true,
    )

    private final TestInfo pt_perf_flythroughs_minspec_final_setup3 = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 1,
        runLevelsInParallel: true,
        timeoutHours: 6,
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW)
        ],
        testGroup: 'pt_perf',
        tests: [
            new TestSuite(name: 'F2P_Mem_Gameplay_GauntletComparingLayouts_MinSpec', timeoutHours: 1, extraArgs: [EXTRA_ARGS_V3, EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING, '--max-parallel-tests', 1].flatten(), needGameServer: false),
            new TestSuite(name: 'F2P_Mem_Gameplay_MinSpec', timeoutHours: 1, extraArgs: [EXTRA_ARGS_V3, EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING, '--max-parallel-tests', 1].flatten(), needGameServer: false),
            new TestSuite(name: 'MP_Perf_Flythrough_Detailed_MinSpec:Flythrough_MP_Abbasid_gla_min_spec:Flythrough_MP_Battery_gla_min_spec:Flythrough_MP_Capstone_gla_min_spec:Flythrough_MP_Aftermath_gla_min_spec', timeoutHours: 1, extraArgs: [EXTRA_ARGS_V3, EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING, '--max-parallel-tests', 1].flatten(), needGameServer: false),
        ]
    )

    private final TestInfo pt_perf_flythroughs_minspec_final_setup_ch1_code = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 1,
        runLevelsInParallel: true,
        timeoutHours: 8,
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW)
        ],
        testGroup: 'pt_perf',
        tests: [
            new TestSuite(name: 'F2P_Mem_Gameplay_GauntletComparingLayouts_MinSpec', timeoutHours: 1, extraArgs: [EXTRA_ARGS_V3, EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING, '--max-parallel-tests', 1].flatten(), needGameServer: false),
        ]
    )

    private final TestInfo pt_perf_lowpriority_final_setup = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 3,
        runLevelsInParallel: true,
        timeoutHours: 8,
        platforms: REGIONAL_BCT_PLATFORMS,
        testGroup: 'pt_perf',
        testNames: ['MP_Perf_LoadLevel', 'F2P_Perf_LoadLevel'],
        extraArgs: [EXTRA_ARGS_V3, '--xbapp-deployment-timeout', 7200, '--xbapp-deployment-print-summary', 'true', '--override-server-build-configuration', 'final'].flatten(),
        needGameServer: true,
    )

    private final TestInfo pt_perf_lowpriority_final_setup_minspec = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 3,
        runLevelsInParallel: true,
        timeoutHours: 8,
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW)
        ],
        testGroup: 'pt_perf',
        testNames: ['MP_Perf_LoadLevel_MinSpec'],
        extraArgs: [EXTRA_ARGS_V3, '--xbapp-deployment-timeout', 7200, '--xbapp-deployment-print-summary', 'true', '--override-server-build-configuration', 'final'].flatten(),
        needGameServer: true,
    )

    private final TestInfo pt_perf_lowpriority_final_setup_Trinity = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 3,
        runLevelsInParallel: true,
        timeoutHours: 8,
        platforms: [
            new Platform(name: Name.PS5, region: Region.DEV)
        ],
        testGroup: 'pt_perf',
        testNames: ['MP_Perf_LoadLevel_Trinity'],
        extraArgs: [EXTRA_ARGS_V3, '--xbapp-deployment-timeout', 7200, '--xbapp-deployment-print-summary', 'true', '--override-server-build-configuration', 'final'].flatten(),
        needGameServer: true,
    )

    private final TestInfo pt_perf_lowpriority_final_setup_recspec = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 3,
        runLevelsInParallel: true,
        timeoutHours: 8,
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW)
        ],
        testGroup: 'pt_perf',
        testNames: ['MP_Perf_LoadLevel_RecSpec'],
        extraArgs: [EXTRA_ARGS_V3, '--xbapp-deployment-timeout', 7200, '--xbapp-deployment-print-summary', 'true', '--override-server-build-configuration', 'final'].flatten(),
        needGameServer: true,
    )

    private final TestInfo pt_perf_lowpriority_final_setup_xbss = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 3,
        runLevelsInParallel: true,
        timeoutHours: 8,
        platforms: [
            new Platform(name: Name.XBSX, region: Region.WW)
        ],
        testGroup: 'pt_perf',
        testNames: ['MP_Perf_LoadLevel_XBSS'],
        extraArgs: [EXTRA_ARGS_V3, '--xbapp-deployment-timeout', 7200, '--xbapp-deployment-print-summary', 'true', '--override-server-build-configuration', 'final'].flatten(),
        needGameServer: true,
    )

    private final TestInfo pt_perf_lowpriority_final_setup_CH1 = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 3,
        runLevelsInParallel: true,
        timeoutHours: 8,
        platforms: REGIONAL_BCT_PLATFORMS,
        testGroup: 'pt_perf',
        tests: [
            new TestSuite(name: 'F2P_Perf_Gameplay_Detailed', extraArgs: [EXTRA_ARGS_V3, '--xbapp-deployment-timeout', 7200, '--xbapp-deployment-print-summary', 'true', '--override-server-build-configuration', 'final'].flatten(), needGameServer: true),
            new TestSuite(name: 'MP_Perf_Gameplay_Detailed', extraArgs: [EXTRA_ARGS_V3, '--xbapp-deployment-timeout', 7200, '--xbapp-deployment-print-summary', 'true', '--override-server-build-configuration', 'final'].flatten(), needGameServer: true),
        ]
    )

    private final TestInfo pt_perf_lowpriority_final_setup_synced = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 8,
        platforms: REGIONAL_BCT_PLATFORMS_CONSOLE_ONLY,
        testGroup: 'pt_perf',
        testNames: ['Common_Perf_CoreCombat', 'Common_Perf_CoreCombat_PGTE'],
        extraArgs: [EXTRA_ARGS_V3, '--xbapp-deployment-timeout', 7200, '--xbapp-deployment-print-summary', 'true', '--override-server-build-configuration', 'final'].flatten(),
        needGameServer: true,
    )

    private final TestInfo pt_perf_lowpriority_final_setup_synced_xbss = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 8,
        platforms: [
            new Platform(name: Name.XBSX, region: Region.WW),
        ],
        testGroup: 'pt_perf',
        testNames: ['Common_Perf_CoreCombat_PGTE_XBSS'],
        extraArgs: [EXTRA_ARGS_V3, '--xbapp-deployment-timeout', 7200, '--xbapp-deployment-print-summary', 'true', '--override-server-build-configuration', 'final'].flatten(),
        needGameServer: true,
    )

    private final TestInfo pt_perf_lowpriority_final_setup_synced_ps5 = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 8,
        platforms: [
            new Platform(name: Name.PS5, region: Region.DEV),
        ],
        testGroup: 'pt_perf',
        testNames: ['Common_Perf_CoreCombat'],
        extraArgs: EXTRA_ARGS_V3,
        needGameServer: true,
    )
/*
    private final TestInfo pt_stab_ch1_stage = new TestInfo(
        platforms: REGIONAL_BCT_PLATFORMS,
        parallelLimit: 3,
        runLevelsInParallel: true,
        showTestResultsOnJenkins: true,
        testGroup: 'pt_stab',
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 5,
        testNames: ['F2P_Stab_Gameplay', 'MP_Stab_Gameplay'],
        extraArgs: EXTRA_ARGS_V2,
    )*/

    private final TestInfo lkg_auto_nonpinned_agents = new TestInfo(
        parallelLimit: 3,
        runLevelsInParallel: true,
        customTestSuiteData: 'show_result_in_nicesyncer:true',
        testGroup: 'lkg_auto',
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 5,
        testNames: ['LKG_CoreCombat', 'LKG_MMT', 'LKG_F2P', 'LKG_SP'],
        extraArgs: EXTRA_ARGS_V1,
        platforms: BCT_PLATFORMS,
    )

    private final TestInfo pt_perf_lowpriority_performance_setup = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 10,
        platforms: REGIONAL_BCT_PLATFORMS,
        testGroup: 'pt_perf',
        testNames: [
            'F2P_Perf_SpatialEditor',
            'Common_Perf_CoreCombat'
        ],
        extraArgs: [EXTRA_ARGS_V3, '--xbapp-deployment-timeout', 7200, '--xbapp-deployment-print-summary', 'true', '--override-server-build-configuration', 'final'].flatten(),
        needGameServer: true,
    )

    private final TestInfo pt_perf_lowpriority_performance_setup2 = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 7,
        platforms: REGIONAL_BCT_PLATFORMS_CONSOLE_ONLY,
        testGroup: 'pt_perf',
        testNames: [
            'F2P_Perf_SpatialEditor',
        ],
        extraArgs: [EXTRA_ARGS_V3, '--xbapp-deployment-timeout', 7200, '--xbapp-deployment-print-summary', 'true', '--override-server-build-configuration', 'final'].flatten(),
        needGameServer: true,
    )
/*
    private final TestInfo pt_stab_la = new TestInfo(
        platforms: REGIONAL_BCT_PLATFORMS,
        remoteLabel: 'eala',
        parallelLimit: 3,
        runLevelsInParallel: true,
        testGroup: 'la_farm_test',
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 9,
        testNames: ['F2P_Stab_Gameplay', 'MP_Stab_Gameplay'],
        extraArgs: EXTRA_ARGS_V2,
    )*/

    private final TestInfo pt_perf_highpriority_performance_setup_la = new TestInfo(
        parallelLimit: 4,
        runLevelsInParallel: true,
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 10,
        platforms: REGIONAL_BCT_PLATFORMS_CONSOLE_ONLY,
        testGroup: 'la_farm_test',
        testNames: ['MP_Perf_Gameplay', 'F2P_Perf_Gameplay'],
        extraArgs: [EXTRA_ARGS_V3, '--xbapp-deployment-timeout', 7200, '--xbapp-deployment-print-summary', 'true', '--override-server-build-configuration', 'final'].flatten(),
        needGameServer: true,
    )

    private final TestInfo pt_perf_lowpriority_final_setup_la = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 2,
        runLevelsInParallel: true,
        timeoutHours: 7,
        platforms: REGIONAL_BCT_PLATFORMS_CONSOLE_ONLY,
        testGroup: 'la_farm_test',
        testNames: ['F2P_Perf_Flythrough_Detailed'],
        extraArgs: [EXTRA_ARGS_V3, '--xbapp-deployment-timeout', 7200, '--xbapp-deployment-print-summary'].flatten(),
        needGameServer: true,
    )

    private final TestInfo large_scale_autoplaytest_setup_ch1_content_dev = new TestInfo(
        timeoutHours: 6,
        parallelLimit: 1,
        runLevelsInParallel: true,
        clientPlatforms: ['ps5'],
        // disableAutomaticRetry: true,
        platforms: [
            new Platform(name: Name.LINUX64, region: Region.WW),
        ],
        testGroup: 'large_scale',
        testNames: ['MP_Stab_Gameplay_LargeScale2'],
        extraArgs: [EXTRA_ARGS_L_SCALE_MP, EXTRA_ARGS_SHIFT_CH1_CONTENT_DEV, EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING, '--test-state-change-timeout', 240].flatten(),
    )

    private final TestInfo large_scale_autoplaytest_setup_online_mp = new TestInfo(
        timeoutHours: 12,
        parallelLimit: 1,
        runLevelsInParallel: true,
        // disableAutomaticRetry: true,
        platforms: [
            new Platform(name: Name.LINUX64, region: Region.WW),
        ],
        remoteLabel: 'eala',
        testGroup: 'large_scale',
        testNames: ['MP_Stab_Gameplay_Online_Spin', 'MP_Stab_Gameplay_LargeScale_Breakthrough_AllMaps'],
        extraArgs: [EXTRA_ARGS_L_SCALE_MP_DENSITY1, EXTRA_ARGS_TEMP, '--soaking-test-stop-mode', 'round', '--soaking-test-total-rounds', 1, '--soaking-test-running-time', '02:00:00', '--server-runtime-debug-args', '"-DrakeSystem.AsyncAddTilesMode 1 -Drake.BudgetAddTile 1 -spreadhost $HOST_IP"'].flatten(),
    )

    private final TestInfo autoplaytest_setup_online_mp_ThinClient = new TestInfo(
        timeoutHours: 12,
        parallelLimit: 1,
        runLevelsInParallel: true,
        // disableAutomaticRetry: true,
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW),
        ],
        remoteLabel: 'eala',
        testGroup: 'pt_stab',
        testNames: ['MP_Stab_Gameplay_Online_ThinClient'],
        extraArgs: [EXTRA_ARGS_V2, '--override-server-build-configuration', 'final',].flatten(),
    )

    private final TestInfo large_scale_autoplaytest_setup_online_two_mp = new TestInfo(
        timeoutHours: 18,
        parallelLimit: 1,
        runLevelsInParallel: true,
        // disableAutomaticRetry: true,
        platforms: [
            new Platform(name: Name.LINUX64, region: Region.WW),
        ],
        remoteLabel: 'eala',
        testGroup: 'large_scale',
        testNames: ['MP_Server_Gameplay_LargeScale_SquadDeathMatch_AllMaps', 'MP_Server_Perf_LargeScale_DiceAI_Online'],
        extraArgs: [EXTRA_ARGS_L_SCALE_MP_DENSITY1, EXTRA_ARGS_TEMP, '--soaking-test-stop-mode', 'round', '--soaking-test-total-rounds', 1, '--soaking-test-running-time', '02:00:00', '--server-runtime-debug-args', '"-DrakeSystem.AsyncAddTilesMode 1 -Drake.BudgetAddTile 1 -spreadhost $HOST_IP"'].flatten(),
    )

    private final TestInfo large_scale_autoplaytest_setup = new TestInfo(
        timeoutHours: 20,
        parallelLimit: 1,
        runLevelsInParallel: true,
        clientPlatforms: ['ps5'],
        // disableAutomaticRetry: true,
        platforms: [
            new Platform(name: Name.LINUX64, region: Region.WW),
        ],
        testGroup: 'large_scale',
        tests: [
            new TestSuite(
                name: 'MP_Server_Gameplay_LargeScale_Conquest_AllMaps',
                timeoutHours: 6,
                extraArgs: [EXTRA_ARGS_L_SCALE_MP_DENSITY1, EXTRA_ARGS_TEMP, '--soaking-test-stop-mode', 'round', '--soaking-test-total-rounds', 1, '--soaking-test-running-time', '02:00:00', '--server-runtime-debug-args', '"-DrakeSystem.AsyncAddTilesMode 1 -Drake.BudgetAddTile 1 -spreadhost $HOST_IP"'].flatten(),
            ),
            new TestSuite(
                name: 'F2P_Perf_Gameplay_Largescale_Online',
                timeoutHours: 6,
                extraArgs: [EXTRA_ARGS_L_SCALE_F2P_DENSITY1, EXTRA_ARGS_SHIFT_CH1_CONTENT_DEV, EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING, '--soaking-test-stop-mode', 'round', '--soaking-test-total-rounds', 1, '--soaking-test-running-time', '02:00:00', '--server-runtime-debug-args', '"-DrakeSystem.AsyncAddTilesMode 1 -Drake.BudgetAddTile 1 -spreadhost $HOST_IP"'].flatten(),
            ),

        ]
    )

    private final TestInfo large_scale_autoplaytest_setup_fullbox = new TestInfo(
        timeoutHours: 20,
        parallelLimit: 1,
        runLevelsInParallel: true,
        // disableAutomaticRetry: true,
        platforms: [
            new Platform(name: Name.LINUX64, region: Region.WW),
        ],
        testGroup: 'large_scale',
        tests: [
            new TestSuite(
                name: 'MP_Server_Gameplay_LargeScale_Conquest_Fullbox',
                timeoutHours: 6,
                extraArgs: [EXTRA_ARGS_L_SCALE_MP_DENSITY2, EXTRA_ARGS_TEMP, '--soaking-test-stop-mode', 'round', '--soaking-test-total-rounds', 1, '--soaking-test-running-time', '02:00:00', '--server-runtime-debug-args', '"-DrakeSystem.AsyncAddTilesMode 1 -Drake.BudgetAddTile 1 -spreadhost $HOST_IP"'].flatten(),
            ),
        ]
    )

    private final TestInfo large_scale_autoplaytest_setup_ch1_stage = new TestInfo(
        timeoutHours: 12,
        parallelLimit: 1,
        runLevelsInParallel: true,
        clientPlatforms: ['ps5'],
        // disableAutomaticRetry: true,
        platforms: [
            new Platform(name: Name.LINUX64, region: Region.WW),
        ],
        testGroup: 'large_scale',
        tests: [
            new TestSuite(
                name: 'F2P_Perf_Gameplay_Largescale_Online',
                timeoutHours: 5,
                extraArgs: [EXTRA_ARGS_L_SCALE_F2P_DENSITY1, EXTRA_ARGS_SHIFT_CH1_STAGE, EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING, '--test-state-change-timeout', 240, '--server-runtime-debug-args', '"-DrakeSystem.AsyncAddTilesMode 1 -Drake.BudgetAddTile 1 -spreadhost $HOST_IP"'].flatten(),
            ),
            new TestSuite(
                name: 'MP_Server_Gameplay_LargeScale_Conquest_AllMaps',
                timeoutHours: 5,
                extraArgs: [EXTRA_ARGS_L_SCALE_MP_DENSITY1, EXTRA_ARGS_SHIFT_CH1_STAGE, EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING, '--test-state-change-timeout', 240, '--server-runtime-debug-args', '"-DrakeSystem.AsyncAddTilesMode 1 -Drake.BudgetAddTile 1 -spreadhost $HOST_IP"'].flatten(),
            ),
        ]
    )

    private final TestInfo thin_client_linux_server_synced = new TestInfo(
        platforms: [
            new Platform(name: Name.LINUX64, region: Region.WW),
        ],
        remoteLabel: 'eala',
        serverPlatform: 'linuxserver',
        testGroup: 'large_scale',
        timeoutHours: 5,
        testNames: ['Common_QVE'],
        extraArgs: ['--timeout-client-level-load', '00:12:00', '--default-thin-client-target-group-overrides', 'qds_linux', '--runtime-connect-timeout', 360, '--timeout-client-deploy', '01:10:00', '--atf-rest-heartbeat-enabled', 'true', '--default-server-platform', 'linux', EXTRA_ARGS_CRITERION].flatten(),
    )

    private final TestInfo multi_platform_client_spin_server_ch1_content_dev = new TestInfo(
        timeoutHours: 6,
        parallelLimit: 1,
        runLevelsInParallel: true,
        platforms: [
            new Platform(name: Name.LINUX64, region: Region.WW),
        ],
        remoteLabel: 'eala',
        clientPlatforms: ['ps5', 'xbsx', 'win64'],
        testGroup: 'large_scale',
        testNames: ['MP_Stab_Gameplay_Crossplay'],
        extraArgs: [EXTRA_ARGS_L_SCALE_MP, EXTRA_ARGS_SHIFT_CH1_CONTENT_DEV, EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING, EXTRA_ARGS_CRITERION, '--test-state-change-timeout', 240, '--trace-local', true].flatten(),
    )

    private final TestInfo pt_perf_highpriority_UltSpec_performance_setup = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 1,
        runLevelsInParallel: true,
        timeoutHours: 10,
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW)
        ],
        testGroup: 'pt_perf_highprio',
        testNames: [
            'F2P_Perf_Gameplay_UltSpec', 'MP_Perf_Gameplay_UltSpec', 'F2P_Perf_Gameplay_GauntletOnLargest', 'MP_Perf_Flythrough_Detailed', 'F2P_Perf_Flythrough_Detailed',
        ],
        extraArgs: [EXTRA_ARGS_V3, '--override-server-build-configuration', 'final', '--max-parallel-tests', '3'].flatten(),
        needGameServer: true,
    )

    private final TestInfo pt_perf_AlphaValidation_UltSpec_BFlabs_performance_setup = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 1,
        runLevelsInParallel: true,
        timeoutHours: 10,
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW)
        ],
        testGroup: 'pt_perf_highprio',
        testNames: [
            'MP_Perf_Gameplay_AlphaValidation_UltSpec_Bflabs',
        ],
        extraArgs: [EXTRA_ARGS_V3, '--override-server-build-configuration', 'final', '--max-parallel-tests', '3'].flatten(),
        needGameServer: true,
    )

    private final TestInfo pt_perf_AlphaValidation_UltSpec_BFlabs_performance_setup_release = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 1,
        runLevelsInParallel: true,
        timeoutHours: 10,
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW)
        ],
        testGroup: 'pt_perf_highprio',
        testNames: [
            'MP_Perf_Gameplay_AlphaValidation_UltSpec_Bflabs',
        ],
        extraArgs: [EXTRA_ARGS_V3, '--override-server-build-configuration', 'final', '--max-parallel-tests', '3'].flatten(),
        needGameServer: true,
    )

    private final TestInfo pt_perf_UltSpec_F2P_performance_setup = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 1,
        runLevelsInParallel: true,
        timeoutHours: 10,
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW)
        ],
        testGroup: 'pt_perf_highprio',
        testNames: [
            'F2P_Perf_Gameplay_GauntletOnLargest_UltSpec',
            'Common_Perf_Features_UltSpec',
        ],
        extraArgs: [EXTRA_ARGS_V3, '--override-server-build-configuration', 'final',].flatten(),
        needGameServer: true,
    )

    private final TestInfo pt_perf_UltSpec_meta_performance_setup = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 1,
        runLevelsInParallel: true,
        timeoutHours: 10,
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW)
        ],
        testGroup: 'pt_perf_highprio',
        testNames: [
            'Meta_Perf_FrontEnd_UltSpec',
        ],
        extraArgs: [EXTRA_ARGS_V3, '--override-server-build-configuration', 'final',].flatten(),
        needGameServer: true,
    )

    private final TestInfo pt_perf_highpriority_minspec_performance_setup = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 1,
        remoteLabel: 'eala',
        runLevelsInParallel: true,
        timeoutHours: 12,
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW)
        ],
        testGroup: 'pt_perf_highprio',
        testNames: [
            'F2P_Perf_Flythrough_MinSpec',
            'F2P_Perf_Gameplay_MinSpec',
            'MP_Perf_Gameplay_MinSpec',
            'F2P_Perf_Gameplay_GauntletOnLargest_MinSpec',
        ],
        extraArgs: [EXTRA_ARGS_V3, '--override-server-build-configuration', 'final','--max-parallel-tests', 1, EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING].flatten(),
        needGameServer: true,
    )
/*
    private final TestInfo pt_perf_highpriority_minspec_performance_setup_AlphaValidation_MinSpec_Coverage = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 1,
        runLevelsInParallel: true,
        timeoutHours: 15,
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW)
        ],
        testGroup: 'pt_perf_highprio',
        testNames: [
            'MP_Perf_Gameplay_AlphaValidation_MinSpec_Coverage',
        ],
        extraArgs: [EXTRA_ARGS_V3, '--override-server-build-configuration', 'final','--max-parallel-tests', 1, EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING].flatten(),
        needGameServer: true,
    )

    private final TestInfo pt_perf_highpriority_minspec_performance_setup_AlphaValidation_MinSpec_HighPrio = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 1,
        runLevelsInParallel: true,
        timeoutHours: 15,
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW)
        ],
        testGroup: 'pt_perf_highprio',
        testNames: [
            'MP_Perf_Gameplay_AlphaValidation_MinSpec_HighPrio',
        ],
        extraArgs: [EXTRA_ARGS_V3, '--override-server-build-configuration', 'final','--max-parallel-tests', 1, EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING].flatten(),
        needGameServer: true,
    )

    private final TestInfo pt_perf_highpriority_minspec_performance_setup_AlphaValidation_HighPrio = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 1,
        runLevelsInParallel: true,
        timeoutHours: 15,
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW)
        ],
        testGroup: 'pt_perf_highprio',
        testNames: [
            'MP_Perf_Gameplay_AlphaValidation_MinSpec_HighPrio:AutoPlayTest_MP_Aftermath_Conquest0:AutoPlayTest_MP_Badlands_Conquest0:AutoPlayTest_MP_Dumbo_Conquest0:AutoPlayTest_MP_Firestorm_Conquest0:AutoPlayTest_MP_Outskirts_Conquest0:AutoPlayTest_MP_Tungsten_Conquest0:AutoPlayTest_MP_Aftermath_Conquest0:AutoPlayTest_MP_Badlands_Conquest0:AutoPlayTest_MP_Dumbo_Conquest0:AutoPlayTest_MP_Firestorm_Conquest0:AutoPlayTest_MP_Outskirts_Conquest0:AutoPlayTest_MP_Tungsten_Conquest0',
        ],
        extraArgs: [EXTRA_ARGS_V3, '--override-server-build-configuration', 'final','--max-parallel-tests', 1, EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING].flatten(),
        needGameServer: true,
    )*/
    private final TestInfo pt_perf_highpriority_minspec_performance_setup_AlphaValidation_MinSpec_BFLabs_release = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 1,
        runLevelsInParallel: true,
        timeoutHours: 15,
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW)
        ],
        testGroup: 'pt_perf_highprio',
        testNames: [
            'MP_Perf_Gameplay_AlphaValidation_MinSpec_BFLabs',
        ],
        extraArgs: [EXTRA_ARGS_V3, '--override-server-build-configuration', 'final', '--max-parallel-tests', 1, EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING].flatten(),
        needGameServer: true,
    )

    private final TestInfo pt_perf_highpriority_minspec_performance_setup_AlphaValidation_MinSpec_BFLabs = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 1,
        runLevelsInParallel: true,
        timeoutHours: 15,
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW)
        ],
        testGroup: 'pt_perf_highprio',
        testNames: [
            'MP_Perf_Gameplay_AlphaValidation_MinSpec_BFLabs',
        ],
        extraArgs: [EXTRA_ARGS_V3, '--override-server-build-configuration', 'final', '--max-parallel-tests', 1, EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING].flatten(),
        needGameServer: true,
    )
/*
    private final TestInfo pt_perf_highpriority_minspec_performance_setup_ch1_stage = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 1,
        remoteLabel: 'eala',
        runLevelsInParallel: true,
        timeoutHours: 8,
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW)
        ],
        testGroup: 'pt_perf_highprio',
        testNames: [
            'F2P_Perf_Flythrough_MinSpec_Detailed','F2P_Perf_Gameplay_GauntletOnLargest_MinSpec','F2P_Perf_Gameplay_MinSpec','MP_Perf_Flythrough_Detailed_MinSpec',
        ],
        extraArgs: [EXTRA_ARGS_V3, '--override-server-build-configuration', 'final', EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING].flatten(),
        needGameServer: true,
    )*/

    private final TestInfo pt_perf_setup_ch1_release_minspec = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 1,
        runLevelsInParallel: true,
        timeoutHours: 8,
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW)
        ],
        testGroup: 'pt_perf_highprio',
        testNames: [
            'F2P_Perf_Gameplay_MinSpec',
        ],
        extraArgs: [EXTRA_ARGS_V3, '--override-server-build-configuration', 'final', EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING].flatten(),
        needGameServer: true,
    )

    private final TestInfo pt_perf_setup_ch1_stage_minspec_eala = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 1,
        runLevelsInParallel: true,
        timeoutHours: 8,
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW)
        ],
        testGroup: 'pt_perf_highprio',
        testNames: [
            'MP_Perf_Gameplay_MinSpec',
        ],
        extraArgs: [EXTRA_ARGS_V3, '--override-server-build-configuration', 'final', EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING].flatten(),
        needGameServer: true,
    )

    private final TestInfo pt_perf_highpriority_minspec_final_setup = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 1,
        runLevelsInParallel: true,
        timeoutHours: 5,
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW)
        ],
        testGroup: 'pt_perf_highprio',
        testNames: [
            'F2P_Perf_Flythrough_MinSpec_Detailed',
            'MP_Perf_Flythrough_Detailed_MinSpec',
            'SP_Perf_Flythrough_Detailed_MinSpec',
        ],
        extraArgs: [EXTRA_ARGS_V3, '--override-server-build-configuration', 'final', EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING].flatten(),
        needGameServer: true,
    )

    private final TestInfo pt_perf_recspec_highpriority_performance_setup = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 1,
        runLevelsInParallel: true,
        timeoutHours: 12,
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW)
        ],
        testGroup: 'pt_perf_highprio',
        testNames: [
            'MP_Perf_Gameplay_RecSpec',
            'F2P_Perf_Portal_RecSpec',
        ],
        extraArgs: [EXTRA_ARGS_V3, '--override-server-build-configuration', 'final', EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING].flatten(),
        needGameServer: true,
    )

    private final TestInfo pt_perf_recspec_highpriority_performance_setup_2 = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 1,
        runLevelsInParallel: true,
        timeoutHours: 14,
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW)
        ],
        testGroup: 'pt_perf_highprio',
        testNames: [
            'F2P_Perf_Gameplay_RecSpec',
            'F2P_Perf_Gameplay_GauntletComparingLayouts_RecSpec',
            'F2P_Perf_Gameplay_GauntletOnLargest_RecSpec',
            'Common_Perf_Features_RecSpec',
        ],
        extraArgs: [EXTRA_ARGS_V3, '--override-server-build-configuration', 'final', EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING].flatten(),
        needGameServer: true,
    )

    private final TestInfo pt_perf_minspec_sp_perf_performance = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 1,
        runLevelsInParallel: true,
        timeoutHours: 8,
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW)
        ],
        testGroup: 'pt_perf_highprio',
        testNames: [
            'SP_Perf_Gameplay_MinSpec_FirstBatch',
            'SP_Perf_Gameplay_MinSpec_SecondBatch',
        ],
        extraArgs: [EXTRA_ARGS_V3, '--override-server-build-configuration', 'final', EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING].flatten(),
        needGameServer: true,
    )

    private final TestInfo pt_perf_minspec_sp_final = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 1,
        runLevelsInParallel: true,
        timeoutHours: 8,
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW)
        ],
        testGroup: 'pt_perf_highprio',
        testNames: [
            'SP_Mem_Gameplay_MinSpec_FirstBatch',
            'SP_Mem_Gameplay_MinSpec_SecondBatch',
        ],
        extraArgs: [EXTRA_ARGS_V3, '--override-server-build-configuration', 'final', EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING].flatten(),
        needGameServer: true,
    )

    private final TestInfo pt_perf_recspec_sp_perf_performance_firstbatch = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 1,
        runLevelsInParallel: true,
        timeoutHours: 10,
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW)
        ],
        testGroup: 'pt_perf_highprio',
        testNames: [
            'SP_Perf_Gameplay_RecSpec_FirstBatch',
        ],
        extraArgs: [EXTRA_ARGS_V3, '--override-server-build-configuration', 'final', EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING].flatten(),
        needGameServer: true,
    )

    private final TestInfo pt_perf_recspec_sp_perf_performance_secondbatch = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 1,
        runLevelsInParallel: true,
        timeoutHours: 10,
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW)
        ],
        testGroup: 'pt_perf_highprio',
        testNames: [
            'SP_Perf_Gameplay_RecSpec_SecondBatch',
        ],
        extraArgs: [EXTRA_ARGS_V3, '--override-server-build-configuration', 'final', EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING].flatten(),
        needGameServer: true,
    )

    private final TestInfo pt_perf_recspec_sp_final_FirstBatch = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 1,
        runLevelsInParallel: true,
        timeoutHours: 12,
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW)
        ],
        testGroup: 'pt_perf_highprio',
        testNames: [
            'SP_Mem_Gameplay_RecSpec_FirstBatch',
        ],
        extraArgs: [EXTRA_ARGS_V3, '--override-server-build-configuration', 'final', '--max-parallel-tests', 1, EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING].flatten(),
        needGameServer: true,
    )

    private final TestInfo pt_perf_recspec_sp_final_SecondBatch = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 1,
        runLevelsInParallel: true,
        timeoutHours: 12,
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW)
        ],
        testGroup: 'pt_perf_highprio',
        testNames: [
            'SP_Mem_Gameplay_RecSpec_SecondBatch',
        ],
        extraArgs: [EXTRA_ARGS_V3, '--override-server-build-configuration', 'final', '--max-parallel-tests', 1, EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING].flatten(),
        needGameServer: true,
    )

    private final TestInfo pt_perf_sp_perf_performance_FirstBatch = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 3,
        runLevelsInParallel: true,
        timeoutHours: 13,
        platforms: REGIONAL_BCT_PLATFORMS,
        testGroup: 'pt_perf_highprio',
        testNames: [
            'SP_Perf_Gameplay_FirstBatch',
        ],
        extraArgs: [EXTRA_ARGS_V3, '--override-server-build-configuration', 'final', EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING].flatten(),
        needGameServer: true,
    )

    private final TestInfo pt_perf_sp_perf_performance_SecondBatch = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 3,
        runLevelsInParallel: true,
        timeoutHours: 13,
        platforms: REGIONAL_BCT_PLATFORMS,
        testGroup: 'pt_perf_highprio',
        testNames: [
            'SP_Perf_Gameplay_SecondBatch',
        ],
        extraArgs: [EXTRA_ARGS_V3, '--override-server-build-configuration', 'final', EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING].flatten(),
        needGameServer: true,
    )
/*
    private final TestInfo pt_perf_sp_perf_performance = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 3,
        runLevelsInParallel: true,
        timeoutHours: 13,
        platforms: REGIONAL_BCT_PLATFORMS,
        testGroup: 'pt_perf_highprio',
        testNames: [
            'SP_Perf_Gameplay_SecondBatch',
            'SP_Perf_Gameplay_FirstBatch',
        ],
        extraArgs: [EXTRA_ARGS_V3, '--override-server-build-configuration', 'final', EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING].flatten(),
        needGameServer: true,
    )
*/
    private final TestInfo pt_perf_trinity_sp_perf_performance = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 1,
        runLevelsInParallel: true,
        timeoutHours: 9,
        platforms: [
            new Platform(name: Name.PS5, region: Region.DEV),
        ],
        testGroup: 'pt_perf_highprio',
        testNames: [
            'SP_Perf_Gameplay_Trinity_FirstBatch',
            'SP_Perf_Gameplay_Trinity_SecondBatch',
        ],
        extraArgs: [EXTRA_ARGS_V3, '--override-server-build-configuration', 'final', EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING].flatten(),
        needGameServer: true,
    )

    private final TestInfo pt_perf_trinity_sp_final = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 1,
        runLevelsInParallel: true,
        timeoutHours: 8,
        platforms: [
            new Platform(name: Name.PS5, region: Region.DEV),
        ],
        testGroup: 'pt_perf_highprio',
        testNames: [
            'SP_Mem_Gameplay_Trinity_FirstBatch',
            'SP_Mem_Gameplay_Trinity_SecondBatch',
        ],
        extraArgs: [EXTRA_ARGS_V3, '--override-server-build-configuration', 'final', EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING, '--max-parallel-tests', 2].flatten(),
        needGameServer: true,
    )

    private final TestInfo pt_mem_trinity_final = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 1,
        runLevelsInParallel: true,
        timeoutHours: 8,
        platforms: [
            new Platform(name: Name.PS5, region: Region.DEV),
        ],
        testGroup: 'pt_perf_highprio',
        testNames: [
			'MP_Mem_Gameplay_Trinity:MemoryPlayTest_MP_Abbasid_Breakthrough0:MemoryPlayTest_MP_Abbasid_Conquest0:MemoryPlayTest_MP_Abbasid_Rush0:MemoryPlayTest_MP_Abbasid_SquadDeathMatch0:MemoryPlayTest_MP_Abbasid_KingOfTheHill0:MemoryPlayTest_MP_Abbasid_Domination0',
			'MP_Mem_Gameplay_Trinity:MemoryPlayTest_MP_Aftermath_Conquest0:MemoryPlayTest_MP_Aftermath_Rush0:MemoryPlayTest_MP_Aftermath_KingOfTheHill0:MemoryPlayTest_MP_Aftermath_Breakthrough0:MemoryPlayTest_MP_Aftermath_Domination0',
			'MP_Mem_Gameplay_Trinity:MemoryPlayTest_MP_Battery_Rush0:MemoryPlayTest_MP_Battery_SquadDeathMatch0:MemoryPlayTest_MP_Battery_KingOfTheHill0:MemoryPlayTest_MP_Battery_Domination0:MemoryPlayTest_MP_Battery_Conquest0',
			'MP_Mem_Gameplay_Trinity:MemoryPlayTest_MP_Capstone_Conquest0:MemoryPlayTest_MP_Capstone_Breakthrough0:MemoryPlayTest_MP_Capstone_Rush0:MemoryPlayTest_MP_Capstone_SquadDeathMatch0:MemoryPlayTest_MP_Capstone_KingOfTheHill0:MemoryPlayTest_MP_Capstone_Domination0',
        ],
        extraArgs: [EXTRA_ARGS_V3, '--override-server-build-configuration', 'final', EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING].flatten(),
        needGameServer: true,
    )

    private final TestInfo pt_mem_trinity_final_release = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 1,
        runLevelsInParallel: true,
        timeoutHours: 8,
        platforms: [
            new Platform(name: Name.PS5, region: Region.DEV),
        ],
        testGroup: 'pt_perf_highprio',
        testNames: [
			'MP_Mem_Gameplay_Trinity:MemoryPlayTest_MP_Abbasid_Breakthrough0:MemoryPlayTest_MP_Abbasid_Conquest0:MemoryPlayTest_MP_Abbasid_Rush0:MemoryPlayTest_MP_Abbasid_SquadDeathMatch0:MemoryPlayTest_MP_Abbasid_KingOfTheHill0:MemoryPlayTest_MP_Abbasid_Domination0',
			'MP_Mem_Gameplay_Trinity:MemoryPlayTest_MP_Aftermath_Conquest0:MemoryPlayTest_MP_Aftermath_Rush0:MemoryPlayTest_MP_Aftermath_KingOfTheHill0:MemoryPlayTest_MP_Aftermath_Breakthrough0:MemoryPlayTest_MP_Aftermath_Domination0',
			'MP_Mem_Gameplay_Trinity:MemoryPlayTest_MP_Battery_Rush0:MemoryPlayTest_MP_Battery_SquadDeathMatch0:MemoryPlayTest_MP_Battery_KingOfTheHill0:MemoryPlayTest_MP_Battery_Domination0:MemoryPlayTest_MP_Battery_Conquest0',
			'MP_Mem_Gameplay_Trinity:MemoryPlayTest_MP_Capstone_Conquest0:MemoryPlayTest_MP_Capstone_Breakthrough0:MemoryPlayTest_MP_Capstone_Rush0:MemoryPlayTest_MP_Capstone_SquadDeathMatch0:MemoryPlayTest_MP_Capstone_KingOfTheHill0:MemoryPlayTest_MP_Capstone_Domination0',
        ],
        extraArgs: [EXTRA_ARGS_V3, '--override-server-build-configuration', 'final', EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING].flatten(),
        needGameServer: true,
    )

    private final TestInfo pt_perf_ultspec_sp_perf_performance_firstbatch = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 1,
        runLevelsInParallel: true,
        timeoutHours: 8,
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW)
        ],
        testGroup: 'pt_perf_highprio',
        testNames: [
            'SP_Perf_Gameplay_UltSpec_FirstBatch',
        ],
        extraArgs: [EXTRA_ARGS_V3, '--override-server-build-configuration', 'final', EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING].flatten(),
        needGameServer: true,
    )

    private final TestInfo pt_perf_ultspec_sp_perf_performance_secondbatch = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 1,
        runLevelsInParallel: true,
        timeoutHours: 8,
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW)
        ],
        testGroup: 'pt_perf_highprio',
        testNames: [
            'SP_Perf_Gameplay_UltSpec_SecondBatch',
        ],
        extraArgs: [EXTRA_ARGS_V3, '--override-server-build-configuration', 'final', EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING].flatten(),
        needGameServer: true,
    )

    private final TestInfo pt_perf_xbss_sp_perf_performance = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 1,
        runLevelsInParallel: true,
        timeoutHours: 8,
        platforms: [
            new Platform(name: Name.XBSX, region: Region.WW),
        ],
        testGroup: 'pt_perf_highprio',
        testNames: [
            'SP_Perf_Gameplay_XBSS_FirstBatch',
            'SP_Perf_Gameplay_XBSS_SecondBatch',
        ],
        extraArgs: [EXTRA_ARGS_V3, '--override-server-build-configuration', 'final', EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING].flatten(),
        needGameServer: true,
    )

    private final TestInfo pt_perf_xbss_meta_performance_setup = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 1,
        runLevelsInParallel: true,
        timeoutHours: 8,
        platforms: [
            new Platform(name: Name.XBSX, region: Region.WW),
        ],
        testGroup: 'pt_perf_highprio',
        testNames: [
            'Meta_Perf_FrontEnd_XBSS',
        ],
        extraArgs: [EXTRA_ARGS_V3, '--override-server-build-configuration', 'final', EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING].flatten(),
        needGameServer: true,
    )

    private final TestInfo pt_perf_xbss_sp_final = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 1,
        runLevelsInParallel: true,
        timeoutHours: 9,
        platforms: [
            new Platform(name: Name.XBSX, region: Region.WW),
        ],
        testGroup: 'pt_perf_highprio',
        testNames: [
            'SP_Mem_Gameplay_XBSS_FirstBatch',
            'SP_Mem_Gameplay_XBSS_SecondBatch',
        ],
        extraArgs: [EXTRA_ARGS_V3, '--override-server-build-configuration', 'final', EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING].flatten(),
        needGameServer: true,
    )

    private final TestInfo pt_perf_recspec_performance_ch1_release = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 1,
        runLevelsInParallel: true,
        timeoutHours: 11,
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW)
        ],
        testGroup: 'pt_perf',
        testNames: [
            'F2P_Perf_Gameplay_RecSpec',
            'F2P_Perf_Gameplay_GauntletOnLargest_RecSpec',
        ],
        extraArgs: [EXTRA_ARGS_V3, '--override-server-build-configuration', 'final', '--max-parallel-tests', 2, EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING].flatten(),
        needGameServer: true,
    )
/*
    private final TestInfo pt_perf_recspec_highpriority_performance_setup_AlphaValidation_RecSpec_Coverage = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 1,
        runLevelsInParallel: true,
        timeoutHours: 15,
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW)
        ],
        testGroup: 'pt_perf_highprio',
        testNames: [
            'MP_Perf_Gameplay_AlphaValidation_RecSpec_Coverage',
        ],
        extraArgs: [EXTRA_ARGS_V3, '--override-server-build-configuration', 'final', EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING].flatten(),
        needGameServer: true,
    )*/
/*
    private final TestInfo pt_perf_recspec_highpriority_performance_setup_AlphaValidation_RecSpec_HighPrio = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 1,
        runLevelsInParallel: true,
        timeoutHours: 15,
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW)
        ],
        testGroup: 'pt_perf_highprio',
        testNames: [
            'MP_Perf_Gameplay_AlphaValidation_RecSpec_HighPrio',
        ],
        extraArgs: [EXTRA_ARGS_V3, '--override-server-build-configuration', 'final', EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING].flatten(),
        needGameServer: true,
    )*/
/*
    private final TestInfo pt_perf_recspec_highpriority_performance_setup_AlphaValidation_RecSpec_HighPrio_ch1_release = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 1,
        runLevelsInParallel: true,
        timeoutHours: 15,
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW)
        ],
        testGroup: 'pt_perf_highprio',
        testNames: [
            'MP_Perf_Gameplay_AlphaValidation_RecSpec_HighPrio:AutoPlayTest_MP_Aftermath_Conquest0_Fidelity:AutoPlayTest_MP_Badlands_Conquest0_Fidelity:AutoPlayTest_MP_Dumbo_Conquest0_Fidelity:AutoPlayTest_MP_Firestorm_Conquest0_Fidelity:AutoPlayTest_MP_Outskirts_Conquest0_Fidelity:AutoPlayTest_MP_Tungsten_Conquest0_Fidelity:AutoPlayTest_MP_Aftermath_Conquest0_Performance:AutoPlayTest_MP_Badlands_Conquest0_Performance:AutoPlayTest_MP_Dumbo_Conquest0_Performance:AutoPlayTest_MP_Firestorm_Conquest0_Performance:AutoPlayTest_MP_Outskirts_Conquest0_Performance:AutoPlayTest_MP_Tungsten_Conquest0_Performance',
        ],
        extraArgs: [EXTRA_ARGS_V3, '--override-server-build-configuration', 'final', EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING].flatten(),
        needGameServer: true,
    )
*/
    private final TestInfo pt_perf_recspec_highpriority_performance_setup_AlphaValidation_RecSpec_BFLabs = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 1,
        runLevelsInParallel: true,
        timeoutHours: 15,
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW)
        ],
        testGroup: 'pt_perf_highprio',
        testNames: [
            'MP_Perf_Gameplay_AlphaValidation_RecSpec_BFLabs',
        ],
        extraArgs: [EXTRA_ARGS_V3, '--override-server-build-configuration', 'final', EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING].flatten(),
        needGameServer: true,
    )

    private final TestInfo pt_perf_recspec_highpriority_performance_setup_AlphaValidation_RecSpec_BFLabs_release = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 1,
        runLevelsInParallel: true,
        timeoutHours: 15,
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW)
        ],
        testGroup: 'pt_perf_highprio',
        testNames: [
            'MP_Perf_Gameplay_AlphaValidation_RecSpec_BFLabs',
        ],
        extraArgs: [EXTRA_ARGS_V3, '--override-server-build-configuration', 'final', EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING].flatten(),
        needGameServer: true,
    )

/*
    private final TestInfo pt_perf_recspec_highpriority_performance_setup_eala = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 1,
        runLevelsInParallel: true,
        timeoutHours: 10,
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW)
        ],
        testGroup: 'pt_perf_highprio',
        testNames: [
            'MP_Perf_Gameplay_RecSpec',
        ],
        extraArgs: [EXTRA_ARGS_V3, '--override-server-build-configuration', 'final', EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING].flatten(),
        needGameServer: true,
    )
*/
    private final TestInfo pt_perf_recspec_highpriority_final_setup = new TestInfo(
        slackChannel: '#bf-sqr-bct-notify',
        parallelLimit: 1,
        runLevelsInParallel: true,
        timeoutHours: 10,
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW)
        ],
        testGroup: 'pt_perf_highprio',
        testNames: [
            'F2P_Perf_Flythrough_RecSpec_Detailed',
            'MP_Perf_Flythrough_Detailed_RecSpec',
            'SP_Perf_Flythrough_Detailed_RecSpec',
            'F2P_Perf_Portal_RecSpec_Detailed',
            'F2P_Perf_Flythrough_RecSpec',
        ],
        extraArgs: [EXTRA_ARGS_V3, '--override-server-build-configuration', 'final', EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING].flatten(),
        needGameServer: true,
    )

    private final TestInfo pt_func_mutators = new TestInfo(
        platforms: REGIONAL_BCT_PLATFORMS,
        parallelLimit: 3,
        runLevelsInParallel: true,
        testGroup: 'pt_func_portal',
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 6,
        testNames: [
            'F2P_Func_Mutators_1',
            'F2P_Func_Mutators_2',
            'F2P_Func_Mutators_3',
            'F2P_Func_Mutators_4',
            'F2P_Func_ModbuilderLibrary_Basic',
            'F2P_Func_SpatialEditor',
            'F2P_Func_ModbuilderLibrary_Soldier',
            'F2P_Func_ModbuilderLibrary_SpatialEditor',
            'F2P_Func_ModbuilderLibrary_Unique',
            'F2P_Func_ModBuilderLibrary_Vehicle',
        ],
        extraArgs: EXTRA_ARGS_V2,
    )

    private final TestInfo release_setup_aitests_DICEAI = new TestInfo(
        slackChannel: '#gla-gaia-autotests',
        platforms: REGIONAL_BCT_PLATFORMS,
        parallelLimit: 3,
        runLevelsInParallel: true,
        testGroup: 'featuretests',
        timeoutHours: 4,
        testNames: ['SP_Features', 'DICEAI_Vehicle', 'DICEAI_AllVehiclesTest'],
        extraArgs: EXTRA_ARGS_V2,
        needGameServer: false,
    )

    private final TestInfo frostedtests_workflow_frostbite = new TestInfo(
        testGroup: 'frostedtests_workflow_frostbite',
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 6,
        platforms: [new Platform(name: Name.WIN64)],
        testNames: ['frosted_scene_workflow_tests',
                    'frosted_efw_core_workflow_tests',
                    'frosted_fbscript_workflow_tests',
                    'frosted_assetGraph_workflow_tests',
                    'frosted_vfx_workflow_tests',
                    'frosted_sourceGeoMesh_workflow_tests',
                    'fbapi_integration_tests'],
        extraArgs: ['--superbundles', 'false', '--fbapi-test-frosted-args', '"-includeTestDataModules true"', '--fbapi-test-frosted-launch-wait-seconds', 480, '--fbapi-test-pytest-test-timeout-seconds', 600, '--fbapi-test-running-timeout-seconds', 1200],
        poolType: '',
    )

    private final TestInfo frostedtests_workflow_battlefield = new TestInfo(
        testGroup: 'frostedtests_workflow_battlefield',
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 6,
        platforms: [new Platform(name: Name.WIN64)],
        testNames: ['frosted_bf_techdesigncore_workflow_tests',
                    'frosted_bf_techart_workflow_tests',
                    'frosted_bf_hardware_workflow_tests',
                    'frosted_bf_hardwareweapons_workflow_tests',
                    'frosted_bf_cinematics_workflow_tests',
                    'frosted_bf_character_workflow_tests',
                    'frosted_bf_ui_workflow_tests'],
        extraArgs: ['--superbundles', 'false', '--fbapi-test-frosted-args', '"-includeTestDataModules true"', '--fbapi-test-frosted-launch-wait-seconds', 480, '--fbapi-test-pytest-test-timeout-seconds', 600, '--fbapi-test-running-timeout-seconds', 1200],
        poolType: '',
    )

    private final TestInfo frostedTestsCH1_2024_TO_CH1 = new TestInfo(
        testGroup: 'frostedtests',
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 6,
        platforms: [new Platform(name: Name.WIN64)],
        testNames: [
            'FrostEdTest_BFWorkflowsTechArt',
        ],
        extraArgs: ['--frosted-runtime-args', '"-includeTestDataModules true -QV.Enable false"', '--frosted-launch-timeout', 600, '--frosted-test-timeout', 480, '--alltests', '--databaseId', "BattlefieldGameData.${CH1_2024_TO_CH1}.Win32.Debug"],
        poolType: ''
    )
/*
    private final TestInfo frostedTestsCH1release = new TestInfo(
        testGroup: 'frostedtests',
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 6,
        platforms: [new Platform(name: Name.WIN64)],
        testNames: [
            'FrostEdTest_BFWorkflowsTechArt',
        ],
        extraArgs: ['--frosted-runtime-args', '"-includeTestDataModules true -QV.Enable false"', '--frosted-launch-timeout', 600, '--frosted-test-timeout', 480, '--alltests', '--databaseId', "BattlefieldGameData.${CH1_RELEASE}.Win32.Debug"],
        poolType: ''
    )
*/
    private final TestInfo unittests_asan_tool = new TestInfo(
        platforms: [
            new Platform(name: Name.WIN64DLL, region: Region.WW),
        ],
        parallelLimit: 2,
        hasPinnedAgents: false,
        runLevelsInParallel: true,
        testGroup: 'unittests',
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 5,
        useLatestDrone: true,
        testNames: ['AllUnitTests(sanitizer=ASAN)'],
        extraArgs: [EXTRA_ARGS_SANITIZERS].flatten(),
        fetchTests: true,
        poolType: ''
    )

    private final TestInfo unittests_asan_static = new TestInfo(
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW),
            new Platform(name: Name.PS5, region: Region.DEV),
            new Platform(name: Name.XBSX, region: Region.WW),
        ],
        parallelLimit: 3,
        hasPinnedAgents: false,
        runLevelsInParallel: true,
        testGroup: 'unittests',
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 5,
        useLatestDrone: true,
        tests: [
            new TestSuite(name: 'AllUnitTests(sanitizer=ASAN)',
                platforms: [new Platform(name: Name.WIN64)],
                extraArgs: [EXTRA_ARGS_SANITIZERS].flatten()),
            new TestSuite(name: 'AllUnitTests(sanitizer=ASAN)',
                platforms: [new Platform(name: Name.XBSX)],
                extraArgs: [EXTRA_ARGS_SANITIZERS, '--max-parallel-tests', 2].flatten()),
            new TestSuite(name: 'AllUnitTests(sanitizer=ASAN)',
                platforms: [new Platform(name: Name.PS5)],
                extraArgs: [EXTRA_ARGS_SANITIZERS_PS5, '--max-parallel-tests', 2].flatten()),
        ],
        fetchTests: true,
    )

    private final TestInfo unittests_ubsan_static = new TestInfo(
        platforms: [
            new Platform(name: Name.PS5, region: Region.DEV),
        ],
        parallelLimit: 1,
        hasPinnedAgents: false,
        runLevelsInParallel: true,
        testGroup: 'unittests',
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 5,
        useLatestDrone: true,
        testNames: ['AllUnitTests(sanitizer=UBSAN)'],
        extraArgs: [EXTRA_ARGS_SANITIZERS_PS5, '--max-parallel-tests', 2].flatten(),
        fetchTests: true,
    )

    private final TestInfo lkg_qv_win = new TestInfo(
        parallelLimit: 1,
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW),
        ],
        runLevelsInParallel: true,
        testGroup: 'lkg_qv',
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 4,
        testNames: ['LKG_AutoPlayers'],
        extraArgs: EXTRA_ARGS_V1,
    )

    private final TestInfo lkg_qv_win_criterion = new TestInfo(
        parallelLimit: 1,
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW),
        ],
        runLevelsInParallel: true,
        testGroup: 'lkg_qv',
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 4,
        testNames: ['LKG_AutoPlayers'],
        extraArgs: [EXTRA_ARGS_V1, EXTRA_ARGS_CRITERION].flatten(),
    )

    private final TestInfo lkg_qv_ECS = new TestInfo(
        parallelLimit: 3,
        runLevelsInParallel: true,
        testGroup: 'lkg_qv',
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 6,
        tests: [
            new TestSuite(
                name: 'LKG_AutoPlayers',
                extraArgs: EXTRA_ARGS_V1,
                platforms: BCT_PLATFORMS,
            ),
        ]
    )
/*
    private final TestInfo pt_stab = new TestInfo(
        platforms: REGIONAL_BCT_PLATFORMS,
        parallelLimit: 3,
        runLevelsInParallel: true,
        testGroup: 'pt_stab',
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 6,
        tests: [
            new TestSuite(name: 'F2P_Stab_Gameplay', extraArgs: EXTRA_ARGS_V2),
            new TestSuite(name: 'MP_Stab_Gameplay', extraArgs: EXTRA_ARGS_V2),
        ]
    )*/
/*
    private final TestInfo mp_stab_test_crash = new TestInfo(
        platforms: [
            new Platform(name: Name.PS5, region: Region.DEV)
        ],
        parallelLimit: 1,
        runLevelsInParallel: true,
        testGroup: 'pt_stab',
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 5,
        tests: [
            new TestSuite(name: 'MP_Stab_Gameplay', extraArgs: ['--timeout-client-level-load', '00:30:00', '--runtime-connect-timeout', 360, '--timeout-client-deploy', '01:10:00', '--atf-rest-heartbeat-enabled', 'true', '--xbsx-skip-defer-exec', 'true', '--kill-subprocesses-on-exit', 'true', '--client-runtime-debug-args', '"-trace -memtrack -traceHost *************"'].flatten()),
        ]
    )
*/
    private final TestInfo pt_stab_soak_mp_ch1_code = new TestInfo(
        platforms: [
            new Platform(name: Name.LINUX64, region: Region.WW),
        ],
        parallelLimit: 1,
        runLevelsInParallel: true,
        testGroup: 'large_scale',
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 12,
        tests: [
            new TestSuite(name: 'MP_Stab_Gameplay_Soak', extraArgs: [EXTRA_ARGS_L_SCALE_MP, EXTRA_ARGS_SHIFT_CH1_CONTENT_DEV, EXTRA_ARGS_SOAK, '--soaking-test-running-time', '03:00:00', EXTRA_ARGS_REMOTE_FILE_SHARE_HANDLING].flatten()),
        ]
    )
/*
    private final TestInfo pt_stab_soak_ps5 = new TestInfo(
        platforms: [
            new Platform(name: Name.PS5, region: Region.DEV),
        ],
        parallelLimit: 1,
        runLevelsInParallel: true,
        testGroup: 'pt_stab',
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 10,
        tests: [
            new TestSuite(name: 'SP_Stab_Gameplay_Soak', extraArgs: [EXTRA_ARGS_V2, '--test-state-change-timeout', 2880, '--soaking-test-running-time', '03:00:00', '--ensemble-grpc', true].flatten()),
        ]
    )

    private final TestInfo pt_stab_soak_win = new TestInfo(
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW),
        ],
        parallelLimit: 1,
        runLevelsInParallel: true,
        testGroup: 'pt_stab',
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 10,
        tests: [
            new TestSuite(name: 'SP_Stab_Gameplay_Soak', extraArgs: [EXTRA_ARGS_V2, '--test-state-change-timeout', 2880, '--soaking-test-running-time', '03:00:00', '--ensemble-grpc', true].flatten()),
        ]
    )

    private final TestInfo pt_stab_soak_xbsx = new TestInfo(
        platforms: [
            new Platform(name: Name.XBSX, region: Region.WW),
        ],
        parallelLimit: 1,
        runLevelsInParallel: true,
        testGroup: 'pt_stab',
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 10,
        tests: [
            new TestSuite(name: 'SP_Stab_Gameplay_Soak', extraArgs: [EXTRA_ARGS_V2, '--test-state-change-timeout', 2880, '--soaking-test-running-time', '03:00:00', '--ensemble-grpc', true].flatten()),
        ]
    )
*/
    private final TestInfo lkg_checkmate_beta = new TestInfo(
        platforms: [
            new Platform(name: Name.WIN64),
        ],
        parallelLimit: 1,
        runLevelsInParallel: true,
        testGroup: 'lkgcheckmate_beta',
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 2,
        tests: [
            new TestSuite(name: 'CheckMate_Mandatory_TestSuite_Beta', extraArgs: [EXTRA_ARGS_V1, '--client-runtime-debug-args', '"-trace -perftrack"'].flatten())
        ]
    )

    private final TestInfo lkg_checkmate_alpha = new TestInfo(
        platforms: [
            new Platform(name: Name.WIN64),
        ],
        parallelLimit: 1,
        runLevelsInParallel: true,
        testGroup: 'lkgcheckmate_alpha',
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 2,
        tests: [
            new TestSuite(name: 'CheckMate_Mandatory_TestSuite_Alpha', extraArgs: [EXTRA_ARGS_V1, '--client-runtime-debug-args', '"-trace -perftrack"'].flatten())
        ]
    )

    private final TestInfo unittests = new TestInfo(
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW)
        ],
        parallelLimit: 1,
        hasPinnedAgents: true,
        runLevelsInParallel: true,
        testGroup: 'unittests',
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 5,
        useLatestDrone: true,
        tests: [
            new TestSuite(name: 'AutoPlayersCodeTests', extraArgs: [EXTRA_ARGS_V2, '--max-parallel-tests', 1].flatten()),
        ]
    )

    private final TestInfo unittests_engine = new TestInfo(
        platforms: [
            new Platform(name: Name.TOOL, region: Region.WW)
        ],
        parallelLimit: 1,
        hasPinnedAgents: true,
        runLevelsInParallel: true,
        testGroup: 'unittests',
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 5,
        useLatestDrone: true,
        tests: [
            new TestSuite(name: 'EngineUnitTests', extraArgs: [EXTRA_ARGS_V3, '--max-parallel-tests', 1].flatten(), poolType: ''),
        ]
    )

    private final TestInfo pt_func_SP = new TestInfo(
        platforms: REGIONAL_BCT_PLATFORMS,
        parallelLimit: 6,
        runLevelsInParallel: true,
        testGroup: 'pt_func',
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 15,
        testNames: [
            'SP_Func_Playthroughs',
            'SP_Func_Cinematics',
        ],
        extraArgs: EXTRA_ARGS_V2,
    )

    private final TestInfo pt_func_A2B_one = new TestInfo(
        platforms: REGIONAL_BCT_PLATFORMS,
        parallelLimit: 6,
        runLevelsInParallel: true,
        testGroup: 'pt_func_A2B',
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 15,
        testNames: [
            'A2B_Vehicles',
            'A2B_F2P_Weapons',
            'A2B_MMT_GameModes',
            'A2B_F2P_BR_Missions',
            'A2B_F2P_CoreModes',
        ],
        extraArgs: EXTRA_ARGS_V2,
    )

    private final TestInfo pt_func_A2B_two = new TestInfo(
        platforms: REGIONAL_BCT_PLATFORMS,
        parallelLimit: 6,
        runLevelsInParallel: true,
        testGroup: 'pt_func_A2B',
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 15,
        testNames: [
            'A2B_WeaponsGadgets',
            'A2B_F2P_Gadgets',
            'A2B_F2P_Vehicles',
            'A2B_F2P_Gameplay',
            'A2B_Attachments',
        ],
        extraArgs: EXTRA_ARGS_V2,
    )

    private final TestInfo pt_func_CC = new TestInfo(
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW),
        ],
        parallelLimit: 1,
        runLevelsInParallel: true,
        testGroup: 'pt_func',
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 10,
        testNames: [
            'CC_Func_Main',
        ],
        extraArgs: EXTRA_ARGS_V2,
    )

    private final TestInfo pt_func_META = new TestInfo(
        platforms: REGIONAL_BCT_PLATFORMS,
        parallelLimit: 6,
        runLevelsInParallel: true,
        testGroup: 'pt_func',
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 15,
        testNames: [
            'META_Func_MyJourney',
            'META_Func_GameFlow',
        ],
        extraArgs: ['--enable-nested-test-case-reporting-to-file', 'true', EXTRA_ARGS_V2].flatten()
    )

/*
    private final TestInfo pt_func_temp = new TestInfo(
        platforms: REGIONAL_BCT_PLATFORMS,
        parallelLimit: 6,
        runLevelsInParallel: true,
        testGroup: 'pt_func',
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 15,
        testNames: [
            'SP_Func_Playthroughs',
        ],
        extraArgs: EXTRA_ARGS_V2,
    )
*/
    private final TestInfo pt_func_content = new TestInfo(
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW),
        ],
        parallelLimit: 6,
        runLevelsInParallel: true,
        testGroup: 'pt_func',
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 13,
        testNames: [
            'TTK_Hardware',
        ],
        extraArgs: EXTRA_ARGS_V2,
    )

    private final TestInfo pt_func_win64 = new TestInfo(
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW),
        ],
        parallelLimit: 1,
        runLevelsInParallel: true,
        testGroup: 'pt_func',
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 4,
        tests: [
            new TestSuite(name: 'Meta_Func_Critpath', extraArgs: EXTRA_ARGS_V2),
            new TestSuite(name: 'GadgetDamageInfo', extraArgs: EXTRA_ARGS_V2),
        ]
    )

    private final TestInfo releasetests = new TestInfo(
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW),
            new Platform(name: Name.XBSX, region: Region.WW),
        ],
        parallelLimit: 2,
        runLevelsInParallel: true,
        testGroup: 'featuretests',
        slackChannel: '#dice-ui-test-status',
        timeoutHours: 2,
        tests: [
            new TestSuite(name: 'BFUIElements', extraArgs: EXTRA_ARGS_V2, needGameServer: false),
        ]
    )

    private final TestInfo persistence_final_setup = new TestInfo(
        testGroup: 'persistence_final_tests',
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 6,
        platforms: [new Platform(name: Name.WIN64, region: Region.WW)],
        tests: [
            new TestSuite(name: 'A2B_MP_Persistence_FL', extraArgs: [EXTRA_ARGS_V2].flatten())
        ]
    )

    private final TestInfo frosted_vstests = new TestInfo(
        testGroup: 'frostedtests',
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 8,
        platforms: [new Platform(name: Name.WIN64)],
        useLatestDrone: true,
        tests: [
            new TestSuite(name: 'AllVSTests', fetchTests: true, poolType: ''),
        ]
    )

    private final Map collectingSlackSettings = [
        (DEFAULT)                   : [channels: [], skip_for_multiple_failures: true],
        (CH1_CODE_DEV)              : bctSlackSettings,
        (CH1_CONTENT_DEV)           : bctSlackSettings,
        (CH1_CONTENT_DEV_SANITIZERS): bctSlackSettings,
        (CH1_TO_TRUNK)              : bctSlackSettings,
        (CH1_2024_TO_CH1)           : bctSlackSettings,
        (CH1_STAGE)                 : bctSlackSettings,
        (CH1_RELEASE)               : bctSlackSettings,
        (TASK2)                     : bctSlackSettings,
        (ECS_SPLINES)               : bctSlackSettings,
        (CH1_CONTENT_DEV_C1S2B1)    : bctSlackSettings,
    ]

    /* ********** ********** ********** ********** **********
    Define if the levels should be tested in parallel or not.
    */

    private final Map runLevelsInParallel = [
        (DEFAULT)                   : true,
        (CH1_CODE_DEV)              : false,
        (CH1_CONTENT_DEV)           : false,
        (CH1_CONTENT_DEV_SANITIZERS): false,
        (CH1_TO_TRUNK)              : false,
        (CH1_2024_TO_CH1)           : false,
        (CH1_STAGE)                 : false,
        (CH1_RELEASE)               : false,
        (TASK2)                     : false,
        (ECS_SPLINES)               : false,
        (CH1_CONTENT_DEV_C1S2B1)    : false,
    ]

    @Override
    List<String> getBranches() {
        return [CH1_CODE_DEV, CH1_CONTENT_DEV, CH1_TO_TRUNK, CH1_2024_TO_CH1, CH1_CONTENT_DEV_SANITIZERS, CH1_STAGE, CH1_RELEASE, TASK2, ECS_SPLINES, CH1_CONTENT_DEV_C1S2B1]
    }

    @Override
    List<AutotestCategory> getTestCategories() {
        List<AutotestCategory> categories = []
        categories += testCategories1
        categories += testCategories2
        categories += testCategories3
        categories += testCategories4
        categories += testCategories5
        return categories
    }

    List<AutotestCategory> getTestCategories1() {
        List<AutotestCategory> categories = []
        categories += new AutotestCategory(
            name: 'lkg_auto',
            testDefinition: 'lkg_auto',
            enableP4Counters: true,
            runBilbo: true,
            needGameServer: true,
            uploadJournals: true,
            captureVideo: true,
            branches: [
                branchConfiguration(CH1_2024_TO_CH1, lkg_auto, 'H 1,5,19,23 * * 1-7'),
                branchConfiguration(CH1_STAGE, lkg_auto_nonpinned_agents, 'H 2,7,14,21 * * 1-7'),
                branchConfiguration(CH1_RELEASE, lkg_auto_release, 'H 0,5,15,21 * * 1-7'),
                branchConfiguration(TASK2, lkg_auto_nonpinned_agents, 'H 3,5,15,22 * * 1-7'),
            ]
        )
        categories += new AutotestCategory(
            name: 'lkg_auto_ScratchDisk',
            testDefinition: 'lkg_auto_ScratchDisk',
            enableP4Counters: true,
            runBilbo: true,
            needGameServer: true,
            uploadJournals: true,
            captureVideo: true,
            branches: [
                branchConfiguration(CH1_CODE_DEV, lkg_auto_ScratchDisk, 'H * * * 1-7'),
                branchConfiguration(CH1_CONTENT_DEV, lkg_auto_ScratchDisk, 'H * * * 1-7'),
            ]
        )
        categories += new AutotestCategory(
            name: 'lkg_auto_la_test',
            testDefinition: 'lkg_auto_la_test',
            enableP4Counters: true,
            remoteLabel: 'eala',
            runBilbo: true,
            needGameServer: true,
            uploadJournals: true,
            captureVideo: true,
            branches: [
                branchConfiguration(CH1_TO_TRUNK, lkg_auto_la_test, 'H H/3 * * 1-7'),
                branchConfiguration(CH1_CONTENT_DEV, lkg_auto_la_test, 'H * * * 1-7'),
            ]
        )
        categories += new AutotestCategory(
            name: 'lkg_bootanddeploy_ScratchDisk',
            testDefinition: 'lkg_bootanddeploy_ScratchDisk',
            enableP4Counters: true,
            runBilbo: true,
            uploadJournals: true,
            captureVideo: true,
            branches: [
                branchConfiguration(CH1_CONTENT_DEV, lkg_bootanddeploy_ScratchDisk, 'H 2,5,8,14,17,20,22 * * 1-7'),
                branchConfiguration(CH1_CODE_DEV, lkg_bootanddeploy_ScratchDisk, 'H 0,3,6,9,12,15,21 * * 1-7'),
            ]
        )
        categories += new AutotestCategory(
            name: 'lkg_bootanddeploy',
            testDefinition: 'lkg_bootanddeploy',
            enableP4Counters: true,
            runBilbo: true,
            uploadJournals: true,
            captureVideo: true,
            branches: [
                branchConfiguration(CH1_TO_TRUNK, lkg_bootanddeploy, 'H 6,22 * * 1-7'),
                branchConfiguration(CH1_2024_TO_CH1, lkg_bootanddeploy, 'H 16,0 * * 1-7'),
            ]
        )
        categories += new AutotestCategory(
            name: 'lkg_bootanddeploy_tool',
            testDefinition: 'lkg_bootanddeploy_tool',
            enableP4Counters: true,
            runBilbo: true,
            buildType: 'dll',
            config: 'release',
            remoteLabel: 'eala',
            uploadJournals: true,
            captureVideo: true,
            branches: [
                branchConfiguration(CH1_CODE_DEV, lkg_bootanddeploy_tool, 'H 0,3,6,9,12,15,21 * * 1-7'),
                branchConfiguration(CH1_CONTENT_DEV, lkg_bootanddeploy_tool, 'H 1,4,7,10,13,16,19,22 * * 1-7'),
                branchConfiguration(CH1_2024_TO_CH1, lkg_bootanddeploy_tool, 'H 2,6,11,14,17,20 * * 1-7'),
                branchConfiguration(CH1_TO_TRUNK, lkg_bootanddeploy_tool, 'H 2,5,8,11,14,17,20,23 * * 1-7'),
            ]
        )
        categories += new AutotestCategory(
            name: 'LKG_AudioTestSuite',
            testDefinition: 'LKG_AudioTestSuite',
            enableP4Counters: true,
            runBilbo: true,
            needGameServer: true,
            uploadJournals: true,
            captureVideo: true,
            branches: [
                branchConfiguration(CH1_CONTENT_DEV, lkg_audiotestsuite, 'H 2 * * 1-7'),
            ]
        )
        categories += new AutotestCategory(
            name: 'pt_func_playtest',
            testDefinition: 'pt_func_playtest',
            runBilbo: true,
            needGameServer: true,
            uploadJournals: false,
            captureVideo: true,
            branches: [
                branchConfiguration(CH1_CODE_DEV, pt_func_playtest, 'H 8,14,20 * * 1-7'),
                branchConfiguration(CH1_CONTENT_DEV, pt_func_playtest, 'H H/4 * * 1-7'),
                branchConfiguration(CH1_2024_TO_CH1, pt_func_playtest, 'H 0,2,22 * * 1-7'),
            ]
        )
        categories += new AutotestCategory(
            name: 'pt_crossplay',
            testDefinition: 'pt_crossplay',
            config: 'final',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            useSpinBuild: true,
            isTestWithLooseFiles: true,
            useTempDeploymentIfLooseFiles: false,
            uploadJournals: false,
            runBilbo: false,
            branches: [
                branchConfiguration(CH1_CONTENT_DEV, pt_crossplay, 'H 12 * * 1-7'),
            ]
        )
        categories += new AutotestCategory(
            name: 'pt_crossplay_spin',
            testDefinition: 'pt_crossplay_spin',
            config: 'final',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            useSpinBuild: true,
            isTestWithLooseFiles: true,
            useTempDeploymentIfLooseFiles: false,
            uploadJournals: false,
            runBilbo: false,
            branches: [
                branchConfiguration(CH1_CONTENT_DEV, pt_crossplay_spin, 'H 14 * * 1-7'),
            ]
        )
        categories += new FrostedAutotestCategory(
            name: 'frostedtests',
            testDefinition: 'frostedtests',
            remoteLabel: 'eala',
            runBilbo: false,
            needGameServer: false,
            uploadJournals: false,
            branches: [
                branchConfiguration(CH1_CONTENT_DEV, frostedTestsCH1ContentDev, 'H 3,10,17,22 * * 1-7'),
                branchConfiguration(CH1_2024_TO_CH1, frostedTestsCH1_2024_TO_CH1, 'H 0,4,18 * * 1-7'),
                branchConfiguration(ECS_SPLINES, frostedTestsECS, 'H 0,2,20,22 * * 1-7'),
            ]
        )/*
        categories += new FrostedAutotestCategory(
            name: 'frostedtests_dice',
            testDefinition: 'frostedtests_dice',
            runBilbo: false,
            needGameServer: false,
            uploadJournals: false,
            branches: [
                branchConfiguration(CH1_RELEASE, frostedTestsCH1release, 'H 2,7,14 * * 1-7'),
            ]
        )*/
        categories += new AutotestCategory(
            name: 'lkg_qv',
            testDefinition: 'lkg_qv',
            enableP4Counters: true,
            runBilbo: true,
            needGameServer: true,
            uploadJournals: true,
            captureVideo: true,
            branches: [
                branchConfiguration(CH1_CODE_DEV, lkg_qv_ch1, 'H 0,5,15 * * 1-7'),
                branchConfiguration(CH1_CONTENT_DEV, lkg_qv_ch1, 'H 0,6,12 * * 1-7'),
                branchConfiguration(CH1_2024_TO_CH1, lkg_qv_ch1, 'H 13,14 * * 1-7'),
                branchConfiguration(CH1_TO_TRUNK, lkg_qv_ch1, 'H 2,7,13 * * 1-7'),
                branchConfiguration(CH1_STAGE, lkg_qv_ch1, 'H 0,6,16 * * 1-7'),
                branchConfiguration(TASK2, lkg_qv_ch1, 'H 7,8,19,20 * * 1-7'),
            ]
        )
        categories += new AutotestCategory(
            name: 'lkg_qv_la',
            testDefinition: 'lkg_qv_la',
            enableP4Counters: true,
            runBilbo: true,
            needGameServer: true,
            uploadJournals: true,
            captureVideo: true,
            branches: [
                branchConfiguration(CH1_CODE_DEV, lkg_qv_la, 'H 19 * * 1-7'),
                branchConfiguration(CH1_CONTENT_DEV, lkg_qv_la, 'H H/8 * * 1-7'),
                branchConfiguration(CH1_2024_TO_CH1, lkg_qv_la, 'H 14 * * 1-7'),
                branchConfiguration(CH1_TO_TRUNK, lkg_qv_la, 'H 23 * * 1-7'),
                branchConfiguration(CH1_STAGE, lkg_qv_la, 'H 18 * * 1-7'),
            ]
        )
        categories += new AutotestCategory(
            name: 'lkg_checkmate',
            testDefinition: 'lkg_checkmate',
            enableP4Counters: true,
            registerVerifiedForPreflight: true,
            config: 'release',
            runBilbo: true,
            registerSmoke: true,
            needGameServer: false,
            uploadJournals: true,
            captureVideo: true,
            buildType: 'dll',
            branches: [
                branchConfiguration(CH1_CODE_DEV, lkg_checkmate_pinned, '10,40 * * * 1-7'),
                branchConfiguration(CH1_CONTENT_DEV, lkg_checkmate_pinned, 'H/30 * * * 1-7'),
                branchConfiguration(CH1_2024_TO_CH1, lkg_checkmate, 'H 6,20 * * 1-7'),
                branchConfiguration(CH1_TO_TRUNK, lkg_checkmate, 'H */6 * * 1-7'),
                branchConfiguration(CH1_STAGE, lkg_checkmate, 'H */4 * * 1-7'),
                branchConfiguration(CH1_RELEASE, lkg_checkmate, 'H */4 * * 1-7'),
            ]
        )
        categories += new AutotestCategory(
            name: 'pt_perf_highpriority_final_setup',
            testDefinition: 'pt_perf_highpriority_final_setup',
            runBilbo: true,
            needGameServer: true,
            config: 'final',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            uploadJournals: true,
            captureVideo: true,
            isTestWithLooseFiles: true,
            useTempDeploymentIfLooseFiles: false,
            branches: [
                branchConfiguration(CH1_CONTENT_DEV, pt_perf_highpriority_final_setup, 'H 20,5 * * 1-6'),
            ]
        )
        categories += new AutotestCategory(
            name: 'pt_perf_highpriority_performance_setup',
            testDefinition: 'pt_perf_highpriority_performance_setup',
            runBilbo: true,
            remoteLabel: 'eala',
            needGameServer: true,
            config: 'performance',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            uploadJournals: true,
            captureVideo: true,
            isTestWithLooseFiles: true,
            useTempDeploymentIfLooseFiles: false,
            branches: [
                branchConfiguration(CH1_CONTENT_DEV, pt_perf_highpriority_performance_setup, 'H 14,22,4 * * 1-6'),
                branchConfiguration(CH1_2024_TO_CH1, pt_perf_highpriority_performance_setup, 'H 16 * * 1-6'),
            ]
        )
        categories += new AutotestCategory(
            name: 'pt_perf_highpriority_performance_setup_two',
            testDefinition: 'pt_perf_highpriority_performance_setup_two',
            runBilbo: true,
            needGameServer: true,
            config: 'performance',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            uploadJournals: true,
            captureVideo: true,
            isTestWithLooseFiles: true,
            useTempDeploymentIfLooseFiles: false,
            branches: [
                branchConfiguration(CH1_CONTENT_DEV, pt_perf_highpriority_performance_setup_two, 'H 16,21 * * 1-6'),
                branchConfiguration(CH1_RELEASE, pt_perf_highpriority_performance_setup_ch1_release, 'H 6 * * 1-6'),
            ]
        )
        categories += new AutotestCategory(
            name: 'pt_perf_highpriority_performance_setup_spin_server',
            testDefinition: 'pt_perf_highpriority_performance_setup_spin_server',
            runBilbo: false,
            config: 'performance',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            useSpinBuild: true,
            uploadJournals: true,
            captureVideo: true,
            isTestWithLooseFiles: true,
            useTempDeploymentIfLooseFiles: false,
            branches: [
                branchConfiguration(CH1_CONTENT_DEV, pt_perf_highpriority_performance_setup_spin_server, 'H 22 * * 1-6'),
            ]
        )
        categories += new AutotestCategory(
            name: 'pt_perf_performance_setup_ch1_content_validation',
            testDefinition: 'pt_perf_performance_setup_ch1_content_validation',
            runBilbo: true,
            needGameServer: true,
            config: 'performance',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            uploadJournals: true,
            captureVideo: true,
            isTestWithLooseFiles: true,
            useTempDeploymentIfLooseFiles: false,
            branches: [
                branchConfiguration(CH1_CONTENT_DEV, pt_perf_performance_setup_ch1_content_validation, 'H 4 * * 1-6'),
            ]
        )/*
            categories += new AutotestCategory(
                name: 'pt_perf_highpriority_performance_setup_AlphaValidation_Coverage',
                testDefinition: 'pt_perf_highpriority_performance_setup_AlphaValidation_Coverage',
                runBilbo: true,
                needGameServer: true,
                config: 'performance',
                serverConfig: 'final',
                region: Region.WW,
                format: 'files',
                uploadJournals: true,
                captureVideo: true,
                isTestWithLooseFiles: true,
                useTempDeploymentIfLooseFiles: false,
                branches: [
                    branchConfiguration(CH1_STAGE, pt_perf_highpriority_performance_setup_AlphaValidation_Coverage, 'H 5,16 * * 1-7'),
                ]
            )
            categories += new AutotestCategory(
                name: 'pt_perf_highpriority_performance_setup_AlphaValidation_HighPrio',
                testDefinition: 'pt_perf_highpriority_performance_setup_AlphaValidation_HighPrio',
                runBilbo: true,
                needGameServer: true,
                config: 'performance',
                serverConfig: 'final',
                region: Region.WW,
                format: 'files',
                uploadJournals: true,
                captureVideo: true,
                isTestWithLooseFiles: true,
                useTempDeploymentIfLooseFiles: false,
                branches: [
                    branchConfiguration(CH1_STAGE, pt_perf_highpriority_performance_setup_AlphaValidation_HighPrio, 'H 7,17,23 * * 1-7'),
                    branchConfiguration(CH1_RELEASE, pt_perf_highpriority_performance_setup_AlphaValidation_HighPrio_ch1_release, 'H 9,0 * * 1-7'),
                ]
            )*/
        categories += new AutotestCategory(
            name: 'pt_perf_highpriority_performance_setup_AlphaValidation_BFLabs',
            testDefinition: 'pt_perf_highpriority_performance_setup_AlphaValidation_BFLabs',
            runBilbo: true,
            needGameServer: true,
            config: 'performance',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            uploadJournals: true,
            captureVideo: true,
            isTestWithLooseFiles: true,
            useTempDeploymentIfLooseFiles: false,
            branches: [
                branchConfiguration(CH1_STAGE, pt_perf_highpriority_performance_setup_AlphaValidation_BFLabs, 'H 8,13,22 * * 1-7'),
            ]
        )
        categories += new AutotestCategory(
            name: 'pt_perf_highpriority_performance_setup_AlphaValidation_BFLabs_release',
            testDefinition: 'pt_perf_highpriority_performance_setup_AlphaValidation_BFLabs_release',
            runBilbo: true,
            needGameServer: true,
            config: 'performance',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            uploadJournals: true,
            captureVideo: true,
            isTestWithLooseFiles: true,
            useTempDeploymentIfLooseFiles: false,
            branches: [
                branchConfiguration(CH1_RELEASE, pt_perf_highpriority_performance_setup_AlphaValidation_BFLabs_release, 'H 2,17 * * 1-7'),
            ]
        )
        categories += new AutotestCategory(
            name: 'pt_perf_performance_setup_trinity',
            testDefinition: 'pt_perf_performance_setup_trinity',
            runBilbo: true,
            needGameServer: true,
            config: 'performance',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            uploadJournals: true,
            captureVideo: true,
            isTestWithLooseFiles: true,
            useTempDeploymentIfLooseFiles: false,
            branches: [
                branchConfiguration(CH1_CONTENT_DEV, pt_perf_performance_setup_trinity, 'H 22,5 * * 1-6'),
            ]
        )
        categories += new AutotestCategory(
            name: 'pt_perf_performance_meta_setup_trinity',
            testDefinition: 'pt_perf_performance_meta_setup_trinity',
            runBilbo: true,
            needGameServer: true,
            config: 'performance',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            uploadJournals: true,
            captureVideo: true,
            isTestWithLooseFiles: true,
            useTempDeploymentIfLooseFiles: false,
            branches: [
                branchConfiguration(CH1_CONTENT_DEV, pt_perf_performance_meta_setup_trinity, 'H 0,3 * * 1-6'),
            ]
        )
        categories += new AutotestCategory(
            name: 'pt_perf_performance_setup_trinity_two',
            testDefinition: 'pt_perf_performance_setup_trinity_two',
            remoteLabel: 'eala',
            runBilbo: true,
            needGameServer: true,
            config: 'performance',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            uploadJournals: true,
            captureVideo: true,
            isTestWithLooseFiles: true,
            useTempDeploymentIfLooseFiles: false,
            branches: [
                branchConfiguration(CH1_CONTENT_DEV, pt_perf_performance_setup_trinity_two, 'H 10 * * 1-6'),
            ]
        )
        categories += new AutotestCategory(
            name: 'pt_perf_performance_setup_trinity_release',
            testDefinition: 'pt_perf_performance_setup_trinity_release',
            remoteLabel: 'eala',
            runBilbo: true,
            needGameServer: true,
            config: 'performance',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            uploadJournals: true,
            captureVideo: true,
            isTestWithLooseFiles: true,
            useTempDeploymentIfLooseFiles: false,
            branches: [
                branchConfiguration(CH1_CONTENT_DEV, pt_perf_performance_setup_trinity_release, 'H 10 * * 1-6'),
            ]
        )
        categories += new AutotestCategory(
            name: 'pt_perf_performance_setup_trinity_stage_AlphaValidation_Trinity_BFLabs',
            testDefinition: 'pt_perf_performance_setup_trinity_stage_AlphaValidation_Trinity_BFLabs',
            runBilbo: true,
            needGameServer: true,
            config: 'performance',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            uploadJournals: true,
            captureVideo: true,
            isTestWithLooseFiles: true,
            useTempDeploymentIfLooseFiles: false,
            branches: [
                branchConfiguration(CH1_STAGE, pt_perf_performance_setup_trinity_stage_AlphaValidation_Trinity_BFLabs, 'H 14,20,3 * * 1-7'),
            ]
        )
        categories += new AutotestCategory(
            name: 'pt_perf_performance_setup_trinity_release_AlphaValidation_Trinity_BFLabs',
            testDefinition: 'pt_perf_performance_setup_trinity_release_AlphaValidation_Trinity_BFLabs',
            runBilbo: true,
            needGameServer: true,
            config: 'performance',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            uploadJournals: true,
            captureVideo: true,
            isTestWithLooseFiles: true,
            useTempDeploymentIfLooseFiles: false,
            branches: [
                branchConfiguration(CH1_RELEASE, pt_perf_performance_setup_trinity_release_AlphaValidation_Trinity_BFLabs, 'H 5,17 * * 1-7'),
            ]
        )/*
            categories += new AutotestCategory(
                name: 'pt_perf_performance_setup_trinity_stage_AlphaValidation_Trinity_Coverage',
                testDefinition: 'pt_perf_performance_setup_trinity_stage_AlphaValidation_Trinity_Coverage',
                runBilbo: true,
                needGameServer: true,
                config: 'performance',
                serverConfig: 'final',
                region: Region.WW,
                format: 'files',
                uploadJournals: true,
                captureVideo: true,
                isTestWithLooseFiles: true,
                useTempDeploymentIfLooseFiles: false,
                branches: [
                    branchConfiguration(CH1_STAGE, pt_perf_performance_setup_trinity_stage_AlphaValidation_Trinity_Coverage, 'H 23 * * 1-7'),
                ]
            )
            categories += new AutotestCategory(
                name: 'pt_perf_performance_setup_trinity_stage_AlphaValidation_Trinity_HighPiro',
                testDefinition: 'pt_perf_performance_setup_trinity_stage_AlphaValidation_Trinity_HighPiro',
                runBilbo: true,
                needGameServer: true,
                config: 'performance',
                serverConfig: 'final',
                region: Region.WW,
                format: 'files',
                uploadJournals: true,
                captureVideo: true,
                isTestWithLooseFiles: true,
                useTempDeploymentIfLooseFiles: false,
                branches: [
                    branchConfiguration(CH1_STAGE, pt_perf_performance_setup_trinity_stage_AlphaValidation_Trinity_HighPiro, 'H 0,8,16 * * 1-7'),
                    branchConfiguration(CH1_RELEASE, pt_perf_performance_setup_trinity_release_AlphaValidation_Trinity_HighPiro, 'H 0,8,16 * * 1-7'),
                ]
            )*/
        categories += new AutotestCategory(
            name: 'pt_perf_performance_setup_trinity_detailed',
            testDefinition: 'pt_perf_performance_setup_trinity_detailed',
            runBilbo: true,
            needGameServer: true,
            config: 'performance',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            uploadJournals: true,
            captureVideo: true,
            isTestWithLooseFiles: true,
            useTempDeploymentIfLooseFiles: false,
            branches: [
                branchConfiguration(CH1_CONTENT_DEV, pt_perf_performance_setup_trinity_detailed, 'H 6 * * 1-6'),
            ]
        )
        categories += new AutotestCategory(
            name: 'pt_perf_highpriority_performance_setup_win',
            testDefinition: 'pt_perf_highpriority_performance_setup_win',
            runBilbo: true,
            needGameServer: true,
            config: 'performance',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            uploadJournals: true,
            captureVideo: true,
            isTestWithLooseFiles: true,
            useTempDeploymentIfLooseFiles: false,
            branches: [
                branchConfiguration(CH1_CONTENT_DEV, pt_perf_highpriority_performance_setup_win, 'H 19,4 * * 1-6'),
                branchConfiguration(CH1_2024_TO_CH1, pt_perf_highpriority_performance_setup_win, 'H 10 * * 1-6'),
            ]
        )
        categories += new AutotestCategory(
            name: 'pt_perf_performance_setup',
            testDefinition: 'pt_perf_performance_setup',
            runBilbo: true,
            needGameServer: true,
            config: 'performance',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            uploadJournals: true,
            captureVideo: true,
            isTestWithLooseFiles: true,
            useTempDeploymentIfLooseFiles: false,
            branches: [
                branchConfiguration(CH1_CONTENT_DEV, pt_perf_performance_setup, 'H 12 * * 1-6'),
                branchConfiguration(CH1_2024_TO_CH1, pt_perf_performance_setup, 'H 0,22 * * 1-6'),
            ]
        )
        categories += new AutotestCategory(
            name: 'pt_perf_meta_performance_setup',
            testDefinition: 'pt_perf_meta_performance_setup',
            runBilbo: true,
            needGameServer: true,
            config: 'performance',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            uploadJournals: true,
            captureVideo: true,
            isTestWithLooseFiles: true,
            useTempDeploymentIfLooseFiles: false,
            branches: [
                branchConfiguration(CH1_CONTENT_DEV, pt_perf_meta_performance_setup, 'H 12,19 * * 1-6'),
            ]
        )
        categories += new AutotestCategory(
            name: 'pt_perf_f2p_highpriority_performance_setup',
            testDefinition: 'pt_perf_f2p_highpriority_performance_setup',
            runBilbo: true,
            needGameServer: true,
            config: 'performance',
            serverConfig: 'final',
            remoteLabel: 'eala',
            region: Region.WW,
            format: 'files',
            uploadJournals: true,
            captureVideo: true,
            isTestWithLooseFiles: true,
            useTempDeploymentIfLooseFiles: false,
            branches: [
                branchConfiguration(CH1_CONTENT_DEV, pt_perf_f2p_highpriority_performance_setup, 'H 14,23,3 * * 1-6'),
            ]
        )
        categories += new AutotestCategory(
            name: 'pt_perf_f2p_highpriority_performance_setup_detailed',
            testDefinition: 'pt_perf_f2p_highpriority_performance_setup_detailed',
            runBilbo: true,
            needGameServer: true,
            config: 'performance',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            uploadJournals: true,
            captureVideo: true,
            isTestWithLooseFiles: true,
            useTempDeploymentIfLooseFiles: false,
            branches: [
                branchConfiguration(CH1_CONTENT_DEV, pt_perf_f2p_highpriority_performance_setup_detailed, 'H 0,6,17 * * 1-6'),
            ]
        )
        categories += new AutotestCategory(
            name: 'pt_perf_highpriority_flythroughs_final_setup',
            testDefinition: 'pt_perf_highpriority_flythroughs_final_setup',
            runBilbo: true,
            needGameServer: true,
            config: 'final',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            uploadJournals: true,
            captureVideo: true,
            isTestWithLooseFiles: true,
            useTempDeploymentIfLooseFiles: false,
            branches: [
                branchConfiguration(CH1_CONTENT_DEV, pt_perf_highpriority_flythroughs_final_setup, 'H 4,8 * * 1-6'),
                branchConfiguration(CH1_2024_TO_CH1, pt_perf_highpriority_flythroughs_final_setup, 'H 12 * * 1-6'),
                /*branchConfiguration(CH1_STAGE, pt_perf_highpriority_flythroughs_final_setup, 'H 12 * * 1-6'),*/
            ]
        )
        categories += new AutotestCategory(
            name: 'pt_perf_mem_final_ch1_stage',
            testDefinition: 'pt_perf_mem_final_ch1_stage',
            runBilbo: true,
            needGameServer: true,
            config: 'final',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            uploadJournals: true,
            captureVideo: true,
            isTestWithLooseFiles: true,
            useTempDeploymentIfLooseFiles: false,
            branches: [
                branchConfiguration(CH1_STAGE, pt_perf_mem_final_ch1_stage, 'H 1,18 * * 1-6'),
            ]
        )
        categories += new AutotestCategory(
            name: 'pt_perf_mem_final_ch1_release',
            testDefinition: 'pt_perf_mem_final_ch1_release',
            runBilbo: true,
            needGameServer: true,
            config: 'final',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            uploadJournals: true,
            captureVideo: true,
            isTestWithLooseFiles: true,
            useTempDeploymentIfLooseFiles: false,
            branches: [
                branchConfiguration(CH1_RELEASE, pt_perf_mem_final_ch1_release, 'H 3,15 * * 1-6'),
            ]
        )
        categories += new AutotestCategory(
            name: 'pt_perf_HUD',
            testDefinition: 'pt_perf_HUD',
            runBilbo: true,
            needGameServer: true,
            config: 'final',
            serverConfig: 'final',
            format: 'files',
            uploadJournals: true,
            captureVideo: true,
            isTestWithLooseFiles: true,
            branches: [
                branchConfiguration(CH1_CONTENT_DEV, pt_perf_HUD, 'H 5 * * 1-7'),
            ]
        )
        categories += new AutotestCategory(
            name: 'pt_perf_HUD_MinSpec',
            testDefinition: 'pt_perf_HUD_MinSpec',
            runBilbo: true,
            needGameServer: true,
            config: 'final',
            serverConfig: 'final',
            format: 'files',
            uploadJournals: true,
            captureVideo: true,
            isTestWithLooseFiles: true,
            branches: [
                branchConfiguration(CH1_CONTENT_DEV, pt_perf_HUD_MinSpec, 'H 9 * * 1-7'),
            ]
        )
        categories += new AutotestCategory(
            name: 'pt_perf_HUD_RecSpec',
            testDefinition: 'pt_perf_HUD_RecSpec',
            runBilbo: true,
            needGameServer: true,
            config: 'final',
            serverConfig: 'final',
            format: 'files',
            uploadJournals: true,
            captureVideo: true,
            isTestWithLooseFiles: true,
            branches: [
                branchConfiguration(CH1_CONTENT_DEV, pt_perf_HUD_RecSpec, 'H 18 * * 1-7'),
            ]
        )
        categories += new AutotestCategory(
            name: 'pt_perf_HUD_Trinity',
            testDefinition: 'pt_perf_HUD_Trinity',
            runBilbo: true,
            needGameServer: true,
            config: 'final',
            serverConfig: 'final',
            format: 'files',
            uploadJournals: true,
            captureVideo: true,
            isTestWithLooseFiles: true,
            branches: [
                branchConfiguration(CH1_CONTENT_DEV, pt_perf_HUD_Trinity, 'H 0 * * 1-7'),
            ]
        )
        categories += new AutotestCategory(
            name: 'ch1_asan_windows',
            testDefinition: 'ch1_asan_windows',
            runBilbo: true,
            needGameServer: false,
            uploadJournals: false,
            captureVideo: true,
            customTag: 'asan',
            branches: [
                branchConfiguration(CH1_CONTENT_DEV_SANITIZERS, ch1_asan_windows, '')
            ]
        )
        categories += new AutotestCategory(
            name: 'lkg_checkmate_la',
            testDefinition: 'lkg_checkmate_la',
            enableP4Counters: true,
            remoteLabel: 'eala',
            registerVerifiedForPreflight: true,
            config: 'release',
            runBilbo: true,
            registerSmoke: true,
            needGameServer: false,
            uploadJournals: true,
            captureVideo: true,
            buildType: 'dll',
            branches: [
                branchConfiguration(CH1_CONTENT_DEV, lkg_checkmate_la, 'H/30 * * * 1-7'),
            ]
        )
        categories += new AutotestCategory(
            name: 'ch1_asan_consoles',
            testDefinition: 'ch1_asan_consoles',
            runBilbo: true,
            needGameServer: false,
            uploadJournals: false,
            captureVideo: true,
            customTag: 'asan',
            branches: [
                branchConfiguration(CH1_CONTENT_DEV_SANITIZERS, ch1_asan_consoles, '')
            ]
        )
        categories += new AutotestCategory(
            name: 'ch1_ubsan_consoles',
            testDefinition: 'ch1_ubsan_consoles',
            runBilbo: true,
            needGameServer: false,
            uploadJournals: false,
            captureVideo: true,
            customTag: 'ubsan',
            branches: [
                branchConfiguration(CH1_CONTENT_DEV_SANITIZERS, ch1_ubsan_consoles, '')
            ]
        )/*
            categories += new AutotestCategory(
                name: 'pt_perf_HUD_UltSpec',
                testDefinition: 'pt_perf_HUD_UltSpec',
                runBilbo: true,
                needGameServer: true,
                config: 'final',
                serverConfig: 'final',
                format: 'files',
                uploadJournals: true,
                captureVideo: true,
                isTestWithLooseFiles: true,
                branches: [
                    branchConfiguration(CH1_CODE_DEV, pt_perf_HUD_UltSpec, 'H 6 * * 3,6'),
                ]
            )*/
        categories += new AutotestCategory(
            name: 'pt_perf_HUD_XBSS',
            testDefinition: 'pt_perf_HUD_XBSS',
            runBilbo: true,
            needGameServer: true,
            config: 'final',
            serverConfig: 'final',
            format: 'files',
            uploadJournals: true,
            captureVideo: true,
            isTestWithLooseFiles: true,
            branches: [
                branchConfiguration(CH1_CONTENT_DEV, pt_perf_HUD_XBSS, 'H 0 * * 1-7'),
            ]
        )
        categories += new AutotestCategory(
            name: 'pt_perf_highpriority_flythroughs_final_setup2',
            testDefinition: 'pt_perf_highpriority_flythroughs_final_setup2',
            runBilbo: true,
            needGameServer: true,
            config: 'final',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            uploadJournals: true,
            captureVideo: true,
            isTestWithLooseFiles: true,
            useTempDeploymentIfLooseFiles: false,
            branches: [
                branchConfiguration(CH1_CONTENT_DEV, pt_perf_highpriority_flythroughs_final_setup2, 'H 0,5,10 * * 1-6'),
                /*branchConfiguration(CH1_STAGE, pt_perf_highpriority_flythroughs_final_setup2, 'H 20,0 * * 1-6'),*/
            ]
        )
        categories += new AutotestCategory(
            name: 'pt_perf_flythroughs_final_implicit_setup',
            testDefinition: 'pt_perf_flythroughs_final_implicit_setup',
            runBilbo: true,
            needGameServer: true,
            config: 'final',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            uploadJournals: true,
            captureVideo: true,
            isTestWithLooseFiles: true,
            useTempDeploymentIfLooseFiles: false,
            branches: [
                branchConfiguration(CH1_CONTENT_DEV, pt_perf_flythroughs_final_implicit_setup, 'H 16,22 * * 1-6'),
            ]
        )
        categories += new AutotestCategory(
            name: 'pt_perf_highpriority_flythroughs_final_setup_mem',
            testDefinition: 'pt_perf_highpriority_flythroughs_final_setup_mem',
            runBilbo: true,
            needGameServer: true,
            config: 'final',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            uploadJournals: true,
            captureVideo: true,
            isTestWithLooseFiles: true,
            useTempDeploymentIfLooseFiles: false,
            branches: [
                branchConfiguration(CH1_CONTENT_DEV, pt_perf_highpriority_flythroughs_final_setup_mem, 'H 12,16,22 * * 1-6'),
                branchConfiguration(CH1_2024_TO_CH1, pt_perf_highpriority_flythroughs_final_setup2, 'H 15 * * 1-6'),
            ]
        )
        categories += new AutotestCategory(
            name: 'pt_perf_highpriority_flythroughs_final_setup_mem_xbss',
            testDefinition: 'pt_perf_highpriority_flythroughs_final_setup_mem_xbss',
            runBilbo: true,
            needGameServer: true,
            config: 'final',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            uploadJournals: true,
            captureVideo: true,
            isTestWithLooseFiles: true,
            useTempDeploymentIfLooseFiles: false,
            branches: [
                branchConfiguration(CH1_CONTENT_DEV, pt_perf_highpriority_flythroughs_final_setup_mem_xbss, 'H 22,6 * * 1-6'),
            ]
        )
        categories += new AutotestCategory(
            name: 'pt_perf_flythroughs_final_setup',
            testDefinition: 'pt_perf_flythroughs_final_setup',
            runBilbo: true,
            needGameServer: true,
            config: 'final',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            uploadJournals: true,
            captureVideo: true,
            isTestWithLooseFiles: true,
            useTempDeploymentIfLooseFiles: false,
            branches: [
                branchConfiguration(CH1_CONTENT_DEV, pt_perf_flythroughs_final_setup, 'H 20 * * 1-6'),
                branchConfiguration(CH1_2024_TO_CH1, pt_perf_flythroughs_final_setup, 'H 12 * * 1-6'),
                /*branchConfiguration(CH1_STAGE, pt_perf_flythroughs_final_setup, 'H 13,19 * * 1-6'),*/
            ]
        )
        categories += new AutotestCategory(
            name: 'pt_perf_flythroughs_final_setup_trinity',
            testDefinition: 'pt_perf_flythroughs_final_setup_trinity',
            runBilbo: true,
            needGameServer: true,
            config: 'final',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            uploadJournals: true,
            captureVideo: true,
            isTestWithLooseFiles: true,
            useTempDeploymentIfLooseFiles: false,
            branches: [
                branchConfiguration(CH1_CONTENT_DEV, pt_perf_flythroughs_final_setup_trinity, 'H 8 * * 1-6'),
            ]
        )
        categories += new AutotestCategory(
            name: 'pt_perf_flythroughs_XBSS_CH1_code_final_setup',
            testDefinition: 'pt_perf_flythroughs_XBSS_CH1_code_final_setup',
            runBilbo: true,
            needGameServer: true,
            config: 'final',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            uploadJournals: true,
            captureVideo: true,
            isTestWithLooseFiles: true,
            useTempDeploymentIfLooseFiles: false,
            branches: [
                branchConfiguration(CH1_CONTENT_DEV, pt_perf_flythroughs_XBSS_CH1_code_final_setup, 'H 22 * * 1-7'),
            ]
        )
        categories += new AutotestCategory(
            name: 'pt_perf_flythroughs_XBSS_CH1_code_final_setup2',
            testDefinition: 'pt_perf_flythroughs_XBSS_CH1_code_final_setup2',
            runBilbo: true,
            needGameServer: true,
            config: 'final',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            uploadJournals: true,
            captureVideo: true,
            isTestWithLooseFiles: true,
            useTempDeploymentIfLooseFiles: false,
            branches: [
                branchConfiguration(CH1_CONTENT_DEV, pt_perf_flythroughs_XBSS_CH1_code_final_setup2, 'H 10 * * 1-7'),
            ]
        )
        categories += new AutotestCategory(
            name: 'pt_perf_xbss_sp_final',
            testDefinition: 'pt_perf_xbss_sp_final',
            runBilbo: true,
            needGameServer: true,
            config: 'final',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            uploadJournals: true,
            captureVideo: true,
            isTestWithLooseFiles: true,
            useTempDeploymentIfLooseFiles: false,
            branches: [
                branchConfiguration(CH1_CONTENT_DEV, pt_perf_xbss_sp_final, 'H 7,19 * * 1-7'),
                /*branchConfiguration(CH1_STAGE, pt_perf_xbss_sp_final, 'H 8,2 * * 1-7'),*/
            ]
        )
        return categories
    }

    List<AutotestCategory> getTestCategories2() {
        List<AutotestCategory> categories = []
        categories += new AutotestCategory(
            name: 'pt_perf_mem_gameplay_XBSS',
            testDefinition: 'pt_perf_mem_gameplay_XBSS',
            runBilbo: true,
            needGameServer: true,
            config: 'final',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            uploadJournals: true,
            captureVideo: true,
            isTestWithLooseFiles: true,
            useTempDeploymentIfLooseFiles: false,
            branches: [
                branchConfiguration(CH1_RELEASE, pt_perf_mem_gameplay_XBSS, 'H 15 * * 1-7'),
                /*branchConfiguration(CH1_STAGE, pt_perf_mem_gameplay_XBSS, 'H 2,9,17 * * 1-7'),*/
            ]
        )/*
        categories += new AutotestCategory(
            name: 'pt_perf_mem_gameplay_XBSS',
            testDefinition: 'pt_perf_mem_gameplay_XBSS',
            runBilbo: true,
            needGameServer: true,
            config: 'final',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            uploadJournals: true,
            captureVideo: true,
            isTestWithLooseFiles: true,
            useTempDeploymentIfLooseFiles: false,
            branches: [
                branchConfiguration(CH1_STAGE, pt_perf_mem_gameplay_F2P_XBSS, 'H 10,18,0 * * 1-7'),
            ]
        )*/
        categories += new AutotestCategory(
            name: 'pt_perf_flythroughs_XBSS_performance_setup',
            testDefinition: 'pt_perf_flythroughs_XBSS_performance_setup',
            runBilbo: true,
            needGameServer: true,
            config: 'performance',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            uploadJournals: true,
            captureVideo: true,
            isTestWithLooseFiles: true,
            useTempDeploymentIfLooseFiles: false,
            branches: [
                branchConfiguration(CH1_CONTENT_DEV, pt_perf_flythroughs_XBSS_performance_setup, 'H 7 * * 1-7'),
            ]
        )
        categories += new AutotestCategory(
            name: 'pt_perf_flythroughs_XBSS_performance_setup_two',
            testDefinition: 'pt_perf_flythroughs_XBSS_performance_setup_two',
            runBilbo: true,
            needGameServer: true,
            config: 'performance',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            uploadJournals: true,
            captureVideo: true,
            isTestWithLooseFiles: true,
            useTempDeploymentIfLooseFiles: false,
            branches: [
                branchConfiguration(CH1_CONTENT_DEV, pt_perf_flythroughs_XBSS_performance_setup_two, 'H 6 * * 1-7'),
            ]
        )/*
        categories += new AutotestCategory(
            name: 'pt_perf_flythroughs_XBSS_performance_setup_temp',
            testDefinition: 'pt_perf_flythroughs_XBSS_performance_setup_temp',
            runBilbo: true,
            needGameServer: true,
            config: 'performance',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            uploadJournals: true,
            captureVideo: true,
            isTestWithLooseFiles: true,
            useTempDeploymentIfLooseFiles: false,
            branches: [
                branchConfiguration(CH1_STAGE, pt_perf_flythroughs_XBSS_performance_setup_temp, 'H 16 * * 1-7'),
            ]
        )
        /*
            categories += new AutotestCategory(
                name: 'pt_perf_flythroughs_XBSS_performance_setup_ch1_stage_AlphaValidation_XBSS_Coverage',
                testDefinition: 'pt_perf_flythroughs_XBSS_performance_setup_ch1_stage_AlphaValidation_XBSS_Coverage',
                runBilbo: true,
                needGameServer: true,
                config: 'performance',
                serverConfig: 'final',
                region: Region.WW,
                format: 'files',
                uploadJournals: true,
                captureVideo: true,
                isTestWithLooseFiles: true,
                useTempDeploymentIfLooseFiles: false,
                branches: [
                    branchConfiguration(CH1_STAGE, pt_perf_flythroughs_XBSS_performance_setup_ch1_stage_AlphaValidation_XBSS_Coverage, 'H 0 * * 1-7'),
                ]
            )
            categories += new AutotestCategory(
                name: 'pt_perf_flythroughs_XBSS_performance_setup_AlphaValidation_XBSS_HighPrio',
                testDefinition: 'pt_perf_flythroughs_XBSS_performance_setup_AlphaValidation_XBSS_HighPrio',
                runBilbo: true,
                needGameServer: true,
                config: 'performance',
                serverConfig: 'final',
                region: Region.WW,
                format: 'files',
                uploadJournals: true,
                captureVideo: true,
                isTestWithLooseFiles: true,
                useTempDeploymentIfLooseFiles: false,
                branches: [
                    branchConfiguration(CH1_STAGE, pt_perf_flythroughs_XBSS_performance_setup_ch1_stage_AlphaValidation_XBSS_HighPrio, 'H 6,14,22 * * 1-7'),
                    branchConfiguration(CH1_RELEASE, pt_perf_flythroughs_XBSS_performance_setup_ch1_release_AlphaValidation_XBSS_HighPrio, 'H 8,19 * * 1-7'),
                ]
            )*/
        categories += new AutotestCategory(
            name: 'pt_perf_flythroughs_XBSS_performance_setup_ch1_stage_AlphaValidation_XBSS_BFLabs',
            testDefinition: 'pt_perf_flythroughs_XBSS_performance_setup_ch1_stage_AlphaValidation_XBSS_BFLabs',
            runBilbo: true,
            needGameServer: true,
            config: 'performance',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            uploadJournals: true,
            captureVideo: true,
            isTestWithLooseFiles: true,
            useTempDeploymentIfLooseFiles: false,
            branches: [
                branchConfiguration(CH1_STAGE, pt_perf_flythroughs_XBSS_performance_setup_ch1_stage_AlphaValidation_XBSS_BFLabs, 'H 20 * * 1-7'),
            ]
        )
        categories += new AutotestCategory(
            name: 'pt_perf_flythroughs_XBSS_performance_setup_ch1_release_AlphaValidation_XBSS_BFLabs',
            testDefinition: 'pt_perf_flythroughs_XBSS_performance_setup_ch1_release_AlphaValidation_XBSS_BFLabs',
            runBilbo: true,
            needGameServer: true,
            config: 'performance',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            uploadJournals: true,
            captureVideo: true,
            isTestWithLooseFiles: true,
            useTempDeploymentIfLooseFiles: false,
            branches: [
                branchConfiguration(CH1_RELEASE, pt_perf_flythroughs_XBSS_performance_setup_ch1_release_AlphaValidation_XBSS_BFLabs, 'H 7,16 * * 1-7'),
            ]
        )
        categories += new AutotestCategory(
            name: 'pt_perf_lowpriority_XBSS_performance_setup',
            testDefinition: 'pt_perf_lowpriority_XBSS_performance_setup',
            runBilbo: true,
            needGameServer: true,
            config: 'performance',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            uploadJournals: true,
            captureVideo: true,
            isTestWithLooseFiles: true,
            useTempDeploymentIfLooseFiles: false,
            branches: [
                branchConfiguration(CH1_CONTENT_DEV, pt_perf_lowpriority_XBSS_performance_setup, 'H 20 * * 1-7'),
                branchConfiguration(CH1_2024_TO_CH1, pt_perf_lowpriority_XBSS_performance_setup, 'H 5 * * 1-7'),
                branchConfiguration(CH1_RELEASE, pt_perf_XBSS_performance_setup_ch1_release, 'H 0 * * 1-7'),
            ]
        )
        categories += new AutotestCategory(
            name: 'pt_perf_lowpriority_XBSS_final_setup',
            testDefinition: 'pt_perf_lowpriority_XBSS_final_setup',
            runBilbo: true,
            needGameServer: true,
            config: 'final',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            uploadJournals: true,
            captureVideo: true,
            isTestWithLooseFiles: true,
            useTempDeploymentIfLooseFiles: false,
            branches: [
                branchConfiguration(CH1_CONTENT_DEV, pt_perf_lowpriority_XBSS_final_setup, 'H 23 * * 1-7'),
                branchConfiguration(CH1_2024_TO_CH1, pt_perf_lowpriority_XBSS_final_setup, 'H 4 * * 1-7'),
            ]
        )
        categories += new AutotestCategory(
            name: 'pt_perf_mem_final_ch1_stage_XBSS',
            testDefinition: 'pt_perf_mem_final_ch1_stage_XBSS',
            runBilbo: true,
            needGameServer: true,
            config: 'final',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            uploadJournals: true,
            captureVideo: true,
            isTestWithLooseFiles: true,
            useTempDeploymentIfLooseFiles: false,
            branches: [
                branchConfiguration(CH1_STAGE, pt_perf_mem_final_ch1_stage_XBSS, 'H 9,22 * * 1-7'),
                branchConfiguration(CH1_RELEASE, pt_perf_mem_final_ch1_stage_XBSS, 'H 6,18 * * 1-7'),
            ]
        )
        categories += new AutotestCategory(
                name: 'pt_perf_mem_final_ch1_stage_UltSpec',
                testDefinition: 'pt_perf_mem_final_ch1_stage_UltSpec',
                runBilbo: true,
                needGameServer: true,
                config: 'final',
                serverConfig: 'final',
                region: Region.WW,
                format: 'files',
                uploadJournals: true,
                captureVideo: false,
                isTestWithLooseFiles: true,
                useTempDeploymentIfLooseFiles: false,
                branches: [
                    branchConfiguration(CH1_STAGE, pt_perf_mem_final_ch1_stage_UltSpec, 'H 0,11,19 * * 1-7'),
                ]
        )
        categories += new AutotestCategory(
                name: 'pt_perf_mem_final_ch1_release_UltSpec',
                testDefinition: 'pt_perf_mem_final_ch1_release_UltSpec',
                runBilbo: true,
                needGameServer: true,
                config: 'final',
                serverConfig: 'final',
                region: Region.WW,
                format: 'files',
                uploadJournals: true,
                captureVideo: false,
                isTestWithLooseFiles: true,
                useTempDeploymentIfLooseFiles: false,
                branches: [
                    branchConfiguration(CH1_RELEASE, pt_perf_mem_final_ch1_release_UltSpec, 'H 4,15 * * 1-7'),
                ]
            )
        categories += new AutotestCategory(
            name: 'pt_perf_minspec_performance_setup',
            testDefinition: 'pt_perf_minspec_performance_setup',
            runBilbo: true,
            needGameServer: true,
            config: 'performance',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            uploadJournals: true,
            captureVideo: false,
            isTestWithLooseFiles: true,
            useTempDeploymentIfLooseFiles: false,
            branches: [
                branchConfiguration(CH1_CONTENT_DEV, pt_perf_minspec_performance_setup, 'H 0 * * 1-6'),
                branchConfiguration(CH1_2024_TO_CH1, pt_perf_minspec_performance_setup, 'H 4 * * 7'),
            ]
        )
        categories += new AutotestCategory(
            name: 'pt_perf_minspec_meta_performance_setup',
            testDefinition: 'pt_perf_minspec_meta_performance_setup',
            runBilbo: true,
            needGameServer: true,
            config: 'performance',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            uploadJournals: true,
            captureVideo: false,
            isTestWithLooseFiles: true,
            useTempDeploymentIfLooseFiles: false,
            branches: [
                branchConfiguration(CH1_CONTENT_DEV, pt_perf_minspec_meta_performance_setup, 'H 0,16 * * 1-6'),
            ]
        )
        categories += new AutotestCategory(
            name: 'pt_perf_high_minspec_performance_setup',
            testDefinition: 'pt_perf_high_minspec_performance_setup',
            runBilbo: true,
            needGameServer: true,
            config: 'performance',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            uploadJournals: true,
            captureVideo: false,
            isTestWithLooseFiles: true,
            copyLooseFileBuildToWindowsShareTimeoutSeconds: 4200,
            useTempDeploymentIfLooseFiles: false,
            branches: [
                branchConfiguration(CH1_CONTENT_DEV, pt_perf_high_minspec_performance_setup, 'H 2 * * 1-6'),
                branchConfiguration(CH1_RELEASE, pt_perf_setup_ch1_release_minspec, 'H 21 * * 1-7'),
            ]
        )
        categories += new AutotestCategory(
            name: 'pt_perf_setup_ch1_stage_minspec_eala',
            testDefinition: 'pt_perf_setup_ch1_stage_minspec_eala',
            runBilbo: true,
            needGameServer: true,
            config: 'performance',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            uploadJournals: true,
            captureVideo: false,
            isTestWithLooseFiles: true,
            copyLooseFileBuildToWindowsShareTimeoutSeconds: 4200,
            useTempDeploymentIfLooseFiles: false,
            branches: [
                /*branchConfiguration(CH1_STAGE, pt_perf_setup_ch1_stage_minspec_eala, 'H 11,0 * * 1-7'),*/
                branchConfiguration(CH1_CONTENT_DEV, pt_perf_setup_ch1_stage_minspec_eala, 'H 16,2 * * 1-7'),
            ]
        )
        categories += new AutotestCategory(
            name: 'pt_perf_flythroughs_minspec_final_setup',
            testDefinition: 'pt_perf_flythroughs_minspec_final_setup',
            runBilbo: true,
            needGameServer: true,
            config: 'final',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            uploadJournals: true,
            captureVideo: false,
            isTestWithLooseFiles: true,
            useTempDeploymentIfLooseFiles: false,
            branches: [
                branchConfiguration(CH1_2024_TO_CH1, pt_perf_flythroughs_minspec_final_setup, 'H 20 * * 7'),
                /*branchConfiguration(CH1_STAGE, pt_perf_flythroughs_minspec_final_setup, 'H 10,20 * * 7'),*/
            ]
        )
        categories += new AutotestCategory(
            name: 'pt_perf_flythroughs_minspec_final_setup2',
            testDefinition: 'pt_perf_flythroughs_minspec_final_setup2',
            runBilbo: true,
            needGameServer: true,
            config: 'final',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            uploadJournals: true,
            captureVideo: false,
            isTestWithLooseFiles: true,
            useTempDeploymentIfLooseFiles: false,
            branches: [
                branchConfiguration(CH1_CONTENT_DEV, pt_perf_flythroughs_minspec_final_setup2, 'H 17 * * 1-6'),
                /*branchConfiguration(CH1_STAGE, pt_perf_flythroughs_minspec_final_setup2, 'H 14 * * 1-6'),*/
            ]
        )
        categories += new AutotestCategory(
            name: 'pt_perf_mem_final_ch1_stage_MinSpec',
            testDefinition: 'pt_perf_mem_final_ch1_stage_MinSpec',
            runBilbo: true,
            needGameServer: true,
            config: 'final',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            uploadJournals: true,
            captureVideo: false,
            isTestWithLooseFiles: true,
            useTempDeploymentIfLooseFiles: false,
            branches: [
                branchConfiguration(CH1_STAGE, pt_perf_mem_final_ch1_stage_MinSpec, 'H 4,20 * * 1-6'),
            ]
        )
        categories += new AutotestCategory(
            name: 'pt_perf_mem_final_ch1_release_MinSpec',
            testDefinition: 'pt_perf_mem_final_ch1_release_MinSpec',
            runBilbo: true,
            needGameServer: true,
            config: 'final',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            uploadJournals: true,
            captureVideo: false,
            isTestWithLooseFiles: true,
            useTempDeploymentIfLooseFiles: false,
            branches: [
                branchConfiguration(CH1_RELEASE, pt_perf_mem_final_ch1_release_MinSpec, 'H 7,12 * * 1-6'),
            ]
        )
        categories += new AutotestCategory(
            name: 'pt_perf_minspec_sp_final',
            testDefinition: 'pt_perf_minspec_sp_final',
            runBilbo: true,
            needGameServer: true,
            config: 'final',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            uploadJournals: true,
            captureVideo: false,
            isTestWithLooseFiles: true,
            useTempDeploymentIfLooseFiles: false,
            branches: [
                branchConfiguration(CH1_CONTENT_DEV, pt_perf_minspec_sp_final, 'H 3,7,12 * * 1-7'),
                /*branchConfiguration(CH1_STAGE, pt_perf_minspec_sp_final, 'H 19,1 * * 1-7'),*/
            ]
        )
        categories += new AutotestCategory(
            name: 'pt_perf_recspec_performance_setup',
            testDefinition: 'pt_perf_recspec_performance_setup',
            runBilbo: true,
            needGameServer: true,
            config: 'performance',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            uploadJournals: true,
            captureVideo: false,
            isTestWithLooseFiles: true,
            useTempDeploymentIfLooseFiles: false,
            branches: [
                branchConfiguration(CH1_CONTENT_DEV, pt_perf_recspec_performance_setup, 'H 16 * * 1-6'),
            ]
        )
        categories += new AutotestCategory(
            name: 'pt_perf_recspec_high_performance_setup',
            testDefinition: 'pt_perf_recspec_high_performance_setup',
            runBilbo: true,
            needGameServer: true,
            config: 'performance',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            uploadJournals: true,
            captureVideo: false,
            isTestWithLooseFiles: true,
            useTempDeploymentIfLooseFiles: false,
            branches: [
                branchConfiguration(CH1_CONTENT_DEV, pt_perf_recspec_high_performance_setup, 'H 15 * * 1-6'),
            ]
        )
        categories += new AutotestCategory(
            name: 'pt_perf_recspec_meta_performance_setup',
            testDefinition: 'pt_perf_recspec_meta_performance_setup',
            runBilbo: true,
            needGameServer: true,
            config: 'performance',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            uploadJournals: true,
            captureVideo: false,
            isTestWithLooseFiles: true,
            useTempDeploymentIfLooseFiles: false,
            branches: [
                branchConfiguration(CH1_CONTENT_DEV, pt_perf_recspec_meta_performance_setup, 'H 6,22 * * 1-6'),
            ]
        )
        categories += new AutotestCategory(
            name: 'pt_perf_flythroughs_recspec_final_setup',
            testDefinition: 'pt_perf_flythroughs_recspec_final_setup',
            runBilbo: true,
            needGameServer: true,
            config: 'final',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            uploadJournals: true,
            captureVideo: false,
            isTestWithLooseFiles: true,
            useTempDeploymentIfLooseFiles: false,
            branches: [
                branchConfiguration(CH1_CONTENT_DEV, pt_perf_flythroughs_recspec_final_setup, 'H 6 * * 1-6'),
                branchConfiguration(CH1_2024_TO_CH1, pt_perf_flythroughs_recspec_final_setup, 'H 8 * * 1-7'),
                /*branchConfiguration(CH1_STAGE, pt_perf_flythroughs_recspec_final_setup, 'H 16 * * 1-6'),*/
            ]
        )
        categories += new AutotestCategory(
            name: 'pt_perf_mem_final_ch1_stage_RecSpec',
            testDefinition: 'pt_perf_mem_final_ch1_stage_RecSpec',
            runBilbo: true,
            needGameServer: true,
            config: 'final',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            uploadJournals: true,
            captureVideo: false,
            isTestWithLooseFiles: true,
            useTempDeploymentIfLooseFiles: false,
            branches: [
                branchConfiguration(CH1_STAGE, pt_perf_mem_final_ch1_stage_RecSpec, 'H 7,22 * * 1-6'),
            ]
        )
        categories += new AutotestCategory(
            name: 'pt_perf_mem_final_ch1_release_RecSpec',
            testDefinition: 'pt_perf_mem_final_ch1_release_RecSpec',
            runBilbo: true,
            needGameServer: true,
            config: 'final',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            uploadJournals: true,
            captureVideo: false,
            isTestWithLooseFiles: true,
            useTempDeploymentIfLooseFiles: false,
            branches: [
                branchConfiguration(CH1_RELEASE, pt_perf_mem_final_ch1_release_RecSpec, 'H 1,19 * * 1-6'),
            ]
        )
        categories += new AutotestCategory(
            name: 'pt_perf_flythroughs_minspec_final_setup3',
            testDefinition: 'pt_perf_flythroughs_minspec_final_setup3',
            runBilbo: true,
            needGameServer: true,
            config: 'final',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            uploadJournals: true,
            captureVideo: false,
            isTestWithLooseFiles: true,
            useTempDeploymentIfLooseFiles: false,
            branches: [
                branchConfiguration(CH1_CONTENT_DEV, pt_perf_flythroughs_minspec_final_setup3, 'H 4 * * 1-6'),
                /*branchConfiguration(CH1_STAGE, pt_perf_flythroughs_minspec_final_setup3, 'H 19 * * 1-6'),*/
            ]
        )
        categories += new AutotestCategory(
            name: 'pt_perf_flythroughs_minspec_final_setup_ch1_code',
            testDefinition: 'pt_perf_flythroughs_minspec_final_setup_ch1_code',
            runBilbo: true,
            needGameServer: true,
            config: 'final',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            uploadJournals: true,
            captureVideo: false,
            isTestWithLooseFiles: true,
            useTempDeploymentIfLooseFiles: false,
            branches: [
                branchConfiguration(CH1_CONTENT_DEV, pt_perf_flythroughs_minspec_final_setup_ch1_code, 'H 4 * * 1-6'),
            ]
        )
        categories += new AutotestCategory(
            name: 'pt_perf_lowpriority_final_setup',
            testDefinition: 'pt_perf_lowpriority_final_setup',
            runBilbo: true,
            needGameServer: true,
            config: 'final',
            serverConfig: 'final',
            remoteLabel: 'eala',
            region: Region.WW,
            format: 'files',
            uploadJournals: true,
            captureVideo: true,
            isTestWithLooseFiles: true,
            useTempDeploymentIfLooseFiles: false,
            branches: [
                branchConfiguration(CH1_2024_TO_CH1, pt_perf_lowpriority_final_setup, 'H 19 * * 1-7'),
                branchConfiguration(CH1_CONTENT_DEV, pt_perf_lowpriority_final_setup, 'H 10 * * 1-7'),
            ]
        )
        categories += new AutotestCategory(
            name: 'pt_perf_lowpriority_final_setup_minspec',
            testDefinition: 'pt_perf_lowpriority_final_setup_minspec',
            runBilbo: true,
            needGameServer: true,
            config: 'final',
            serverConfig: 'final',
            remoteLabel: 'eala',
            region: Region.WW,
            format: 'files',
            uploadJournals: true,
            captureVideo: true,
            isTestWithLooseFiles: true,
            useTempDeploymentIfLooseFiles: false,
            branches: [
                branchConfiguration(CH1_CONTENT_DEV, pt_perf_lowpriority_final_setup_minspec, 'H 20 * * 1-7'),
            ]
        )
        categories += new AutotestCategory(
            name: 'pt_perf_lowpriority_final_setup_Trinity',
            testDefinition: 'pt_perf_lowpriority_final_setup_Trinity',
            runBilbo: true,
            needGameServer: true,
            config: 'final',
            serverConfig: 'final',
            remoteLabel: 'eala',
            region: Region.WW,
            format: 'files',
            uploadJournals: true,
            captureVideo: true,
            isTestWithLooseFiles: true,
            useTempDeploymentIfLooseFiles: false,
            branches: [
                branchConfiguration(CH1_CONTENT_DEV, pt_perf_lowpriority_final_setup_Trinity, 'H 3 * * 1-7'),
            ]
        )
        categories += new AutotestCategory(
            name: 'pt_perf_lowpriority_final_setup_recspec',
            testDefinition: 'pt_perf_lowpriority_final_setup_recspec',
            runBilbo: true,
            needGameServer: true,
            config: 'final',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            uploadJournals: true,
            captureVideo: true,
            isTestWithLooseFiles: true,
            useTempDeploymentIfLooseFiles: false,
            branches: [
                branchConfiguration(CH1_CONTENT_DEV, pt_perf_lowpriority_final_setup_recspec, 'H 7 * * 1-7'),
            ]
        )
        categories += new AutotestCategory(
            name: 'pt_perf_lowpriority_final_setup_xbss',
            testDefinition: 'pt_perf_lowpriority_final_setup_xbss',
            runBilbo: true,
            needGameServer: true,
            config: 'final',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            uploadJournals: true,
            captureVideo: true,
            isTestWithLooseFiles: true,
            useTempDeploymentIfLooseFiles: false,
            branches: [
                branchConfiguration(CH1_CONTENT_DEV, pt_perf_lowpriority_final_setup_xbss, 'H 16 * * 1-7'),
            ]
        )
        categories += new AutotestCategory(
            name: 'pt_perf_lowpriority_final_setup_CH1',
            testDefinition: 'pt_perf_lowpriority_final_setup_CH1',
            runBilbo: true,
            needGameServer: true,
            config: 'final',
            remoteLabel: 'eala',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            uploadJournals: true,
            captureVideo: true,
            isTestWithLooseFiles: true,
            useTempDeploymentIfLooseFiles: false,
            branches: [
                branchConfiguration(CH1_CONTENT_DEV, pt_perf_lowpriority_final_setup_CH1, 'H 1 * * 1-7'),
            ]
        )
        categories += new AutotestCategory(
            name: 'pt_perf_lowpriority_final_setup_synced',
            testDefinition: 'pt_perf_lowpriority_final_setup_synced',
            runBilbo: true,
            needGameServer: true,
            remoteLabel: 'eala',
            config: 'final',
            serverConfig: 'final',
            uploadJournals: true,
            captureVideo: true,
            isTestWithLooseFiles: false,
            branches: [
                branchConfiguration(CH1_CONTENT_DEV, pt_perf_lowpriority_final_setup_synced, 'H 14 * * 1-7'),
                branchConfiguration(CH1_2024_TO_CH1, pt_perf_lowpriority_final_setup_synced, 'H 1 * * 1-7'),
            ]
        )
        categories += new AutotestCategory(
            name: 'pt_perf_lowpriority_final_setup_synced_xbss',
            testDefinition: 'pt_perf_lowpriority_final_setup_synced_xbss',
            runBilbo: true,
            needGameServer: true,
            config: 'final',
            serverConfig: 'final',
            uploadJournals: true,
            captureVideo: true,
            isTestWithLooseFiles: false,
            branches: [
                branchConfiguration(CH1_CONTENT_DEV, pt_perf_lowpriority_final_setup_synced_xbss, 'H 20 * * 1-7'),
            ]
        )
        categories += new AutotestCategory(
            name: 'pt_perf_lowpriority_final_setup_synced_ps5',
            testDefinition: 'pt_perf_lowpriority_final_setup_synced_ps5',
            remoteLabel: 'eala',
            runBilbo: true,
            needGameServer: true,
            config: 'final',
            serverConfig: 'final',
            uploadJournals: true,
            captureVideo: true,
            isTestWithLooseFiles: false,
            branches: [
                branchConfiguration(CH1_CONTENT_DEV, pt_perf_lowpriority_final_setup_synced_ps5, 'H 7 * * 1-7'),
                branchConfiguration(CH1_2024_TO_CH1, pt_perf_lowpriority_final_setup_synced_ps5, 'H 23 * * 1-7'),
            ]
        )
        categories += new AutotestCategory(
            name: 'pt_perf_lowpriority_performance_setup',
            testDefinition: 'pt_perf_lowpriority_performance_setup',
            runBilbo: true,
            needGameServer: true,
            config: 'performance',
            remoteLabel: 'eala',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            uploadJournals: true,
            captureVideo: true,
            isTestWithLooseFiles: true,
            useTempDeploymentIfLooseFiles: false,
            branches: [
                branchConfiguration(CH1_CONTENT_DEV, pt_perf_lowpriority_performance_setup, 'H 6 * * 1-7'),
                branchConfiguration(CH1_2024_TO_CH1, pt_perf_lowpriority_performance_setup, 'H 16 * * 1-7'),
            ]
        )
        categories += new AutotestCategory(
            name: 'pt_perf_lowpriority_performance_setup2',
            testDefinition: 'pt_perf_lowpriority_performance_setup2',
            runBilbo: true,
            needGameServer: true,
            config: 'performance',
            remoteLabel: 'eala',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            uploadJournals: true,
            captureVideo: true,
            isTestWithLooseFiles: true,
            useTempDeploymentIfLooseFiles: false,
            branches: [
                branchConfiguration(CH1_CONTENT_DEV, pt_perf_lowpriority_performance_setup2, 'H 11 * * 1-7'),
            ]
        )/*
        categories += new AutotestCategory(
            name: 'pt_stab_la',
            testDefinition: 'pt_stab_la',
            runBilbo: true,
            remoteLabel: 'eala',
            needGameServer: true,
            config: 'final',
            uploadJournals: false,
            captureVideo: true,
            branches: [
                branchConfiguration(CH1_CODE_DEV, pt_stab_la, 'H 0,8,13,19 * * 1-7'),
            ]
        )*/
        categories += new AutotestCategory(
            name: 'pt_perf_highpriority_performance_setup_la',
            testDefinition: 'pt_perf_highpriority_performance_setup_la',
            runBilbo: true,
            needGameServer: true,
            config: 'performance',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            uploadJournals: true,
            captureVideo: true,
            isTestWithLooseFiles: true,
            useTempDeploymentIfLooseFiles: false,
            branches: [
                branchConfiguration(CH1_CONTENT_DEV, pt_perf_highpriority_performance_setup_la, 'H 4,9,21 * * 1-7'),
            ]
        )
        categories += new AutotestCategory(
            name: 'pt_perf_lowpriority_final_setup_la',
            testDefinition: 'pt_perf_lowpriority_final_setup_la',
            runBilbo: true,
            remoteLabel: 'eala',
            needGameServer: true,
            config: 'final',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            uploadJournals: true,
            captureVideo: true,
            isTestWithLooseFiles: true,
            useTempDeploymentIfLooseFiles: false,
            branches: [
                branchConfiguration(CH1_CONTENT_DEV, pt_perf_lowpriority_final_setup_la, 'H 10 * * 1-7'),
            ]
        )
        categories += new AutotestCategory(
            name: 'large_scale_autoplaytest_setup_ch1_content_dev',
            testDefinition: 'large_scale_autoplaytest_setup_ch1_content_dev',
            config: 'final',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            useSpinBuild: true,
            isTestWithLooseFiles: true,
            useTempDeploymentIfLooseFiles: false,
            uploadJournals: false,
            runBilbo: false,
            branches: [
                branchConfiguration(CH1_CONTENT_DEV, large_scale_autoplaytest_setup_ch1_content_dev, ''), // TODO update EXTRA_ARGS_SHIFT_CH1_CONTENT_DEV when we want to trigger this job again
            ],
        )
        categories += new AutotestCategory(
            name: 'persistence_final_setup',
            testDefinition: 'persistence_final_setup',
            runBilbo: true,
            needGameServer: true,
            config: 'final',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            uploadJournals: false,
            captureVideo: true,
            isTestWithLooseFiles: true,
            useTempDeploymentIfLooseFiles: false,
            branches: [
                branchConfiguration(CH1_CONTENT_DEV, persistence_final_setup, 'H 14 * * 1-7'),
            ]
        )
        categories += new AutotestCategory(
            name: 'large_scale_autoplaytest_setup',
            testDefinition: 'large_scale_autoplaytest_setup',
            config: 'final',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            useSpinBuild: true,
            isTestWithLooseFiles: true,
            useTempDeploymentIfLooseFiles: false,
            uploadJournals: false,
            runBilbo: false,
            branches: [
                branchConfiguration(CH1_CONTENT_DEV, large_scale_autoplaytest_setup, 'H 12 * * 1-6'),
            ],
        )
        categories += new AutotestCategory(
            name: 'large_scale_autoplaytest_setup_fullbox',
            testDefinition: 'large_scale_autoplaytest_setup_fullbox',
            config: 'final',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            useSpinBuild: true,
            isTestWithLooseFiles: true,
            useTempDeploymentIfLooseFiles: false,
            uploadJournals: false,
            runBilbo: false,
            branches: [
                branchConfiguration(CH1_CONTENT_DEV, large_scale_autoplaytest_setup_fullbox, 'H 6 * * 1-6'),
            ],
        )
        categories += new AutotestCategory(
            name: 'large_scale_autoplaytest_setup_ch1_stage',
            testDefinition: 'large_scale_autoplaytest_setup_ch1_stage',
            config: 'final',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            useSpinBuild: true,
            isTestWithLooseFiles: true,
            useTempDeploymentIfLooseFiles: false,
            uploadJournals: false,
            runBilbo: false,
            branches: [
                branchConfiguration(CH1_STAGE, large_scale_autoplaytest_setup_ch1_stage, 'H 5 * * 1-7'),
            ],
        )
        categories += new AutotestCategory(
            name: 'large_scale_autoplaytest_setup_online_mp',
            testDefinition: 'large_scale_autoplaytest_setup_online_mp',
            config: 'final',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            useSpinBuild: true,
            isTestWithLooseFiles: true,
            useTempDeploymentIfLooseFiles: false,
            uploadJournals: false,
            runBilbo: false,
            branches: [
                branchConfiguration(CH1_CONTENT_DEV, large_scale_autoplaytest_setup_online_mp, 'H 16 * * 1-7'),
            ],
        )
        categories += new AutotestCategory(
            name: 'autoplaytest_setup_online_mp_ThinClient',
            testDefinition: 'autoplaytest_setup_online_mp_ThinClient',
            runBilbo: true,
            needGameServer: true,
            config: 'final',
            uploadJournals: false,
            captureVideo: false,
            branches: [
                branchConfiguration(CH1_CONTENT_DEV, autoplaytest_setup_online_mp_ThinClient, 'H */4 * * 1-7'),
            ],
        )
        categories += new AutotestCategory(
            name: 'large_scale_autoplaytest_setup_online_two_mp',
            testDefinition: 'large_scale_autoplaytest_setup_online_two_mp',
            config: 'final',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            useSpinBuild: true,
            isTestWithLooseFiles: true,
            useTempDeploymentIfLooseFiles: false,
            uploadJournals: false,
            runBilbo: false,
            branches: [
                branchConfiguration(CH1_CONTENT_DEV, large_scale_autoplaytest_setup_online_two_mp, 'H 19 * * 1-7'),
            ],
        )
        categories += new AutotestCategory(
            name: 'thin_client_linux_server_synced',
            testDefinition: 'thin_client_linux_server_synced',
            runBilbo: false,
            needGameServer: true,
            uploadJournals: false,
            branches: [
                branchConfiguration(CH1_CONTENT_DEV, thin_client_linux_server_synced, 'H */5 * * 1-7'),
            ]
        )
        categories += new AutotestCategory(
            name: 'multi_platform_client_spin_server_test',
            testDefinition: 'multi_platform_client_spin_server_test',
            config: 'final',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            useSpinBuild: true,
            isTestWithLooseFiles: true,
            useTempDeploymentIfLooseFiles: false,
            uploadJournals: false,
            runBilbo: false,
            branches: [
                branchConfiguration(CH1_CONTENT_DEV, multi_platform_client_spin_server_ch1_content_dev, 'H */6 * * 1-7'),
            ]
        )
        categories += new AutotestCategory(
            name: 'pt_perf_highpriority_UltSpec_performance_setup',
            testDefinition: 'pt_perf_highpriority_UltSpec_performance_setup',
            runBilbo: true,
            needGameServer: true,
            config: 'performance',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            uploadJournals: true,
            captureVideo: false,
            isTestWithLooseFiles: true,
            useTempDeploymentIfLooseFiles: false,
            branches: [
                branchConfiguration(CH1_CONTENT_DEV, pt_perf_highpriority_UltSpec_performance_setup, 'H 0,12 * * 1-7'),
            ]
        )
        categories += new AutotestCategory(
            name: 'pt_perf_AlphaValidation_UltSpec_BFlabs_performance_setup_release',
            testDefinition: 'pt_perf_AlphaValidation_UltSpec_BFlabs_performance_setup_release',
            runBilbo: true,
            needGameServer: true,
            config: 'performance',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            uploadJournals: true,
            captureVideo: false,
            isTestWithLooseFiles: true,
            useTempDeploymentIfLooseFiles: false,
            branches: [
                branchConfiguration(CH1_RELEASE, pt_perf_AlphaValidation_UltSpec_BFlabs_performance_setup_release, 'H 4,15 * * 1-7'),
            ]
        )
        categories += new AutotestCategory(
            name: 'pt_perf_AlphaValidation_UltSpec_BFlabs_performance_setup',
            testDefinition: 'pt_perf_AlphaValidation_UltSpec_BFlabs_performance_setup',
            runBilbo: true,
            needGameServer: true,
            config: 'performance',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            uploadJournals: true,
            captureVideo: false,
            isTestWithLooseFiles: true,
            useTempDeploymentIfLooseFiles: false,
            branches: [
                branchConfiguration(CH1_STAGE, pt_perf_AlphaValidation_UltSpec_BFlabs_performance_setup, 'H 0,9,18 * * 1-7'),
            ]
        )/*
        categories += new AutotestCategory(
            name: 'pt_perf_highpriority_UltSpec_final_setup',
            testDefinition: 'pt_perf_highpriority_UltSpec_final_setup',
            runBilbo: true,
            needGameServer: true,
            config: 'final',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            uploadJournals: true,
            captureVideo: true,
            isTestWithLooseFiles: true,
            useTempDeploymentIfLooseFiles: false,
            branches: [
                 branchConfiguration(CH1_CONTENT_DEV, pt_perf_highpriority_UltSpec_final_setup, 'H 0 * * 1-6'),
            ]
        )*/
        categories += new AutotestCategory(
            name: 'pt_perf_highpriority_minspec_performance_setup',
            testDefinition: 'pt_perf_highpriority_minspec_performance_setup',
            runBilbo: true,
            needGameServer: true,
            config: 'performance',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            uploadJournals: true,
            captureVideo: false,
            isTestWithLooseFiles: true,
            copyLooseFileBuildToWindowsShareTimeoutSeconds: 4200,
            useTempDeploymentIfLooseFiles: false,
            branches: [
                branchConfiguration(CH1_CONTENT_DEV, pt_perf_highpriority_minspec_performance_setup, 'H 8 * * 1-6'),
            ]
        )/*
            categories += new AutotestCategory(
                name: 'pt_perf_highpriority_minspec_performance_setup_AlphaValidation_MinSpec_Coverage',
                testDefinition: 'pt_perf_highpriority_minspec_performance_setup_AlphaValidation_MinSpec_Coverage',
                runBilbo: true,
                needGameServer: true,
                config: 'performance',
                serverConfig: 'final',
                region: Region.WW,
                format: 'files',
                uploadJournals: true,
                captureVideo: false,
                isTestWithLooseFiles: true,
                copyLooseFileBuildToWindowsShareTimeoutSeconds: 4200,
                useTempDeploymentIfLooseFiles: false,
                branches: [
                    branchConfiguration(CH1_STAGE, pt_perf_highpriority_minspec_performance_setup_AlphaValidation_MinSpec_Coverage, 'H 9 * * 1-7'),
                ]
            )
            categories += new AutotestCategory(
                name: 'pt_perf_highpriority_minspec_performance_setup_AlphaValidation_MinSpec_HighPrio',
                testDefinition: 'pt_perf_highpriority_minspec_performance_setup_AlphaValidation_MinSpec_HighPrio',
                runBilbo: true,
                needGameServer: true,
                config: 'performance',
                serverConfig: 'final',
                region: Region.WW,
                format: 'files',
                uploadJournals: true,
                captureVideo: false,
                isTestWithLooseFiles: true,
                copyLooseFileBuildToWindowsShareTimeoutSeconds: 4200,
                useTempDeploymentIfLooseFiles: false,
                branches: [
                    branchConfiguration(CH1_STAGE, pt_perf_highpriority_minspec_performance_setup_AlphaValidation_MinSpec_HighPrio, 'H 0,5,10,15 * * 1-7'),
                    branchConfiguration(CH1_RELEASE, pt_perf_highpriority_minspec_performance_setup_AlphaValidation_HighPrio, 'H 4,15 * * 1-7'),
                ]
            )*/
        return categories
    }

    List<AutotestCategory> getTestCategories3() {
        List<AutotestCategory> categories = []
        categories += new AutotestCategory(
            name: 'pt_perf_highpriority_minspec_performance_setup_AlphaValidation_MinSpec_BFLabs_release',
            testDefinition: 'pt_perf_highpriority_minspec_performance_setup_AlphaValidation_MinSpec_BFLabs_release',
            runBilbo: true,
            needGameServer: true,
            config: 'performance',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            uploadJournals: true,
            captureVideo: false,
            isTestWithLooseFiles: true,
            copyLooseFileBuildToWindowsShareTimeoutSeconds: 4200,
            useTempDeploymentIfLooseFiles: false,
            branches: [
                branchConfiguration(CH1_RELEASE, pt_perf_highpriority_minspec_performance_setup_AlphaValidation_MinSpec_BFLabs_release, 'H 20,4 * * 1-7'),
            ]
        )
        categories += new AutotestCategory(
            name: 'pt_perf_highpriority_minspec_performance_setup_AlphaValidation_MinSpec_BFLabs',
            testDefinition: 'pt_perf_highpriority_minspec_performance_setup_AlphaValidation_MinSpec_BFLabs',
            runBilbo: true,
            needGameServer: true,
            config: 'performance',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            uploadJournals: true,
            captureVideo: false,
            isTestWithLooseFiles: true,
            copyLooseFileBuildToWindowsShareTimeoutSeconds: 4200,
            useTempDeploymentIfLooseFiles: false,
            branches: [
                branchConfiguration(CH1_STAGE, pt_perf_highpriority_minspec_performance_setup_AlphaValidation_MinSpec_BFLabs, 'H 0,7,14 * * 1-7'),
            ]
        )
        categories += new AutotestCategory(
            name: 'pt_perf_highpriority_minspec_final_setup',
            testDefinition: 'pt_perf_highpriority_minspec_final_setup',
            runBilbo: true,
            needGameServer: true,
            config: 'final',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            uploadJournals: true,
            captureVideo: false,
            isTestWithLooseFiles: true,
            copyLooseFileBuildToWindowsShareTimeoutSeconds: 4200,
            useTempDeploymentIfLooseFiles: false,
            branches: [
                branchConfiguration(CH1_CONTENT_DEV, pt_perf_highpriority_minspec_final_setup, 'H 0,5,10,19 * * 1-6'),
            ]
        )
        categories += new AutotestCategory(
            name: 'pt_perf_recspec_highpriority_performance_setup',
            testDefinition: 'pt_perf_recspec_highpriority_performance_setup',
            runBilbo: true,
            needGameServer: true,
            config: 'performance',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            uploadJournals: true,
            captureVideo: false,
            isTestWithLooseFiles: true,
            useTempDeploymentIfLooseFiles: false,
            branches: [
                branchConfiguration(CH1_CONTENT_DEV, pt_perf_recspec_highpriority_performance_setup, 'H 1,14 * * 1-6'),
            ]
        )
        categories += new AutotestCategory(
            name: 'pt_perf_minspec_sp_perf_performance',
            testDefinition: 'pt_perf_minspec_sp_perf_performance',
            runBilbo: true,
            needGameServer: true,
            config: 'performance',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            uploadJournals: true,
            captureVideo: false,
            isTestWithLooseFiles: true,
            useTempDeploymentIfLooseFiles: false,
            branches: [
                branchConfiguration(CH1_CONTENT_DEV, pt_perf_minspec_sp_perf_performance, 'H 1,5,11,17,22 * * 1-6'),
                /*branchConfiguration(CH1_STAGE, pt_perf_minspec_sp_perf_performance, 'H 16,0 * * 1-6'),*/
            ]
        )
        categories += new AutotestCategory(
            name: 'pt_perf_sp_perf_performance_FirstBatch',
            testDefinition: 'pt_perf_sp_perf_performance_FirstBatch',
            runBilbo: true,
            needGameServer: true,
            config: 'performance',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            uploadJournals: true,
            captureVideo: false,
            isTestWithLooseFiles: true,
            useTempDeploymentIfLooseFiles: false,
            branches: [
                branchConfiguration(CH1_CONTENT_DEV, pt_perf_sp_perf_performance_FirstBatch, 'H 19,22 * * 1-6'),
                branchConfiguration(CH1_2024_TO_CH1, pt_perf_sp_perf_performance_FirstBatch, 'H 8 * * 1-6'),
                /*branchConfiguration(CH1_STAGE, pt_perf_sp_perf_performance_FirstBatch, 'H 6,19 * * 1-6'),*/
            ]
        )
        categories += new AutotestCategory(
            name: 'pt_perf_sp_perf_performance_SecondBatch',
            testDefinition: 'pt_perf_sp_perf_performance_SecondBatch',
            runBilbo: true,
            needGameServer: true,
            config: 'performance',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            uploadJournals: true,
            captureVideo: false,
            isTestWithLooseFiles: true,
            useTempDeploymentIfLooseFiles: false,
            branches: [
                branchConfiguration(CH1_CONTENT_DEV, pt_perf_sp_perf_performance_SecondBatch, 'H 8,17 * * 1-6'),
                branchConfiguration(CH1_2024_TO_CH1, pt_perf_sp_perf_performance_SecondBatch, 'H 5 * * 1-6'),
                /*branchConfiguration(CH1_STAGE, pt_perf_sp_perf_performance_SecondBatch, 'H 12,23 * * 1-6'),*/
            ]
        )/*
        categories += new AutotestCategory(
            name: 'pt_perf_sp_perf_performance_two',
            testDefinition: 'pt_perf_sp_perf_performance_two',
            runBilbo: true,
            needGameServer: true,
            config: 'performance',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            uploadJournals: true,
            captureVideo: false,
            isTestWithLooseFiles: true,
            useTempDeploymentIfLooseFiles: false,
            branches: [
                branchConfiguration(CH1_STAGE, pt_perf_sp_perf_performance, 'H 4,15 * * 1-6'),
            ]
        )*/
        categories += new AutotestCategory(
            name: 'pt_perf_trinity_sp_perf_performance',
            testDefinition: 'pt_perf_trinity_sp_perf_performance',
            runBilbo: true,
            needGameServer: true,
            config: 'performance',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            uploadJournals: true,
            captureVideo: false,
            isTestWithLooseFiles: true,
            useTempDeploymentIfLooseFiles: false,
            branches: [
                branchConfiguration(CH1_CONTENT_DEV, pt_perf_trinity_sp_perf_performance, 'H 4 * * 1-6'),
                /*branchConfiguration(CH1_STAGE, pt_perf_trinity_sp_perf_performance, 'H 17,21 * * 1-6'),*/
            ]
        )
        categories += new AutotestCategory(
            name: 'pt_perf_xbss_sp_perf_performance',
            testDefinition: 'pt_perf_xbss_sp_perf_performance',
            runBilbo: true,
            needGameServer: true,
            config: 'performance',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            uploadJournals: true,
            captureVideo: false,
            isTestWithLooseFiles: true,
            useTempDeploymentIfLooseFiles: false,
            branches: [
                branchConfiguration(CH1_CONTENT_DEV, pt_perf_xbss_sp_perf_performance, 'H 22 * * 1-6'),
                /*branchConfiguration(CH1_STAGE, pt_perf_xbss_sp_perf_performance, 'H 10,16,3 * * 1-6'),*/
            ]
        )
        categories += new AutotestCategory(
            name: 'pt_perf_xbss_meta_performance_setup',
            testDefinition: 'pt_perf_xbss_meta_performance_setup',
            runBilbo: true,
            needGameServer: true,
            config: 'performance',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            uploadJournals: true,
            captureVideo: false,
            isTestWithLooseFiles: true,
            useTempDeploymentIfLooseFiles: false,
            branches: [
                branchConfiguration(CH1_CONTENT_DEV, pt_perf_xbss_meta_performance_setup, 'H 5,21 * * 1-6'),
            ]
        )
        categories += new AutotestCategory(
            name: 'pt_perf_recspec_highpriority_performance_setup_2',
            testDefinition: 'pt_perf_recspec_highpriority_performance_setup_2',
            runBilbo: true,
            needGameServer: true,
            config: 'performance',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            uploadJournals: true,
            captureVideo: false,
            isTestWithLooseFiles: true,
            useTempDeploymentIfLooseFiles: false,
            branches: [
                branchConfiguration(CH1_CONTENT_DEV, pt_perf_recspec_highpriority_performance_setup_2, 'H 22,9 * * 1-6'),
                branchConfiguration(CH1_RELEASE, pt_perf_recspec_performance_ch1_release, 'H 5 * * 1-7')
            ]
        )/*
        categories += new AutotestCategory(
            name: 'pt_perf_recspec_highpriority_performance_setup_eala',
            testDefinition: 'pt_perf_recspec_highpriority_performance_setup_eala',
            runBilbo: true,
            needGameServer: true,
            config: 'performance',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            remoteLabel: 'eala',
            uploadJournals: true,
            captureVideo: false,
            isTestWithLooseFiles: true,
            useTempDeploymentIfLooseFiles: false,
            branches: [
                branchConfiguration(CH1_STAGE, pt_perf_recspec_highpriority_performance_setup_eala, 'H 0,17 * * 1-7'),
            ]
        )
        )
            categories += new AutotestCategory(
                name: 'pt_perf_recspec_highpriority_performance_setup_AlphaValidation_RecSpec_Coverage',
                testDefinition: 'pt_perf_recspec_highpriority_performance_setup_AlphaValidation_RecSpec_Coverage',
                runBilbo: true,
                needGameServer: true,
                config: 'performance',
                serverConfig: 'final',
                region: Region.WW,
                format: 'files',
                uploadJournals: true,
                captureVideo: false,
                isTestWithLooseFiles: true,
                useTempDeploymentIfLooseFiles: false,
                branches: [
                    branchConfiguration(CH1_STAGE, pt_perf_recspec_highpriority_performance_setup_AlphaValidation_RecSpec_Coverage, 'H 17 * * 1-7'),
                ]
            )
            categories += new AutotestCategory(
                name: 'pt_perf_recspec_highpriority_performance_setup_AlphaValidation_RecSpec_HighPrio',
                testDefinition: 'pt_perf_recspec_highpriority_performance_setup_AlphaValidation_RecSpec_HighPrio',
                runBilbo: true,
                needGameServer: true,
                config: 'performance',
                serverConfig: 'final',
                region: Region.WW,
                format: 'files',
                uploadJournals: true,
                captureVideo: false,
                isTestWithLooseFiles: true,
                useTempDeploymentIfLooseFiles: false,
                branches: [
                    branchConfiguration(CH1_STAGE, pt_perf_recspec_highpriority_performance_setup_AlphaValidation_RecSpec_HighPrio, 'H 2,10,18 * * 1-7'),
                    branchConfiguration(CH1_RELEASE, pt_perf_recspec_highpriority_performance_setup_AlphaValidation_RecSpec_HighPrio_ch1_release, 'H 1,16 * * 1-7'),
                ]
            )*/
        categories += new AutotestCategory(
            name: 'pt_perf_recspec_highpriority_performance_setup_AlphaValidation_RecSpec_BFLabs_release',
            testDefinition: 'pt_perf_recspec_highpriority_performance_setup_AlphaValidation_RecSpec_BFLabs_release',
            runBilbo: true,
            needGameServer: true,
            config: 'performance',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            uploadJournals: true,
            captureVideo: false,
            isTestWithLooseFiles: true,
            useTempDeploymentIfLooseFiles: false,
            branches: [
                branchConfiguration(CH1_RELEASE, pt_perf_recspec_highpriority_performance_setup_AlphaValidation_RecSpec_BFLabs_release, 'H 22,9 * * 1-7'),
            ]
        )
        categories += new AutotestCategory(
            name: 'pt_perf_recspec_highpriority_performance_setup_AlphaValidation_RecSpec_BFLabs',
            testDefinition: 'pt_perf_recspec_highpriority_performance_setup_AlphaValidation_RecSpec_BFLabs',
            runBilbo: true,
            needGameServer: true,
            config: 'performance',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            uploadJournals: true,
            captureVideo: false,
            isTestWithLooseFiles: true,
            useTempDeploymentIfLooseFiles: false,
            branches: [
                branchConfiguration(CH1_STAGE, pt_perf_recspec_highpriority_performance_setup_AlphaValidation_RecSpec_BFLabs, 'H 6 * * 1-7'),
            ]
        )
        categories += new AutotestCategory(
            name: 'pt_perf_recspec_highpriority_final_setup',
            testDefinition: 'pt_perf_recspec_highpriority_final_setup',
            runBilbo: true,
            needGameServer: true,
            config: 'final',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            uploadJournals: true,
            captureVideo: false,
            isTestWithLooseFiles: true,
            useTempDeploymentIfLooseFiles: false,
            branches: [
                branchConfiguration(CH1_CONTENT_DEV, pt_perf_recspec_highpriority_final_setup, 'H 9,15 * * 1-6'),
            ]
        )
        categories += new AutotestCategory(
            name: 'pt_mem_trinity_final',
            testDefinition: 'pt_mem_trinity_final',
            runBilbo: true,
            needGameServer: true,
            config: 'final',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            remoteLabel: 'eala',
            uploadJournals: true,
            captureVideo: false,
            isTestWithLooseFiles: true,
            useTempDeploymentIfLooseFiles: false,
            branches: [
                branchConfiguration(CH1_STAGE, pt_mem_trinity_final, 'H 2,17 * * 1-6'),
            ]
        )
        categories += new AutotestCategory(
            name: 'pt_mem_trinity_final_release',
            testDefinition: 'pt_mem_trinity_final_release',
            runBilbo: true,
            needGameServer: true,
            config: 'final',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            remoteLabel: 'eala',
            uploadJournals: true,
            captureVideo: false,
            isTestWithLooseFiles: true,
            useTempDeploymentIfLooseFiles: false,
            branches: [
                branchConfiguration(CH1_RELEASE, pt_mem_trinity_final_release, 'H 5,14 * * 1-6'),
            ]
        )
        categories += new AutotestCategory(
            name: 'pt_perf_trinity_sp_final',
            testDefinition: 'pt_perf_trinity_sp_final',
            runBilbo: true,
            needGameServer: true,
            config: 'final',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            remoteLabel: 'eala',
            uploadJournals: true,
            captureVideo: false,
            isTestWithLooseFiles: true,
            useTempDeploymentIfLooseFiles: false,
            branches: [
                branchConfiguration(CH1_CONTENT_DEV, pt_perf_trinity_sp_final, 'H 7,15,22 * * 1-6'),
                /*branchConfiguration(CH1_STAGE, pt_perf_trinity_sp_final, 'H 2,6,12 * * 1-6'),*/
            ]
        )
        categories += new AutotestCategory(
            name: 'unittests_asan_tool',
            testDefinition: 'unittests_asan_tool',
            runBilbo: false,
            buildType: 'dll',
            config: 'release',
            elipyCmd: 'icepick_run_codetests',
            needGameServer: false,
            uploadJournals: false,
            captureVideo: false,
            useExistingFilerBuild: true,
            customTag: 'asan',
            branches: [
                branchConfiguration(CH1_CONTENT_DEV_SANITIZERS, unittests_asan_tool, 'H H/4 * * 1-7'),
            ]
        )
        categories += new AutotestCategory(
            name: 'unittests_asan_static',
            testDefinition: 'unittests_asan_static',
            runBilbo: false,
            config: 'final',
            elipyCmd: 'icepick_run_codetests',
            needGameServer: false,
            uploadJournals: false,
            captureVideo: false,
            useExistingFilerBuild: true,
            customTag: 'asan',
            branches: [
                branchConfiguration(CH1_CONTENT_DEV_SANITIZERS, unittests_asan_static, 'H 5,7,10,12,22 * * 1-7'),
            ]
        )
        categories += new AutotestCategory(
            name: 'unittests_ubsan_static',
            testDefinition: 'unittests_ubsan_static',
            runBilbo: false,
            config: 'final',
            elipyCmd: 'icepick_run_codetests',
            needGameServer: false,
            uploadJournals: false,
            captureVideo: false,
            useExistingFilerBuild: true,
            customTag: 'ubsan',
            branches: [
                branchConfiguration(CH1_CONTENT_DEV_SANITIZERS, unittests_ubsan_static, 'H H/4 * * 1-7'),
            ]
        )
        categories += new AutotestCategory(
            name: 'pt_func_mutators',
            testDefinition: 'pt_func_mutators',
            runBilbo: true,
            needGameServer: true,
            uploadJournals: false,
            captureVideo: true,
            branches: [
                branchConfiguration(CH1_CONTENT_DEV, pt_func_mutators, 'H 8 * * 1-7')
            ]
        )
        categories += new AutotestCategory(
            name: 'release_setup_aitests_DICEAI',
            testDefinition: 'release_setup_aitests_DICEAI',
            runBilbo: true,
            needGameServer: false,
            config: 'release',
            uploadJournals: false,
            captureVideo: true,
            branches: [
                branchConfiguration(CH1_CONTENT_DEV, release_setup_aitests_DICEAI, 'H 20 * * 1-7')
            ]
        )
        categories += new FrostedAutotestCategory(
            name: 'frostedtests_workflow_frostbite',
            testDefinition: 'frostedtests_workflow_frostbite',
            remoteLabel: 'eala',
            runBilbo: false,
            needGameServer: false,
            uploadJournals: false,
            branches: [
                branchConfiguration(CH1_CODE_DEV, frostedtests_workflow_frostbite, 'H 19 * * 1-7'),
                branchConfiguration(CH1_CONTENT_DEV, frostedtests_workflow_frostbite, 'H 7 * * 1-7'),
                branchConfiguration(ECS_SPLINES, frostedtests_workflow_frostbite, 'H 4 * * 1-7'),
            ]
        )
        categories += new FrostedAutotestCategory(
            name: 'frostedtests_workflow_battlefield',
            testDefinition: 'frostedtests_workflow_battlefield',
            remoteLabel: 'eala',
            runBilbo: false,
            needGameServer: false,
            uploadJournals: false,
            branches: [
                branchConfiguration(CH1_CODE_DEV, frostedtests_workflow_battlefield, 'H 19 * * 1-7'),
                branchConfiguration(CH1_CONTENT_DEV, frostedtests_workflow_battlefield, 'H 8 * * 1-7'),
                branchConfiguration(ECS_SPLINES, frostedtests_workflow_battlefield, 'H 5 * * 1-7'),
            ]
        )
        categories += new FrostedAutotestCategory(
            name: 'frostedtests_extended',
            testDefinition: 'frostedtests_extended',
            remoteLabel: 'eala',
            runBilbo: false,
            needGameServer: false,
            uploadJournals: false,
            branches: [
                branchConfiguration(CH1_CONTENT_DEV, frostedTestsCH1CODEDEVExtended, 'H 0,8,16,21 * * 1-7'),
                branchConfiguration(CH1_2024_TO_CH1, frostedTestsCH1CODEDEVExtended, 'H 2 * * 1-7'),
                branchConfiguration(ECS_SPLINES, frostedTestsECSSplines, 'H 4,17 * * 1-7'),
            ]
        )
        categories += new AutotestCategory(
            name: 'lkg_qv_win',
            testDefinition: 'lkg_qv_win',
            enableP4Counters: true,
            runBilbo: true,
            needGameServer: true,
            uploadJournals: true,
            captureVideo: true,
            branches: [
                branchConfiguration(CH1_CODE_DEV, lkg_qv_win, 'H 0,3,6,9,12,15,18,21 * * 1-7'),
                branchConfiguration(CH1_CONTENT_DEV, lkg_qv_win, 'H 1,4,7,10,13,16,19,22 * * 1-7'),
            ]
        )
        categories += new AutotestCategory(
            name: 'lkg_qv_win_criterion',
            testDefinition: 'lkg_qv_win_criterion',
            enableP4Counters: true,
            runBilbo: true,
            needGameServer: true,
            uploadJournals: true,
            captureVideo: true,
            branches: [
                branchConfiguration(CH1_CONTENT_DEV, lkg_qv_win_criterion, 'H 0,3 * * 1-7'),
            ]
        )
        categories += new AutotestCategory(
            name: 'pt_stab',
            testDefinition: 'pt_stab',
            runBilbo: true,
            needGameServer: true,
            config: 'final',
            uploadJournals: false,
            captureVideo: true,
            branches: [
                /*branchConfiguration(CH1_CODE_DEV, pt_stab, 'H 3,20 * * 1-7'),
                branchConfiguration(CH1_STAGE, pt_stab_ch1_stage, 'H 5,17 * * 1-7'),
                branchConfiguration(CH1_RELEASE, pt_stab, 'H 3,13 * * 1-7'),
                branchConfiguration(CH1_2024_TO_CH1, pt_stab, 'H 11,22 * * 1-7'),*/
            ]
        )/*
            categories += new AutotestCategory(
                name: 'mp_stab_test_crash',
                testDefinition: 'mp_stab_test_crash',
                runBilbo: true,
                needGameServer: true,
                config: 'final',
                uploadJournals: false,
                captureVideo: true,
                branches: [
                    branchConfiguration(CH1_CODE_DEV, mp_stab_test_crash, 'H H/3 * * 1-7'),
                ]
            )*/
        categories += new AutotestCategory(
            name: 'pt_stab_soak_mp_ch1_code',
            testDefinition: 'pt_stab_soak_mp_ch1_code',
            config: 'final',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            useSpinBuild: true,
            isTestWithLooseFiles: true,
            useTempDeploymentIfLooseFiles: false,
            uploadJournals: false,
            runBilbo: false,
            branches: [
                branchConfiguration(CH1_CONTENT_DEV, pt_stab_soak_mp_ch1_code, 'H 6,14,22 * * 1-7'),
            ]
        )/*
        categories += new AutotestCategory(
            name: 'pt_stab_soak_ps5',
            testDefinition: 'pt_stab_soak_ps5',
            runBilbo: true,
            needGameServer: true,
            config: 'final',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            isTestWithLooseFiles: true,
            useTempDeploymentIfLooseFiles: false,
            uploadJournals: false,
            captureVideo: true,
            branches: [
                branchConfiguration(CH1_CONTENT_DEV, pt_stab_soak_ps5, 'H 14,0 * * 1-7'),
            ]
        )
        categories += new AutotestCategory(
            name: 'pt_stab_soak_xbsx',
            testDefinition: 'pt_stab_soak_xbsx',
            runBilbo: true,
            needGameServer: true,
            config: 'final',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            isTestWithLooseFiles: true,
            useTempDeploymentIfLooseFiles: false,
            uploadJournals: false,
            captureVideo: true,
            branches: [
                branchConfiguration(CH1_CONTENT_DEV, pt_stab_soak_xbsx, 'H 6,17 * * 1-7'),
            ]
        )
        categories += new AutotestCategory(
            name: 'pt_stab_soak_win',
            testDefinition: 'pt_stab_soak_win',
            runBilbo: true,
            needGameServer: true,
            config: 'final',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            isTestWithLooseFiles: true,
            useTempDeploymentIfLooseFiles: false,
            uploadJournals: false,
            captureVideo: true,
            branches: [
                branchConfiguration(CH1_CONTENT_DEV, pt_stab_soak_win, 'H 8,19 * * 1-7'),
            ]
        )*/
        categories += new AutotestCategory(
            name: 'lkg_checkmate_beta',
            testDefinition: 'lkg_checkmate_beta',
            enableP4Counters: true,
            config: 'release',
            runBilbo: true,
            needGameServer: false,
            uploadJournals: true,
            captureVideo: true,
            buildType: 'dll',
            branches: [
                branchConfiguration(CH1_CODE_DEV, lkg_checkmate_beta, '10,40 * * * 1-7'),
                branchConfiguration(CH1_CONTENT_DEV, lkg_checkmate_beta, '10,40 * * * 1-7'),
            ]
        )
        categories += new AutotestCategory(
            name: 'lkg_checkmate_alpha',
            testDefinition: 'lkg_checkmate_alpha',
            enableP4Counters: true,
            config: 'release',
            runBilbo: true,
            needGameServer: false,
            uploadJournals: true,
            captureVideo: true,
            buildType: 'dll',
            branches: [
                branchConfiguration(CH1_CODE_DEV, lkg_checkmate_alpha, 'H 7,9,13,15 * * 1-7'),
                branchConfiguration(CH1_CONTENT_DEV, lkg_checkmate_alpha, 'H 7,9,13,15 * * 1-7'),
            ]
        )
        categories += new AutotestCategory(
            name: 'unittests',
            testDefinition: 'unittests',
            runBilbo: false,
            buildType: 'dll',
            config: 'release',
            elipyCmd: 'icepick_run_codetests',
            needGameServer: false,
            uploadJournals: false,
            captureVideo: false,
            branches: [
                branchConfiguration(CH1_CONTENT_DEV, unittests, 'H 0,6,11 * * 1-7'),
                branchConfiguration(CH1_2024_TO_CH1, unittests, 'H 5,11,18 * * 1-7'),
            ]
        )
        categories += new AutotestCategory(
            name: 'unittests_engine',
            testDefinition: 'unittests_engine',
            runBilbo: false,
            buildType: 'dll',
            config: 'release',
            elipyCmd: 'icepick_run_codetests',
            needGameServer: false,
            uploadJournals: false,
            captureVideo: false,
            branches: [
                branchConfiguration(CH1_CONTENT_DEV, unittests_engine, 'H 0,11,21 * * 1-7'),
                branchConfiguration(CH1_2024_TO_CH1, unittests_engine, 'H 2,13,23 * * 1-7'),
                branchConfiguration(CH1_TO_TRUNK, unittests_engine, 'H 6,16,0 * * 1-7'),
                branchConfiguration(ECS_SPLINES, unittests_engine, 'H 3,10,16 * * 1-7'),
            ]
        )
        categories += new AutotestCategory(
            name: 'pt_func_A2B_one',
            testDefinition: 'pt_func_A2B_one',
            runBilbo: true,
            needGameServer: true,
            uploadJournals: false,
            captureVideo: true,
            branches: [
                branchConfiguration(CH1_CONTENT_DEV, pt_func_A2B_one, 'H 0,6 * * 1-7'),
            ]
        )
        categories += new AutotestCategory(
            name: 'pt_func_A2B_two',
            testDefinition: 'pt_func_A2B_two',
            runBilbo: true,
            needGameServer: true,
            uploadJournals: false,
            captureVideo: true,
            branches: [
                branchConfiguration(CH1_CONTENT_DEV, pt_func_A2B_two, 'H 12,18 * * 1-7'),
            ]
        )
        categories += new AutotestCategory(
            name: 'pt_func_SP',
            testDefinition: 'pt_func_SP',
            runBilbo: true,
            needGameServer: true,
            uploadJournals: false,
            captureVideo: true,
            branches: [
                branchConfiguration(CH1_CONTENT_DEV, pt_func_SP, 'H 9,15 * * 1-7'),
            ]
        )
        categories += new AutotestCategory(
            name: 'pt_func_CC',
            testDefinition: 'pt_func_CC',
            runBilbo: true,
            needGameServer: true,
            uploadJournals: false,
            captureVideo: true,
            branches: [
                branchConfiguration(CH1_CONTENT_DEV, pt_func_CC, 'H 22 * * 1-7'),
            ]
        )
        categories += new AutotestCategory(
            name: 'pt_func_META',
            testDefinition: 'pt_func_META',
            runBilbo: true,
            needGameServer: true,
            uploadJournals: false,
            captureVideo: true,
            branches: [
                branchConfiguration(CH1_CONTENT_DEV, pt_func_META, 'H 9 * * 1-7'),
            ]
        )/*
        categories += new AutotestCategory(
            name: 'pt_func_temp',
            testDefinition: 'pt_func_temp',
            runBilbo: true,
            needGameServer: true,
            uploadJournals: false,
            captureVideo: true,
            branches: [
                branchConfiguration(CH1_STAGE, pt_func_temp, 'H 14 * * 1-7'),
            ]
        )*/
        categories += new AutotestCategory(
            name: 'pt_func_content',
            testDefinition: 'pt_func_content',
            runBilbo: true,
            needGameServer: true,
            uploadJournals: false,
            captureVideo: true,
            branches: [
                branchConfiguration(CH1_CONTENT_DEV, pt_func_content, 'H 4 * * 1-7'),
            ]
        )
        categories += new AutotestCategory(
            name: 'pt_func_win64',
            testDefinition: 'pt_func_win64',
            runBilbo: true,
            needGameServer: true,
            uploadJournals: false,
            captureVideo: true,
            branches: [
                branchConfiguration(CH1_CONTENT_DEV, pt_func_win64, 'H 10,0 * * 1-7'),
            ]
        )
        categories += new AutotestCategory(
            name: 'releasetests',
            testDefinition: 'releasetests',
            runBilbo: true,
            needGameServer: false,
            config: 'release',
            uploadJournals: false,
            captureVideo: true,
            branches: [
                branchConfiguration(CH1_CODE_DEV, releasetests, 'H 16 * * 1-7'),
                branchConfiguration(CH1_CONTENT_DEV, releasetests, 'H 5 * * 1-7'),
            ]
        )
        categories += new FrostedAutotestCategory(
            name: 'frosted_vstests',
            testDefinition: 'frosted_vstests',
            remoteLabel: 'eala',
            runBilbo: false,
            needGameServer: false,
            uploadJournals: false,
            branches: [
                branchConfiguration(CH1_CODE_DEV, frosted_vstests, 'H 22 * * 1-7'),
                branchConfiguration(CH1_CONTENT_DEV, frosted_vstests, 'H 10,2 * * 1-7'),
                branchConfiguration(TASK2, frosted_vstests, 'H 12 * * 1-7'),
            ]
        )

        return categories
    }

    List<AutotestCategory> getTestCategories4() {
        List<AutotestCategory> categories = []
        categories += new AutotestCategory(
            name: 'pt_perf_ultspec_sp_perf_performance_firstbatch',
            testDefinition: 'pt_perf_ultspec_sp_perf_performance_firstbatch',
            runBilbo: true,
            needGameServer: true,
            config: 'performance',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            uploadJournals: true,
            captureVideo: false,
            isTestWithLooseFiles: true,
            useTempDeploymentIfLooseFiles: false,
            branches: [
                branchConfiguration(CH1_CONTENT_DEV, pt_perf_ultspec_sp_perf_performance_firstbatch, 'H 12,19 * * 1-6'),
                ]
            )
        categories += new AutotestCategory(
            name: 'pt_perf_ultspec_sp_perf_performance_secondbatch',
            testDefinition: 'pt_perf_ultspec_sp_perf_performance_secondbatch',
            runBilbo: true,
            needGameServer: true,
            config: 'performance',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            uploadJournals: true,
            captureVideo: false,
            isTestWithLooseFiles: true,
            useTempDeploymentIfLooseFiles: false,
            branches: [
                branchConfiguration(CH1_CONTENT_DEV, pt_perf_ultspec_sp_perf_performance_secondbatch, 'H 7,23 * * 1-6'),
                ]
            )
        categories += new AutotestCategory(
            name: 'pt_perf_recspec_sp_perf_performance_firstbatch',
            testDefinition: 'pt_perf_recspec_sp_perf_performance_firstbatch',
            runBilbo: true,
            needGameServer: true,
            config: 'performance',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            uploadJournals: true,
            captureVideo: false,
            isTestWithLooseFiles: true,
            useTempDeploymentIfLooseFiles: false,
            branches: [
                branchConfiguration(CH1_CONTENT_DEV, pt_perf_recspec_sp_perf_performance_firstbatch, 'H 23 * * 1-6'),
                /*branchConfiguration(CH1_STAGE, pt_perf_recspec_sp_perf_performance_firstbatch, 'H 0,8,16 * * 1-6'),*/
                ]
            )
        categories += new AutotestCategory(
            name: 'pt_perf_recspec_sp_perf_performance_secondbatch',
            testDefinition: 'pt_perf_recspec_sp_perf_performance_secondbatch',
            runBilbo: true,
            needGameServer: true,
            config: 'performance',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            uploadJournals: true,
            captureVideo: false,
            isTestWithLooseFiles: true,
            useTempDeploymentIfLooseFiles: false,
            branches: [
                branchConfiguration(CH1_CONTENT_DEV, pt_perf_recspec_sp_perf_performance_secondbatch, 'H 5 * * 1-6'),
                /*branchConfiguration(CH1_STAGE, pt_perf_recspec_sp_perf_performance_secondbatch, 'H 0,8,16 * * 1-6'),*/
                ]
            )
        categories += new AutotestCategory(
            name: 'pt_perf_recspec_sp_final_FirstBatch',
            testDefinition: 'pt_perf_recspec_sp_final_FirstBatch',
            runBilbo: true,
            needGameServer: true,
            config: 'final',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            uploadJournals: true,
            captureVideo: false,
            isTestWithLooseFiles: true,
            useTempDeploymentIfLooseFiles: false,
            branches: [
                /*branchConfiguration(CH1_STAGE, pt_perf_recspec_sp_final_FirstBatch, 'H 16,2 * * 7'),*/
                branchConfiguration(CH1_CONTENT_DEV, pt_perf_recspec_sp_final_FirstBatch, 'H 7 * * 1-7')
            ]
        )
        categories += new AutotestCategory(
            name: 'pt_perf_recspec_sp_final_SecondBatch',
            testDefinition: 'pt_perf_recspec_sp_final_SecondBatch',
            runBilbo: true,
            needGameServer: true,
            config: 'final',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            uploadJournals: true,
            captureVideo: false,
            isTestWithLooseFiles: true,
            useTempDeploymentIfLooseFiles: false,
            branches: [
                /*branchConfiguration(CH1_STAGE, pt_perf_recspec_sp_final_SecondBatch, 'H 16,2 * * 7'),*/
                branchConfiguration(CH1_CONTENT_DEV, pt_perf_recspec_sp_final_SecondBatch, 'H 6 * * 1-7')
            ]
        )
        categories += new AutotestCategory(
            name: 'pt_perf_UltSpec_F2P_performance_setup',
            testDefinition: 'pt_perf_UltSpec_F2P_performance_setup',
            runBilbo: true,
            needGameServer: true,
            config: 'performance',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            uploadJournals: true,
            captureVideo: false,
            isTestWithLooseFiles: true,
            useTempDeploymentIfLooseFiles: false,
            branches: [
                branchConfiguration(CH1_CONTENT_DEV, pt_perf_UltSpec_F2P_performance_setup, 'H 0,14 * * 1-7'),
            ]
        )
        categories += new AutotestCategory(
            name: 'pt_perf_UltSpec_meta_performance_setup',
            testDefinition: 'pt_perf_UltSpec_meta_performance_setup',
            runBilbo: true,
            needGameServer: true,
            config: 'performance',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            uploadJournals: true,
            captureVideo: false,
            isTestWithLooseFiles: true,
            useTempDeploymentIfLooseFiles: false,
            branches: [
                branchConfiguration(CH1_CONTENT_DEV, pt_perf_UltSpec_meta_performance_setup, 'H 2,17 * * 1-7'),
            ]
        )
        categories += new AutotestCategory(
            name: 'lkg_qv_ecs',
            testDefinition: 'lkg_qv_ecs',
            enableP4Counters: true,
            runBilbo: true,
            needGameServer: false,
            uploadJournals: true,
            captureVideo: true,
            branches: [
                branchConfiguration(ECS_SPLINES, lkg_qv_ECS, 'H 2,14,22 * * 1-7'),
            ]
        )
        categories += new AutotestCategory(
            name: 'lkg_auto_ecs',
            testDefinition: 'lkg_auto_ecs',
            enableP4Counters: true,
            runBilbo: true,
            needGameServer: true,
            uploadJournals: true,
            captureVideo: true,
            branches: [
                branchConfiguration(ECS_SPLINES, lkg_auto_ECS, 'H 1,13,23 * * 1-7'),
            ]
        )
        categories += new AutotestCategory(
            name: 'lkg_checkmate_final',
            testDefinition: 'lkg_checkmate_final',
            enableP4Counters: true,
            config: 'release',
            runBilbo: true,
            needGameServer: false,
            uploadJournals: true,
            captureVideo: true,
            buildType: 'dll',
            branches: [
                branchConfiguration(ECS_SPLINES, lkg_checkmate, 'H 9,11,13 * * 1-7'),
            ]
        )
        return categories
    }

    List<AutotestCategory> getTestCategories5() {
        List<AutotestCategory> categories = []
        categories += new AutotestCategory(
            name: 'pt_perf_final_setup_seasons',
            testDefinition: 'pt_perf_final_setup_seasons',
            runBilbo: true,
            needGameServer: true,
            config: 'final',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            uploadJournals: true,
            captureVideo: false,
            isTestWithLooseFiles: true,
            useTempDeploymentIfLooseFiles: false,
            branches: [
                branchConfiguration(CH1_CONTENT_DEV_C1S2B1, pt_perf_final_setup_seasons, 'H 16,2 * * 1-6'),
                ]
            )
        categories += new AutotestCategory(
            name: 'pt_perf_performance_setup_seasons',
            testDefinition: 'pt_perf_performance_setup_seasons',
            runBilbo: true,
            needGameServer: true,
            config: 'performance',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            uploadJournals: true,
            captureVideo: false,
            isTestWithLooseFiles: true,
            useTempDeploymentIfLooseFiles: false,
            branches: [
                branchConfiguration(CH1_CONTENT_DEV_C1S2B1, pt_perf_performance_setup_seasons, 'H 7,13 * * 1-6'),
                ]
            )
        categories += new AutotestCategory(
            name: 'pt_perf_final_setup_seasons_MinSpec',
            testDefinition: 'pt_perf_final_setup_seasons_MinSpec',
            runBilbo: true,
            needGameServer: true,
            config: 'final',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            uploadJournals: true,
            captureVideo: false,
            isTestWithLooseFiles: true,
            useTempDeploymentIfLooseFiles: false,
            branches: [
                branchConfiguration(CH1_CONTENT_DEV_C1S2B1, pt_perf_final_setup_seasons_MinSpec, 'H 13,0 * * 1-6'),
                ]
            )

        categories += new AutotestCategory(
            name: 'pt_perf_performance_setup_seasons_MinSpec',
            testDefinition: 'pt_perf_performance_setup_seasons_MinSpec',
            runBilbo: true,
            needGameServer: true,
            config: 'performance',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            uploadJournals: true,
            captureVideo: false,
            isTestWithLooseFiles: true,
            useTempDeploymentIfLooseFiles: false,
            branches: [
                branchConfiguration(CH1_CONTENT_DEV_C1S2B1, pt_perf_performance_setup_seasons_MinSpec, 'H 6,22 * * 1-6'),
                ]
            )

        categories += new AutotestCategory(
            name: 'pt_perf_final_setup_seasons_RecSpec',
            testDefinition: 'pt_perf_final_setup_seasons_RecSpec',
            runBilbo: true,
            needGameServer: true,
            config: 'final',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            uploadJournals: true,
            captureVideo: false,
            isTestWithLooseFiles: true,
            useTempDeploymentIfLooseFiles: false,
            branches: [
                branchConfiguration(CH1_CONTENT_DEV_C1S2B1, pt_perf_final_setup_seasons_RecSpec, 'H 18,7 * * 1-6'),
                ]
            )

        categories += new AutotestCategory(
            name: 'pt_perf_performance_setup_seasons_RecSpec',
            testDefinition: 'pt_perf_performance_setup_seasons_RecSpec',
            runBilbo: true,
            needGameServer: true,
            config: 'performance',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            uploadJournals: true,
            captureVideo: false,
            isTestWithLooseFiles: true,
            useTempDeploymentIfLooseFiles: false,
            branches: [
                branchConfiguration(CH1_CONTENT_DEV_C1S2B1, pt_perf_performance_setup_seasons_RecSpec, 'H 14,4 * * 1-6'),
                ]
            )

        categories += new AutotestCategory(
            name: 'pt_perf_final_setup_seasons_Trinity',
            testDefinition: 'pt_perf_final_setup_seasons_Trinity',
            runBilbo: true,
            needGameServer: true,
            config: 'final',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            uploadJournals: true,
            captureVideo: false,
            isTestWithLooseFiles: true,
            useTempDeploymentIfLooseFiles: false,
            branches: [
                branchConfiguration(CH1_CONTENT_DEV_C1S2B1, pt_perf_final_setup_seasons_Trinity, 'H 14,8 * * 1-6'),
                ]
            )

        categories += new AutotestCategory(
            name: 'pt_perf_performance_setup_seasons_Trinity',
            testDefinition: 'pt_perf_performance_setup_seasons_Trinity',
            runBilbo: true,
            needGameServer: true,
            config: 'performance',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            uploadJournals: true,
            captureVideo: false,
            isTestWithLooseFiles: true,
            useTempDeploymentIfLooseFiles: false,
            branches: [
                branchConfiguration(CH1_CONTENT_DEV_C1S2B1, pt_perf_performance_setup_seasons_Trinity, 'H 18,1 * * 1-6'),
                ]
            )

        categories += new AutotestCategory(
            name: 'pt_perf_final_setup_seasons_UltSpec',
            testDefinition: 'pt_perf_final_setup_seasons_UltSpec',
            runBilbo: true,
            needGameServer: true,
            config: 'final',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            uploadJournals: true,
            captureVideo: false,
            isTestWithLooseFiles: true,
            useTempDeploymentIfLooseFiles: false,
            branches: [
                branchConfiguration(CH1_CONTENT_DEV_C1S2B1, pt_perf_final_setup_seasons_UltSpec, 'H 5,22 * * 1-6'),
                ]
            )

        categories += new AutotestCategory(
            name: 'pt_perf_performance_setup_seasons_UltSpec',
            testDefinition: 'pt_perf_performance_setup_seasons_UltSpec',
            runBilbo: true,
            needGameServer: true,
            config: 'performance',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            uploadJournals: true,
            captureVideo: false,
            isTestWithLooseFiles: true,
            useTempDeploymentIfLooseFiles: false,
            branches: [
                branchConfiguration(CH1_CONTENT_DEV_C1S2B1, pt_perf_performance_setup_seasons_UltSpec, 'H 17,7 * * 1-6'),
                ]
            )

        categories += new AutotestCategory(
            name: 'pt_perf_final_setup_seasons_XBSS',
            testDefinition: 'pt_perf_final_setup_seasons_XBSS',
            runBilbo: true,
            needGameServer: true,
            config: 'final',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            uploadJournals: true,
            captureVideo: false,
            isTestWithLooseFiles: true,
            useTempDeploymentIfLooseFiles: false,
            branches: [
                branchConfiguration(CH1_CONTENT_DEV_C1S2B1, pt_perf_final_setup_seasons_XBSS, 'H 14,3 * * 1-6'),
                ]
            )

        categories += new AutotestCategory(
            name: 'pt_perf_performance_setup_seasons_XBSS',
            testDefinition: 'pt_perf_performance_setup_seasons_XBSS',
            runBilbo: true,
            needGameServer: true,
            config: 'performance',
            serverConfig: 'final',
            region: Region.WW,
            format: 'files',
            uploadJournals: true,
            captureVideo: false,
            isTestWithLooseFiles: true,
            useTempDeploymentIfLooseFiles: false,
            branches: [
                branchConfiguration(CH1_CONTENT_DEV_C1S2B1, pt_perf_performance_setup_seasons_XBSS, 'H 16,3 * * 1-6'),
                ]
            )

        return categories
    }

    @Override
    List<AutotestCategory> getManualTestCategories() {
        return []
    }

    @Override
    Map<String, List<Platform>> getPlatforms() {
        return [
            (DEFAULT)                   : [new Platform(name: Name.WIN64)],
            (CH1_CODE_DEV)              : BCT_PLATFORMS,
            (CH1_CONTENT_DEV)           : BCT_PLATFORMS,
            (CH1_CONTENT_DEV_SANITIZERS): BCT_PLATFORMS,
            (CH1_TO_TRUNK)              : BCT_PLATFORMS,
            (CH1_2024_TO_CH1)           : BCT_PLATFORMS,
            (CH1_STAGE)                 : BCT_PLATFORMS,
            (CH1_RELEASE)               : BCT_PLATFORMS,
            (TASK2)                     : BCT_PLATFORMS,
            (ECS_SPLINES)               : BCT_PLATFORMS,
            (CH1_CONTENT_DEV_C1S2B1)    : BCT_PLATFORMS,
        ]
    }

    @Override
    boolean shouldLevelsRunInParallel(String branchName) {
        return getSetting(branchName, runLevelsInParallel)
    }

    @Override
    Map getSlackSettings(String branchName) {
        return (Map) getSetting(branchName, collectingSlackSettings)
    }

    @Override
    Map getManualTestCategoriesSetting(String branchName) {
        Map settings = [
            parallel_limit: 1,
            categories    : getManualTestCategories(branchName),
        ]
        return settings
    }

}
