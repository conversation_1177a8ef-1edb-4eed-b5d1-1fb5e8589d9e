package com.ea.project.fb1.branchsettings

import com.ea.lib.LibPerforce
import com.ea.lib.jobsettings.ShiftSettings
import com.ea.lib.model.branchsettings.PipelineDeterminismTestConfiguration
import com.ea.project.fb1.Fb1Battlefieldgame

class DevNaBattlefieldGame {
    // Settings for jobs
    static Class project = Fb1Battlefieldgame
    static Map general_settings = [
        dataset                                : project.dataset,
        elipy_install_call                     : project.elipy_install_call,
        elipy_call                             : "${project.elipy_call} --use-fbenv-core",
        frostbite_licensee                     : 'BattlefieldGame',
        workspace_root                         : project.workspace_root,
        azure_elipy_call                       : "${project.azure_elipy_call} --use-fbenv-core",
        azure_elipy_install_call               : project.azure_elipy_install_call,
        job_label_statebuild                   : 'statebuild_dev-na-dice-next',
        job_label_poolbuild_patchdata          : 'poolbuild_dev-na-dice-next',
        webexport_script_path                  : 'Code\\DICE\\BattlefieldGame\\fbcli\\webexport.py',
        azure_workspace_root                   : project.azure_workspace_root,
        azure_fileshare                        : [
            additional_tools_to_include: ['frostedtests', 'win64'],
            secret_context             : 'fb1_azure_fileshare',
            target_build_share         : 'fb1',
        ],
        gametool_settings                      : [
            gametools: [
                (LibPerforce.GAMETOOL_DRONE): [
                    dry_run: true,
                ],
            ],
        ],
        autotest_remote_settings               : [
            eala: [
                credentials   : 'monkey.commons',
                p4_code_creds : 'fb1-la-p4',
                p4_code_server: 'dicela-p4edge-fb.la.ad.ea.com:2001',
                p4_data_creds : 'fb1-la-p4',
                p4_data_server: 'dicela-p4edge-fb.la.ad.ea.com:2001',
            ],
            dice: [
                p4_code_creds : 'perforce-frostbite02-commons',
                p4_code_server: 'dice-p4buildedge02-fb.dice.ad.ea.com:2001',
                p4_data_creds : 'perforce-frostbite02-commons',
                p4_data_server: 'dice-p4buildedge02-fb.dice.ad.ea.com:2001',
            ]
        ],
        pipeline_determinism_test_configuration: new PipelineDeterminismTestConfiguration(
            cronTrigger: 'H */4 * * *',
            referenceJob: '.data.start',
            perforceCodeCredentials: 'fb1-la-p4',
            perforceCodeServer: 'dicela-p4edge-fb.la.ad.ea.com:2001',
            perforceDataCredentials: 'fb1-la-p4',
            perforceDataServer: 'dicela-p4edge-fb.la.ad.ea.com:2001',
        ),
    ]
    static Map standard_jobs_settings = [
        asset                        : 'DevLevels',
        archive_pipelinelog_success  : true,
        baseline_set                 : false,
        bilbo_store_offsite          : true,
        claim_builds                 : true,
        clean_local                  : false,
        code_data_sync               : true,
        data_reference_job           : 'dev-na-battlefieldgame.DiceNextData.upgrade.data',
        deploy_frostedtests          : true,
        deploy_tests                 : true,
        enable_daily_data_clean      : true,
        enable_lkg_cleaning          : true,
        enable_lkg_p4_counters       : true,
        no_lkg_patch_data            : true,
        import_avalanche_state       : false,
        extra_data_args              : ['--pipeline-args -Pipeline.MaxConcurrencyThrottleMemoryThreshold --pipeline-args 0.8 --pipeline-args -Pipeline.AssetTypeTimeSummary --pipeline-args -1 --pipeline-args -ContentDatabase.EnableHailstormLocalCache --pipeline-args true --pipeline-args -Pipeline.EmitAssetTypeSummaryFile --pipeline-args true --pipeline-args -Pipeline.MaxWarnings --pipeline-args 0 --pipeline-args Pipeline.EnableWarningCountSummary --pipeline-args true '],
        extra_frosty_args            : ['--pipeline-args -Pipeline.MaxConcurrencyThrottleMemoryThreshold --pipeline-args 0.8 --pipeline-args -ContentDatabase.EnableHailstormLocalCache --pipeline-args true '],
        frosty_digital_asset         : 'ShippingLevels',
        icepick_extra_framework_args : '',
        icepick_settings_files       : '',
        import_avalanche_autotest    : false,
        use_super_bundles            : true,
        strip_symbols                : false,
        patch_branch                 : 'dev-na-battlefieldgame-first-patch',
        poolbuild_data               : true,
        statebuild_data              : true,
        poolbuild_frosty             : true,
        statebuild_code_nomaster     : true,
        poolbuild_patchdata          : true,
        poolbuild_patchfrosty        : true,
        poolbuild_label              : 'poolbuild_dev-na-dice-next',
        job_label_statebuild         : 'statebuild_dev-na-dice-next',
        prebuild_info                : [
            config           : 'release',
            dry_run          : false,
            input_param_path : 'Code\\DICE\\BattlefieldGame\\BattlefieldGame-outsource-input-param.xml',
            platforms_sln    : ['tool'],
            platform_prebuild: ['win64-dll'],
            prebuild_path    : '//fblicensee/dice/dev-na-dice-next-outsource-prebuilts',
            skip_platforms   : ['gdk', 'NX', 'ps4', 'ps5', 'xdk'],
            extra_args       : [],
        ],
        produce_build_status         : false,
        trigger_type_data            : 'none',
        trigger_type_patchdata       : 'none',
        server_asset                 : 'Game/Setup/Build/DevMPLevels',
        shift_branch                 : true,
        shift_reference_job          : 'dev-na-battlefieldgame.patchfrosty.start',
        skip_icepick_settings_file   : true,
        slack_channel_code           : [
            channels                  : ['#bf-dev-na-build-notify'],
            skip_for_multiple_failures: true,
        ],
        slack_channel_code_nomaster  : [
            channels                  : ['#bf-dev-na-build-notify'],
            skip_for_multiple_failures: true,
        ],
        slack_channel_data           : [
            channels                  : ['#bf-dev-na-build-notify'],
            skip_for_multiple_failures: true,
        ],
        slack_channel_patchdata      : [
            channels                  : ['#bf-dev-na-build-notify'],
            skip_for_multiple_failures: true,
        ],
        slack_channel_patchfrosty    : [
            channels                  : ['#bf-dev-na-build-notify'],
            skip_for_multiple_failures: true,
        ],
        split_code_data_sync         : true,
        statebuild_code              : false,
        store_baseline_reference_job : 'dev-na-battlefieldgame.patchfrosty.start',
        store_regular_baseline_builds: true,
        store_baseline_platforms     : ['all-but-gen4'],
        timeout_hours_frosty         : 10,
        timeout_hours                : 8,
        timeout_hours_data           : 8,
        timeout_hours_upgrade_data   : 3,
        timeout_hours_patchdata      : 10,
        upgrade_data_job             : true,
        upgrade_data_downstream_jobs : 'dev-na-battlefieldgame.data.start,dev-na-battlefieldgame.patchdata.start',
        use_linuxclient              : true,
        use_zipped_drone_builds      : true,
        webexport_branch             : true,
        new_locations                : [
            earo: [
                elipy_call_new_location: project.elipy_call_earo + ' --use-fbenv-core',
            ],
            RippleEffect: [
                elipy_call_new_location: project.elipy_call_eala + ' --use-fbenv-core',
            ],
        ],
        move_location_parallel       : true,
    ]
    static Map icepick_settings = [
        ignore_icepick_exit_code: false
    ]
    static Map preflight_settings = [:]

    // Matrix definitions for jobs
    static List code_matrix = [
        [name: 'win64game', configs: ['final', 'retail', 'performance']],
        [name: 'ps5', configs: ['final', 'retail', 'release', 'performance']],
        [name: 'xbsx', configs: ['final', 'retail', 'release', 'performance']],
        [name: 'win64server', configs: ['final']],
        [name: 'linux64server', configs: ['final']],
        [name: 'linux64', configs: ['final']],
        [name: 'win64trial', configs: ['final', 'retail']],
        [name: 'tool', configs: [[name: 'release', compile_unit_tests: true, run_unit_tests: true], [name: 'deprecation-test', allow_failure: true, compile_unit_tests: true]]],
    ]
    static List bilbo_move_matrix = [
        [name: 'win64game', configs: ['final', 'performance']],
        [name: 'tool', configs: [[name: 'release']]],
        [name: 'win64server', configs: ['final']],
        [name: 'xbsx', configs: ['final', 'release', 'performance']],
        [name: 'ps5', configs: ['final', 'release', 'performance']],
        [name: 'linux64server', configs: ['final']],
        [name: 'linux64', configs: ['final']],
    ]
    static List code_stressbulkbuild_matrix = [
        [name: 'tool', configs: ['release']],
    ]
    static List code_nomaster_matrix = [
        [name: 'win64game', configs: ['retail']],
        [name: 'ps5', configs: ['final']],
        [name: 'xbsx', configs: ['retail']],
        [name: 'win64server', configs: ['final']],
        [name: 'linux64server', configs: ['final']],
        [name: 'tool', configs: ['release']],
    ]
    static List code_downstream_matrix = [
        [name: '.code.p4counterupdater', args: ['code_changelist', 'code_countername']],
        [name: '.DiceNextData.upgrade.data', args: ['code_changelist']],
    ]
    static List data_matrix = [
        'win64',
        'ps5',
        'xbsx',
        'linux64',
        'server',
        [name: 'validate-frosted', allow_failure: true, deployment_platform: false],
    ]
    static List data_downstream_matrix = [
        [name: '.data.p4counterupdater', args: ['code_changelist', 'data_changelist', 'code_countername', 'data_countername']],
        [name: '.data.p4cleancounterupdater', args: ['code_changelist', 'data_changelist', 'code_countername', 'data_countername']],
        [name: '.code.tool.release.copy-build-to-azure', args: ['code_changelist']],
    ]
    static List patchdata_matrix = [
        'win64',
        'ps5',
        'xbsx',
        'linux64',
    ]
    static List patchdata_downstream_matrix = [
        [name: '.patchfrosty.start', args: []],
    ]
    static List frosty_matrix = []
    static List frosty_downstream_matrix = []
    static List frosty_for_patch_matrix = [
        [name: 'win64', variants: [[format: 'files', config: 'final', region: 'ww', args: ''],
                                   [format: 'digital', config: 'final', region: 'ww', args: '', allow_failure: true],
                                   [format: 'files', config: 'performance', region: 'ww', args: '']
        ]],
        [name: 'ps5', variants: [[format: 'digital', config: 'final', region: 'dev', args: '', allow_failure: true],
                                 [format: 'files', config: 'final', region: 'dev', args: ' --additional-configs release'],
                                 [format: 'files', config: 'performance', region: 'dev', args: '']
        ]],
        [name: 'xbsx', variants: [[format: 'digital', config: 'final', region: 'ww', args: '', allow_failure: true],
                                  [format: 'files', config: 'final', region: 'ww', args: ' --additional-configs release'],
                                  [format: 'files', config: 'performance', region: 'ww', args: '']
        ]],
        [name: 'server', variants: [[format: 'files', config: 'final', region: 'ww', args: '']]],
        [name: 'linuxserver', variants: [[format: 'digital', config: 'final', region: 'ww', args: '', allow_failure: true],
                                         [format: 'files', config: 'final', region: 'ww', args: '']]],
        [name: 'linux64', variants: [[format: 'files', config: 'final', region: 'ww', args: '']]],
    ]
    static List patchfrosty_matrix = []
    static List patchfrosty_downstream_matrix = [
        [name: '.spin.linuxserver.digital.final.ww', args: ['code_changelist', 'data_changelist']],
        [name: '.bilbo.register.local', args: ['code_changelist', 'data_changelist']],
    ]
    static List code_preflight_matrix = []
    static List data_preflight_matrix = []
    static List spin_upload_matrix = [
        [name: 'linuxserver', variants: [[format: 'digital', config: 'final', region: 'ww']]],
    ]
    static List shift_upload_matrix = [
        [shifter_type: ShiftSettings.SHIFTER_TYPE_OFFSITE_DRONE, args: ['code_changelist']],
    ]
    static List shift_downstream_matrix = []
    static List freestyle_job_trigger_matrix = [
        [upstream_job: '.bilbo.register.local', downstream_job: ".shift.${ShiftSettings.SHIFTER_TYPE_OFFSITE_DRONE}.start", args: ['code_changelist']],
    ]
    static List azure_uploads_matrix = [
        [platform: 'tool', content_type: ['code'], config: ['release']]
    ]
    static List pipeline_determinism_test_matrix = [
        [platform: 'win64', job_label: 'pipeline_determinism_win64'],
        [platform: 'win64', job_name: 'debugdb', additional_script_args: '-add_cook_args=\"-forceDebugTarget\"', job_label: 'pipeline_determinism_debugdb'],
        [platform: 'ps5', job_label: 'pipeline_determinism_ps5'],
        [platform: 'xbsx', job_label: 'pipeline_determinism_xbsx'],
    ]
}
