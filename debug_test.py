import unittest
from click.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>
from dice_elipy_scripts.combined_bundles import cli
from unittest.mock import patch, MagicMock

class TestCombinedBundles(unittest.TestCase):
    def setUp(self):
        self.runner = CliRunner()
        self.platform = 'win64'
        self.data_directory = 'Data'
        self.code_branch = 'build-main-dre'
        self.code_changelist = '436418'
        self.data_branch = 'build-main-dre'
        self.data_changelist = '436418'
        self.combine_code_branch = 'build-release'
        self.combine_code_changelist = '436400'
        self.combine_data_branch = 'build-release'
        self.combine_data_changelist = '436400'

    def test_debug(self):
        args = [
            'win64',
            '--data-directory', self.data_directory,
            '--code-branch', self.code_branch,
            '--code-changelist', self.code_changelist,
            '--data-branch', self.data_branch,
            '--data-changelist', self.data_changelist,
            '--combine-code-branch', self.combine_code_branch,
            '--combine-code-changelist', self.combine_code_changelist,
            '--combine-data-branch', self.combine_data_branch,
            '--combine-data-changelist', self.combine_data_changelist,
        ]
        
        result = self.runner.invoke(cli, args)
        print('Exit code:', result.exit_code)
        print('Output:', result.output)
        if result.exception:
            print('Exception:', result.exception)
            import traceback
            traceback.print_exception(type(result.exception), result.exception, result.exception.__traceback__)

if __name__ == "__main__":
    test = TestCombinedBundles()
    test.setUp()
    test.test_debug()
