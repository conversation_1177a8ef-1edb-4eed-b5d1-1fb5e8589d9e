# Combined Bundles Output Folder Change Summary

**Date Completed:** July 14, 2025

**Start Time:** July 14, 2025 14:55
**End Time:** July 14, 2025 15:05
**Total Duration:** 0 hours, 10 minutes

## Summary
- Updated `combined_bundles.py` to construct the output folder path using both main and combined streams/changelists.
- Added comments and docstrings to explain the new logic.
- Added a unit test to verify the folder naming logic.
- Ran <PERSON> and pylint for code quality.
- Ran all relevant unit tests; new logic is verified and robust.
- Updated README.md to document the new folder path logic and parameters.

## Details
- The output folder is now named:
  `combined_bundles_{main_code_branch}_{main_code_changelist}_{main_data_branch}_{main_data_changelist}__{combine_code_branch}_{combine_code_changelist}_{combine_data_branch}_{combine_data_changelist}`
- This ensures unique output folders for builds with different combined streams/changelists.

## Verification
- All tests for folder logic pass.
- No output collisions will occur for builds with the same main stream but different combined streams/changelists.

---
**Change fully implemented and verified.**
