#!/usr/bin/env python
"""Minimal test to verify the fix."""

import sys
import os
import subprocess

def run_quick_test():
    """Run a quick test."""
    os.chdir(r"c:\Users\<USER>\vscode")
    
    cmd = [
        sys.executable, "-m", "pytest", 
        r"pycharm\elipy-scripts\dice_elipy_scripts\tests\test_combined_bundles.py::TestCombinedBundles::test_xbsx_platform_uses_smart_delivery_settings",
        "-v", "--tb=line"
    ]
    
    result = subprocess.run(cmd, capture_output=True, text=True)
    
    print("Return code:", result.returncode)
    print("STDOUT:")
    print(result.stdout)
    if result.stderr:
        print("STDERR:")
        print(result.stderr)
    
    return result.returncode == 0

if __name__ == "__main__":
    success = run_quick_test()
    print(f"\nTest {'PASSED' if success else 'FAILED'}")
    
    # If test still fails, let's check if we can find the exact error
    if not success:
        print("\nTrying to get more details...")
        os.chdir(r"c:\Users\<USER>\vscode")
        cmd = [
            sys.executable, "-m", "pytest", 
            r"pycharm\elipy-scripts\dice_elipy_scripts\tests\test_combined_bundles.py::TestCombinedBundles::test_xbsx_platform_uses_smart_delivery_settings",
            "-vvv", "--tb=short", "--no-header"
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        print("Detailed output:")
        print(result.stdout)
        if result.stderr:
            print(result.stderr)
