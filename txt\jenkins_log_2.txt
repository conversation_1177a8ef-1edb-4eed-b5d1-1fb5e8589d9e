14:53:58 2025-07-14 13:53:58 elipy2 [INFO]: elipy2 version: 17.3a1.dev9065
14:53:58 2025-07-14 13:53:58 elipy2 [INFO]: dice_elipy_scripts version: 10.2a1.dev14124
14:53:58 2025-07-14 13:53:58 elipy2 [INFO]: performaing a --dry-run? True
14:53:58 2025-07-14 13:53:58 elipy2 [INFO]: Skipping path_retention files
14:53:58 2025-07-14 13:53:58 elipy2 [INFO]: Selected categories are: {'frosty\\BattlefieldGame': [{'default': 5}, {'ch1-code-dev': 10}, {'ch1-content-dev': 30}, {'ch1-stage': 20}, {'bflabs': 0}, {'ch1-bflabs-release': 0}, {'ch1-bflabs-qol': 0}], 'frosty\\Frostbite': [{'default': 5}], 'code': [{'default': 40}, {'ch1-content-dev': 100}, {'ch1-marketing-dev': 100}, {'bflabs': 0}, {'ch1-bflabs-release': 0}, {'ch1-bflabs-qol': 0}], 'webexport': [{'default': 50}, {'bflabs': 0}, {'ch1-bflabs-release': 0}, {'ch1-bflabs-qol': 0}], 'expressiondebugdata\\BattlefieldGame': [{'default': 50}, {'bflabs': 0}], 'expressiondebugdata\\Frostbite': [{'default': 50}], 'tnt_local': [{'default': 0}]}
14:53:59 2025-07-14 13:53:59 elipy2 [INFO]: Disk-based discovery found 38 branches: ['2024_1_dev-bf-to-CH1', 'CH1-SP-content-dev', 'CH1-SP-content-dev-disc-build', 'CH1-SP-content-dev-first-patch', 'CH1-SP-stage', 'CH1-bflabs-stage', 'CH1-code-dev', 'CH1-content-dev', 'CH1-content-dev-C1S2B1', 'CH1-content-dev-disc-build', 'CH1-content-dev-first-patch', 'CH1-playtest', 'CH1-playtest-gnt', 'CH1-playtest-maps', 'CH1-playtest-san', 'CH1-playtest-san-s2', 'CH1-playtest-sp', 'CH1-playtest-stage', 'CH1-qol', 'CH1-stage', 'CH1-stage-playtest-sp', 'CH1-to-trunk', 'bf-playtest-gnt', 'bf-playtest-maps', 'bf-playtest-san', 'bf-playtest-sp', 'dev-na-to-trunk', 'dev-na-to-trunk-sub', 'glacier-sku-transition', 'task1', 'task2', 'task3', 'trunk-code-dev', 'trunk-code-dev-skybuild', 'trunk-code-dev-test', 'trunk-content-dev', 'trunk-playtest', 'trunk-to-dev-na']
14:53:59 2025-07-14 13:53:59 elipy2 [INFO]: retaining 5 at file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-to-trunk
14:53:59 2025-07-14 13:53:59 elipy2 [INFO]: retaining 5 at file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-SP-stage
14:53:59 2025-07-14 13:53:59 elipy2 [INFO]: retaining 30 at file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-content-dev
14:53:59 2025-07-14 13:53:59 elipy2 [INFO]: retaining 5 at file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-SP-content-dev
14:53:59 2025-07-14 13:53:59 elipy2 [INFO]: retaining 5 at file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-content-dev-C1S2B1
14:53:59 2025-07-14 13:53:59 elipy2 [INFO]: retaining 5 at file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\trunk-code-dev
14:53:59 2025-07-14 13:53:59 elipy2 [INFO]: retaining 5 at file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\dev-na-to-trunk-sub
14:53:59 2025-07-14 13:53:59 elipy2 [INFO]: retaining 5 at file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-content-dev-disc-build
14:53:59 2025-07-14 13:53:59 elipy2 [INFO]: retaining 5 at file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\task2
14:53:59 2025-07-14 13:53:59 elipy2 [INFO]: retaining 5 at file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-playtest-sp
14:53:59 2025-07-14 13:53:59 elipy2 [INFO]: retaining 5 at file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-playtest-san
14:53:59 2025-07-14 13:53:59 elipy2 [INFO]: retaining 5 at file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-SP-content-dev-first-patch
14:53:59 2025-07-14 13:53:59 elipy2 [INFO]: retaining 5 at file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-SP-content-dev-disc-build
14:53:59 2025-07-14 13:53:59 elipy2 [INFO]: retaining 5 at file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-playtest-maps
14:53:59 2025-07-14 13:53:59 elipy2 [INFO]: retaining 10 at file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-code-dev
14:53:59 2025-07-14 13:53:59 elipy2 [INFO]: retaining 5 at file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\trunk-to-dev-na
14:53:59 2025-07-14 13:53:59 elipy2 [INFO]: retaining 5 at file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-qol
14:53:59 2025-07-14 13:53:59 elipy2 [INFO]: retaining 5 at file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\bf-playtest-gnt
14:53:59 2025-07-14 13:53:59 elipy2 [INFO]: retaining 5 at file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-playtest-stage
14:53:59 2025-07-14 13:53:59 elipy2 [INFO]: retaining 5 at file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\bf-playtest-sp
14:53:59 2025-07-14 13:53:59 elipy2 [INFO]: retaining 5 at file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-stage-playtest-sp
14:53:59 2025-07-14 13:53:59 elipy2 [INFO]: retaining 5 at file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\trunk-code-dev-skybuild
14:53:59 2025-07-14 13:53:59 elipy2 [INFO]: retaining 5 at file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\task3
14:53:59 2025-07-14 13:53:59 elipy2 [INFO]: retaining 5 at file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-bflabs-stage
14:53:59 2025-07-14 13:53:59 elipy2 [INFO]: retaining 5 at file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-content-dev-first-patch
14:53:59 2025-07-14 13:53:59 elipy2 [INFO]: retaining 5 at file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\glacier-sku-transition
14:53:59 2025-07-14 13:53:59 elipy2 [INFO]: retaining 5 at file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\bf-playtest-maps
14:53:59 2025-07-14 13:53:59 elipy2 [INFO]: retaining 5 at file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\2024_1_dev-bf-to-CH1
14:53:59 2025-07-14 13:53:59 elipy2 [INFO]: retaining 5 at file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-playtest-san-s2
14:53:59 2025-07-14 13:53:59 elipy2 [INFO]: retaining 5 at file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-playtest-gnt
14:53:59 2025-07-14 13:53:59 elipy2 [INFO]: retaining 5 at file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\trunk-content-dev
14:53:59 2025-07-14 13:53:59 elipy2 [INFO]: retaining 5 at file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\bf-playtest-san
14:53:59 2025-07-14 13:53:59 elipy2 [INFO]: retaining 5 at file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\trunk-code-dev-test
14:53:59 2025-07-14 13:53:59 elipy2 [INFO]: retaining 5 at file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\task1
14:53:59 2025-07-14 13:53:59 elipy2 [INFO]: retaining 5 at file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\dev-na-to-trunk
14:53:59 2025-07-14 13:53:59 elipy2 [INFO]: retaining 5 at file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-playtest
14:53:59 2025-07-14 13:53:59 elipy2 [INFO]: retaining 20 at file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-stage
14:53:59 2025-07-14 13:53:59 elipy2 [INFO]: retaining 5 at file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\trunk-playtest
14:53:59 2025-07-14 13:53:59 elipy2 [INFO]: Found 5 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-to-trunk
14:53:59 2025-07-14 13:53:59 elipy2 [INFO]: Found 5 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-SP-stage
14:53:59 2025-07-14 13:53:59 elipy2 [INFO]: Found 5 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-content-dev-C1S2B1
14:53:59 2025-07-14 13:53:59 elipy2 [INFO]: Found 5 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-playtest-sp
14:53:59 2025-07-14 13:53:59 elipy2 [INFO]: Found 5 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\task2
14:53:59 2025-07-14 13:53:59 elipy2 [INFO]: Found 5 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-content-dev-disc-build
14:53:59 2025-07-14 13:53:59 elipy2 [INFO]: Found 5 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-playtest-san
14:53:59 2025-07-14 13:53:59 elipy2 [INFO]: Found 5 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\dev-na-to-trunk-sub
14:53:59 2025-07-14 13:53:59 elipy2 [INFO]: Found 6 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-SP-content-dev
14:53:59 2025-07-14 13:53:59 elipy2 [INFO]: Found 6 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\trunk-code-dev
14:53:59 2025-07-14 13:53:59 elipy2 [INFO]: Found 30 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-content-dev
14:55:36 2025-07-14 13:55:36 elipy2 [INFO]: Nothing to delete! builds available 5 <= 5 maximum builds
14:55:36 2025-07-14 13:55:36 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-content-dev-C1S2B1
14:55:37 2025-07-14 13:55:37 elipy2 [INFO]: Found 5 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-SP-content-dev-first-patch
14:55:39 2025-07-14 13:55:39 elipy2 [INFO]: Nothing to delete! builds available 5 <= 5 maximum builds
14:55:39 2025-07-14 13:55:39 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-to-trunk
14:55:41 2025-07-14 13:55:41 elipy2 [INFO]: Nothing to delete! builds available 5 <= 5 maximum builds
14:55:41 2025-07-14 13:55:41 elipy2 [INFO]: Nothing to delete! builds available 5 <= 5 maximum builds
14:55:41 2025-07-14 13:55:41 elipy2 [INFO]: Nothing to delete! builds available 5 <= 5 maximum builds
14:55:41 2025-07-14 13:55:41 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\task2
14:55:41 2025-07-14 13:55:41 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-SP-stage
14:55:41 2025-07-14 13:55:41 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-playtest-sp
14:55:41 2025-07-14 13:55:41 elipy2 [INFO]: Nothing to delete! builds available 5 <= 5 maximum builds
14:55:41 2025-07-14 13:55:41 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-playtest-san
14:55:41 2025-07-14 13:55:41 elipy2 [INFO]: Found 5 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-SP-content-dev-disc-build
14:55:41 2025-07-14 13:55:41 elipy2 [INFO]: Found 4 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-playtest-maps
14:55:42 2025-07-14 13:55:42 elipy2 [INFO]: Found 4 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\trunk-to-dev-na
14:55:42 2025-07-14 13:55:42 elipy2 [INFO]: Nothing to delete! builds available 5 <= 5 maximum builds
14:55:42 2025-07-14 13:55:42 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-content-dev-disc-build
14:55:42 2025-07-14 13:55:42 elipy2 [INFO]: Found 4 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-qol
14:55:42 2025-07-14 13:55:42 elipy2 [INFO]: Found 10 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-code-dev
14:55:43 2025-07-14 13:55:43 elipy2 [INFO]: Found 4 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\bf-playtest-gnt
14:55:46 2025-07-14 13:55:46 elipy2 [INFO]: Nothing to delete! builds available 5 <= 5 maximum builds
14:55:46 2025-07-14 13:55:46 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\dev-na-to-trunk-sub
14:55:47 2025-07-14 13:55:47 elipy2 [INFO]: Found 1 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-playtest-stage
14:56:05 2025-07-14 13:56:05 elipy2 [INFO]: Deletion plan: 0 orphan builds, 1 regular builds (retention)
14:56:05 2025-07-14 13:56:05 elipy2 [INFO]: Deleting 1 builds (1 regular, 0 orphan)
14:56:05 2025-07-14 13:56:05 elipy2 [INFO]: Phase 2: Deleting 1 builds based on retention policy
14:56:05 2025-07-14 13:56:05 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\trunk-code-dev\24401686\trunk-code-dev\24401686
14:56:06 2025-07-14 13:56:06 elipy2 [INFO]: All planned deletions completed successfully
14:56:08 2025-07-14 13:56:08 elipy2 [INFO]: Nothing to delete! builds available 1 <= 5 maximum builds
14:56:08 2025-07-14 13:56:08 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-playtest-stage
14:56:08 2025-07-14 13:56:08 elipy2 [INFO]: Found 4 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\bf-playtest-sp
14:56:09 2025-07-14 13:56:09 elipy2 [INFO]: Found 4 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-stage-playtest-sp
14:56:12 2025-07-14 13:56:12 elipy2 [INFO]: Deletion plan: 0 orphan builds, 1 regular builds (retention)
14:56:12 2025-07-14 13:56:12 elipy2 [INFO]: Deleting 1 builds (1 regular, 0 orphan)
14:56:12 2025-07-14 13:56:12 elipy2 [INFO]: Phase 2: Deleting 1 builds based on retention policy
14:56:12 2025-07-14 13:56:12 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-SP-content-dev\24441123\CH1-SP-content-dev\24441123
14:56:12 2025-07-14 13:56:12 elipy2 [INFO]: All planned deletions completed successfully
14:56:13 2025-07-14 13:56:13 elipy2 [INFO]: Found 1 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\trunk-code-dev-skybuild
14:56:35 2025-07-14 13:56:35 elipy2 [INFO]: Nothing to delete! builds available 1 <= 5 maximum builds
14:56:35 2025-07-14 13:56:35 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\trunk-code-dev-skybuild
14:56:36 2025-07-14 13:56:36 elipy2 [INFO]: Found 3 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\task3
14:57:03 2025-07-14 13:57:03 elipy2 [INFO]: Nothing to delete! builds available 4 <= 5 maximum builds
14:57:03 2025-07-14 13:57:03 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-playtest-maps
14:57:04 2025-07-14 13:57:04 elipy2 [INFO]: Found 5 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-bflabs-stage
14:57:05 2025-07-14 13:57:05 elipy2 [INFO]: Nothing to delete! builds available 4 <= 5 maximum builds
14:57:05 2025-07-14 13:57:05 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\trunk-to-dev-na
14:57:07 2025-07-14 13:57:07 elipy2 [INFO]: Nothing to delete! builds available 4 <= 5 maximum builds
14:57:07 2025-07-14 13:57:07 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\bf-playtest-gnt
14:57:07 2025-07-14 13:57:07 elipy2 [INFO]: Found 5 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-content-dev-first-patch
14:57:07 2025-07-14 13:57:07 elipy2 [INFO]: Nothing to delete! builds available 4 <= 5 maximum builds
14:57:07 2025-07-14 13:57:07 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-qol
14:57:08 2025-07-14 13:57:08 elipy2 [INFO]: Found 4 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\glacier-sku-transition
14:57:09 2025-07-14 13:57:09 elipy2 [INFO]: Found 4 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\bf-playtest-maps
14:57:21 2025-07-14 13:57:21 elipy2 [INFO]: Nothing to delete! builds available 5 <= 5 maximum builds
14:57:21 2025-07-14 13:57:21 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-SP-content-dev-first-patch
14:57:22 2025-07-14 13:57:22 elipy2 [INFO]: Found 5 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\2024_1_dev-bf-to-CH1
14:57:24 2025-07-14 13:57:24 elipy2 [INFO]: Nothing to delete! builds available 5 <= 5 maximum builds
14:57:24 2025-07-14 13:57:24 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-SP-content-dev-disc-build
14:57:26 2025-07-14 13:57:26 elipy2 [INFO]: Found 5 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-playtest-san-s2
14:57:30 2025-07-14 13:57:30 elipy2 [INFO]: Nothing to delete! builds available 4 <= 5 maximum builds
14:57:30 2025-07-14 13:57:30 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-stage-playtest-sp
14:57:31 2025-07-14 13:57:31 elipy2 [INFO]: Found 5 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-playtest-gnt
14:57:32 2025-07-14 13:57:32 elipy2 [INFO]: Nothing to delete! builds available 4 <= 5 maximum builds
14:57:32 2025-07-14 13:57:32 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\bf-playtest-sp
14:57:34 2025-07-14 13:57:34 elipy2 [INFO]: Found 5 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\trunk-content-dev
14:57:42 2025-07-14 13:57:42 elipy2 [INFO]: Nothing to delete! builds available 3 <= 5 maximum builds
14:57:42 2025-07-14 13:57:42 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\task3
14:57:43 2025-07-14 13:57:43 elipy2 [INFO]: Found 4 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\bf-playtest-san
14:58:41 2025-07-14 13:58:41 elipy2 [INFO]: Nothing to delete! builds available 4 <= 5 maximum builds
14:58:41 2025-07-14 13:58:41 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\glacier-sku-transition
14:58:43 2025-07-14 13:58:43 elipy2 [INFO]: Found 4 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\trunk-code-dev-test
14:58:47 2025-07-14 13:58:47 elipy2 [INFO]: Nothing to delete! builds available 4 <= 5 maximum builds
14:58:47 2025-07-14 13:58:47 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\bf-playtest-maps
14:58:48 2025-07-14 13:58:48 elipy2 [INFO]: Found 4 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\task1
14:58:58 2025-07-14 13:58:58 elipy2 [INFO]: Nothing to delete! builds available 5 <= 5 maximum builds
14:58:58 2025-07-14 13:58:58 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-bflabs-stage
14:58:58 2025-07-14 13:58:58 elipy2 [INFO]: Nothing to delete! builds available 5 <= 5 maximum builds
14:58:58 2025-07-14 13:58:58 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-content-dev-first-patch
14:58:59 2025-07-14 13:58:59 elipy2 [INFO]: Found 1 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-playtest
14:59:00 2025-07-14 13:59:00 elipy2 [INFO]: Found 5 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\dev-na-to-trunk
14:59:14 2025-07-14 13:59:14 elipy2 [INFO]: Nothing to delete! builds available 5 <= 5 maximum builds
14:59:14 2025-07-14 13:59:14 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\2024_1_dev-bf-to-CH1
14:59:16 2025-07-14 13:59:16 elipy2 [INFO]: Nothing to delete! builds available 4 <= 5 maximum builds
14:59:16 2025-07-14 13:59:16 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\bf-playtest-san
14:59:16 2025-07-14 13:59:16 elipy2 [INFO]: Nothing to delete! builds available 10 <= 10 maximum builds
14:59:16 2025-07-14 13:59:16 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-code-dev
14:59:17 2025-07-14 13:59:17 elipy2 [INFO]: Found 4 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\trunk-playtest
14:59:17 2025-07-14 13:59:17 elipy2 [INFO]: Found 20 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-stage
14:59:20 2025-07-14 13:59:20 elipy2 [INFO]: Nothing to delete! builds available 5 <= 5 maximum builds
14:59:20 2025-07-14 13:59:20 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-playtest-san-s2
14:59:21 2025-07-14 13:59:21 elipy2 [INFO]: Nothing to delete! builds available 1 <= 5 maximum builds
14:59:21 2025-07-14 13:59:21 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-playtest
14:59:24 2025-07-14 13:59:24 elipy2 [INFO]: Nothing to delete! builds available 5 <= 5 maximum builds
14:59:24 2025-07-14 13:59:24 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-playtest-gnt
14:59:26 2025-07-14 13:59:26 elipy2 [INFO]: Nothing to delete! builds available 5 <= 5 maximum builds
14:59:26 2025-07-14 13:59:26 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\trunk-content-dev
14:59:43 2025-07-14 13:59:43 elipy2 [INFO]: Nothing to delete! builds available 4 <= 5 maximum builds
14:59:43 2025-07-14 13:59:43 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\trunk-code-dev-test
14:59:49 2025-07-14 13:59:49 elipy2 [INFO]: Nothing to delete! builds available 4 <= 5 maximum builds
14:59:49 2025-07-14 13:59:49 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\task1
14:59:58 2025-07-14 13:59:58 elipy2 [INFO]: Nothing to delete! builds available 5 <= 5 maximum builds
14:59:58 2025-07-14 13:59:58 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\dev-na-to-trunk
14:59:58 2025-07-14 13:59:58 elipy2 [INFO]: Nothing to delete! builds available 4 <= 5 maximum builds
14:59:58 2025-07-14 13:59:58 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\trunk-playtest
15:01:00 2025-07-14 14:01:00 elipy2 [INFO]: Nothing to delete! builds available 30 <= 30 maximum builds
15:01:00 2025-07-14 14:01:00 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-content-dev
15:01:15 2025-07-14 14:01:15 elipy2 [INFO]: Nothing to delete! builds available 20 <= 20 maximum builds
15:01:15 2025-07-14 14:01:15 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-stage
15:01:16 2025-07-14 14:01:16 elipy2 [INFO]: Disk-based discovery found 1 branches: ['trunk-to-dev-na']
15:01:16 2025-07-14 14:01:16 elipy2 [INFO]: retaining 5 at file:\\filer.dice.ad.ea.com\builds\Battlefield\frosty\Frostbite\trunk-to-dev-na
15:01:16 2025-07-14 14:01:16 elipy2 [INFO]: Found 4 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\Frostbite\trunk-to-dev-na
15:01:32 2025-07-14 14:01:32 elipy2 [INFO]: Nothing to delete! builds available 4 <= 5 maximum builds
15:01:32 2025-07-14 14:01:32 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\frosty\Frostbite\trunk-to-dev-na
15:01:32 2025-07-14 14:01:32 elipy2 [INFO]: Disk-based discovery found 46 branches: ['2024_1_dev-bf-to-CH1', 'CH1-SP-release', 'CH1-event', 'CH1-release', 'ch1-bflabs-qol', 'ch1-bflabs-stage', 'ch1-code-dev', 'ch1-code-dev-asan', 'ch1-code-dev-clean', 'ch1-code-dev-sanitizers', 'ch1-content-dev', 'ch1-content-dev-cache', 'ch1-content-dev-clean', 'ch1-content-dev-metrics', 'ch1-content-dev-sanitizers', 'ch1-marketing-dev', 'ch1-marketing-dev-cache', 'ch1-media-team', 'ch1-playtest', 'ch1-playtest-stage', 'ch1-qol', 'ch1-sp-content-dev', 'ch1-sp-stage', 'ch1-stage', 'ch1-to-trunk', 'dev-na-to-trunk', 'dev-na-to-trunk-sub', 'ecs-splines', 'glacier-sku-transition', 'media-team', 'task1', 'task2', 'task3', 'task4', 'trunk-code-dev', 'trunk-code-dev-asan', 'trunk-code-dev-clean', 'trunk-code-dev-sanitizers', 'trunk-code-dev-skybuild', 'trunk-code-dev-test', 'trunk-content-dev', 'trunk-content-dev-cache', 'trunk-content-dev-metrics', 'trunk-content-dev-skybuild', 'trunk-playtest', 'trunk-to-dev-na']
15:01:32 2025-07-14 14:01:32 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-playtest
15:01:32 2025-07-14 14:01:32 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\CH1-release
15:01:32 2025-07-14 14:01:32 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-sp-content-dev
15:01:32 2025-07-14 14:01:32 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ecs-splines
15:01:32 2025-07-14 14:01:32 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-media-team
15:01:32 2025-07-14 14:01:32 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev
15:01:32 2025-07-14 14:01:32 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-asan
15:01:32 2025-07-14 14:01:32 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\media-team
15:01:32 2025-07-14 14:01:32 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\dev-na-to-trunk-sub
15:01:32 2025-07-14 14:01:32 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-bflabs-stage
15:01:32 2025-07-14 14:01:32 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev
15:01:32 2025-07-14 14:01:32 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\task2
15:01:32 2025-07-14 14:01:32 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-clean
15:01:32 2025-07-14 14:01:32 elipy2 [INFO]: retaining 0 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-bflabs-qol
15:01:32 2025-07-14 14:01:32 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-to-dev-na
15:01:32 2025-07-14 14:01:32 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-clean
15:01:32 2025-07-14 14:01:32 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-qol
15:01:32 2025-07-14 14:01:32 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-metrics
15:01:32 2025-07-14 14:01:32 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-skybuild
15:01:32 2025-07-14 14:01:32 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-asan
15:01:32 2025-07-14 14:01:32 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-stage
15:01:32 2025-07-14 14:01:32 elipy2 [INFO]: retaining 100 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev
15:01:32 2025-07-14 14:01:32 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-content-dev-metrics
15:01:32 2025-07-14 14:01:32 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-sanitizers
15:01:32 2025-07-14 14:01:32 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\task3
15:01:32 2025-07-14 14:01:32 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-content-dev-cache
15:01:32 2025-07-14 14:01:32 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-marketing-dev-cache
15:01:32 2025-07-14 14:01:32 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\glacier-sku-transition
15:01:32 2025-07-14 14:01:32 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-content-dev-skybuild
15:01:32 2025-07-14 14:01:32 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-playtest-stage
15:01:32 2025-07-14 14:01:32 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\2024_1_dev-bf-to-CH1
15:01:32 2025-07-14 14:01:32 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-clean
15:01:32 2025-07-14 14:01:32 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-content-dev
15:01:32 2025-07-14 14:01:32 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-sp-stage
15:01:32 2025-07-14 14:01:32 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-test
15:01:32 2025-07-14 14:01:32 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-cache
15:01:32 2025-07-14 14:01:32 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\CH1-SP-release
15:01:32 2025-07-14 14:01:32 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\task1
15:01:32 2025-07-14 14:01:32 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers
15:01:32 2025-07-14 14:01:32 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\CH1-event
15:01:32 2025-07-14 14:01:32 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-to-trunk
15:01:32 2025-07-14 14:01:32 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\dev-na-to-trunk
15:01:32 2025-07-14 14:01:32 elipy2 [INFO]: retaining 100 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-marketing-dev
15:01:32 2025-07-14 14:01:32 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers
15:01:32 2025-07-14 14:01:32 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\task4
15:01:32 2025-07-14 14:01:32 elipy2 [INFO]: retaining 40 at file:\\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-playtest
15:01:32 2025-07-14 14:01:32 elipy2 [INFO]: Found 2 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-playtest
15:01:32 2025-07-14 14:01:32 elipy2 [INFO]: Found 2 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-skybuild
15:01:32 2025-07-14 14:01:32 elipy2 [INFO]: Found 14 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-asan
15:01:32 2025-07-14 14:01:32 elipy2 [INFO]: Found 30 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-qol
15:01:32 2025-07-14 14:01:32 elipy2 [INFO]: Found 40 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-media-team
15:01:32 2025-07-14 14:01:32 elipy2 [INFO]: Found 40 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-clean
15:01:32 2025-07-14 14:01:32 elipy2 [INFO]: Found 42 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\dev-na-to-trunk-sub
15:01:32 2025-07-14 14:01:32 elipy2 [INFO]: Found 42 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-to-dev-na
15:01:32 2025-07-14 14:01:32 elipy2 [INFO]: Found 42 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev
15:01:32 2025-07-14 14:01:32 elipy2 [INFO]: Found 44 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-sp-content-dev
15:01:32 2025-07-14 14:01:32 elipy2 [INFO]: Found 43 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-stage
15:02:15 2025-07-14 14:02:15 elipy2 [INFO]: Nothing to delete! builds available 2 <= 40 maximum builds
15:02:15 2025-07-14 14:02:15 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-skybuild
15:02:16 2025-07-14 14:02:16 elipy2 [INFO]: Nothing to delete! builds available 2 <= 40 maximum builds
15:02:16 2025-07-14 14:02:16 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-playtest
15:02:18 2025-07-14 14:02:18 elipy2 [INFO]: Found 14 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-asan
15:02:18 2025-07-14 14:02:18 elipy2 [INFO]: Found 42 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\CH1-release
15:06:23 2025-07-14 14:06:23 elipy2 [INFO]: Nothing to delete! builds available 14 <= 40 maximum builds
15:06:23 2025-07-14 14:06:23 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-asan
15:06:24 2025-07-14 14:06:24 elipy2 [INFO]: Found 40 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\media-team
15:07:16 2025-07-14 14:07:16 elipy2 [INFO]: Nothing to delete! builds available 14 <= 40 maximum builds
15:07:16 2025-07-14 14:07:16 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-asan
15:07:17 2025-07-14 14:07:17 elipy2 [INFO]: Found 40 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-content-dev-metrics
15:12:41 2025-07-14 14:12:41 elipy2 [INFO]: Nothing to delete! builds available 30 <= 40 maximum builds
15:12:41 2025-07-14 14:12:41 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-qol
15:12:42 2025-07-14 14:12:42 elipy2 [INFO]: Found 42 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-metrics
15:16:05 2025-07-14 14:16:05 elipy2 [INFO]: Nothing to delete! builds available 40 <= 40 maximum builds
15:16:05 2025-07-14 14:16:05 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-clean
15:16:10 2025-07-14 14:16:10 elipy2 [INFO]: Nothing to delete! builds available 40 <= 40 maximum builds
15:16:10 2025-07-14 14:16:10 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-media-team
15:16:11 2025-07-14 14:16:11 elipy2 [INFO]: Found 43 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev
15:16:47 2025-07-14 14:16:47 elipy2 [INFO]: Preserving build \\filer.dice.ad.ea.com\builds\Battlefield\code\dev-na-to-trunk-sub\21796588 (promoted build)
15:16:56 2025-07-14 14:16:56 elipy2 [INFO]: Preserving build \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-to-dev-na\23180978 (drone build)
15:16:59 2025-07-14 14:16:59 elipy2 [INFO]: Preserving build \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev\24120608 (drone build)
15:17:01 2025-07-14 14:17:01 elipy2 [INFO]: Preserving build \\filer.dice.ad.ea.com\builds\Battlefield\code\dev-na-to-trunk-sub\21947691 (drone build)
15:17:01 2025-07-14 14:17:01 elipy2 [INFO]: Deletion plan: 0 orphan builds, 0 regular builds (retention)
15:17:01 2025-07-14 14:17:01 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\dev-na-to-trunk-sub
15:17:03 2025-07-14 14:17:03 elipy2 [INFO]: Found 40 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-bflabs-stage
15:17:08 2025-07-14 14:17:08 elipy2 [INFO]: Preserving build \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-to-dev-na\23318395 (drone build)
15:17:08 2025-07-14 14:17:08 elipy2 [INFO]: Deletion plan: 0 orphan builds, 0 regular builds (retention)
15:17:08 2025-07-14 14:17:08 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-to-dev-na
15:17:09 2025-07-14 14:17:09 elipy2 [INFO]: Preserving build \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-stage\23681229 (promoted build)
15:17:10 2025-07-14 14:17:10 elipy2 [INFO]: Found 40 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-clean
15:17:11 2025-07-14 14:17:11 elipy2 [INFO]: Preserving build \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev\24121042 (drone build)
15:17:11 2025-07-14 14:17:11 elipy2 [INFO]: Deletion plan: 0 orphan builds, 0 regular builds (retention)
15:17:11 2025-07-14 14:17:11 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev
15:17:13 2025-07-14 14:17:13 elipy2 [INFO]: Found 42 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\task2
15:17:21 2025-07-14 14:17:21 elipy2 [INFO]: Preserving build \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-stage\24231509 (drone build)
15:17:27 2025-07-14 14:17:27 elipy2 [INFO]: Preserving build \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-sp-content-dev\24145276 (promoted build)
15:17:32 2025-07-14 14:17:32 elipy2 [INFO]: Preserving build \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-stage\24234018 (drone build)
15:17:32 2025-07-14 14:17:32 elipy2 [INFO]: Deletion plan: 0 orphan builds, 0 regular builds (retention)
15:17:32 2025-07-14 14:17:32 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-stage
15:17:34 2025-07-14 14:17:34 elipy2 [INFO]: Found 104 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev
15:17:36 2025-07-14 14:17:36 elipy2 [INFO]: Preserving build \\filer.dice.ad.ea.com\builds\Battlefield\code\CH1-release\23882059 (drone build)
15:17:38 2025-07-14 14:17:38 elipy2 [INFO]: Preserving build \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-sp-content-dev\24253749 (drone build)
15:17:50 2025-07-14 14:17:50 elipy2 [INFO]: Preserving build \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-sp-content-dev\24254058 (drone build)
15:17:50 2025-07-14 14:17:50 elipy2 [INFO]: Preserving build \\filer.dice.ad.ea.com\builds\Battlefield\code\CH1-release\23883264 (drone build)
15:17:50 2025-07-14 14:17:50 elipy2 [INFO]: Deletion plan: 0 orphan builds, 0 regular builds (retention)
15:17:50 2025-07-14 14:17:50 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\CH1-release
15:17:52 2025-07-14 14:17:52 elipy2 [INFO]: Found 40 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\task3
15:18:02 2025-07-14 14:18:02 elipy2 [INFO]: Deletion plan: 0 orphan builds, 1 regular builds (retention)
15:18:02 2025-07-14 14:18:02 elipy2 [INFO]: Deleting 1 builds (1 regular, 0 orphan)
15:18:02 2025-07-14 14:18:02 elipy2 [INFO]: Phase 2: Deleting 1 builds based on retention policy
15:18:02 2025-07-14 14:18:02 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-sp-content-dev\24401249
15:18:02 2025-07-14 14:18:02 elipy2 [INFO]: All planned deletions completed successfully
15:18:05 2025-07-14 14:18:05 elipy2 [INFO]: Found 42 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\ecs-splines
15:20:23 2025-07-14 14:20:23 elipy2 [INFO]: Nothing to delete! builds available 40 <= 40 maximum builds
15:20:23 2025-07-14 14:20:23 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\media-team
15:20:25 2025-07-14 14:20:25 elipy2 [INFO]: Found 24 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-marketing-dev-cache
15:20:59 2025-07-14 14:20:59 elipy2 [INFO]: Nothing to delete! builds available 40 <= 40 maximum builds
15:20:59 2025-07-14 14:20:59 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-content-dev-metrics
15:21:00 2025-07-14 14:21:00 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-sanitizers\24029263
15:21:00 2025-07-14 14:21:00 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-sanitizers\24031339
15:21:00 2025-07-14 14:21:00 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-sanitizers\24044352
15:21:00 2025-07-14 14:21:00 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-sanitizers\24056933
15:21:00 2025-07-14 14:21:00 elipy2 [INFO]: Found sanitizer subdirectory: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-sanitizers\asan
15:21:00 2025-07-14 14:21:00 elipy2 [INFO]: Found sanitizer subdirectory: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-sanitizers\ubsan
15:21:00 2025-07-14 14:21:00 elipy2 [INFO]: Found 4 direct CL directories in main sanitizer folder: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-sanitizers
15:21:00 2025-07-14 14:21:00 elipy2 [INFO]: Found 18 CL directories in sanitizer subdirectory asan: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-sanitizers\asan
15:21:00 2025-07-14 14:21:00 elipy2 [INFO]: Found 18 CL directories in sanitizer subdirectory ubsan: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-sanitizers\ubsan
15:21:00 2025-07-14 14:21:00 elipy2 [INFO]: Sanitizer scan complete for \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-sanitizers: 40 total builds found (4 direct, 36 in subdirs)
15:21:00 2025-07-14 14:21:00 elipy2 [INFO]: Found 40 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-sanitizers
15:26:15 2025-07-14 14:26:15 elipy2 [INFO]: Preserving build \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-metrics\23809175 (drone build)
15:26:28 2025-07-14 14:26:28 elipy2 [INFO]: Preserving build \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-metrics\23819207 (drone build)
15:26:28 2025-07-14 14:26:28 elipy2 [INFO]: Deletion plan: 0 orphan builds, 0 regular builds (retention)
15:26:28 2025-07-14 14:26:28 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-metrics
15:26:28 2025-07-14 14:26:28 elipy2 [INFO]: Found 1 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-content-dev-skybuild
15:26:49 2025-07-14 14:26:49 elipy2 [INFO]: Nothing to delete! builds available 1 <= 40 maximum builds
15:26:49 2025-07-14 14:26:49 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-content-dev-skybuild
15:26:49 2025-07-14 14:26:49 elipy2 [INFO]: Found 1 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-playtest-stage
15:27:09 2025-07-14 14:27:09 elipy2 [INFO]: Nothing to delete! builds available 1 <= 40 maximum builds
15:27:09 2025-07-14 14:27:09 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-playtest-stage
15:27:11 2025-07-14 14:27:11 elipy2 [INFO]: Found 41 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\2024_1_dev-bf-to-CH1
15:27:47 2025-07-14 14:27:47 elipy2 [INFO]: Nothing to delete! builds available 24 <= 40 maximum builds
15:27:47 2025-07-14 14:27:47 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-marketing-dev-cache
15:27:49 2025-07-14 14:27:49 elipy2 [INFO]: Found 39 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\glacier-sku-transition
15:29:30 2025-07-14 14:29:30 elipy2 [INFO]: Nothing to delete! builds available 40 <= 40 maximum builds
15:29:30 2025-07-14 14:29:30 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-bflabs-stage
15:29:31 2025-07-14 14:29:31 elipy2 [INFO]: Found 45 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-content-dev
15:29:34 2025-07-14 14:29:34 elipy2 [INFO]: Nothing to delete! builds available 40 <= 40 maximum builds
15:29:34 2025-07-14 14:29:34 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-clean
15:29:36 2025-07-14 14:29:36 elipy2 [INFO]: Found 40 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-test
15:29:36 2025-07-14 14:29:36 elipy2 [INFO]: Found 0 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-bflabs-qol
15:29:36 2025-07-14 14:29:36 elipy2 [INFO]: Nothing to delete! builds available 0 <= 0 maximum builds
15:29:36 2025-07-14 14:29:36 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-bflabs-qol
15:29:37 2025-07-14 14:29:37 elipy2 [INFO]: Found 4 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\CH1-SP-release
15:30:02 2025-07-14 14:30:02 elipy2 [INFO]: Preserving build \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev\23470552 (promoted build)
15:30:16 2025-07-14 14:30:16 elipy2 [INFO]: Preserving build \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev\23498679 (drone build)
15:30:27 2025-07-14 14:30:27 elipy2 [INFO]: Nothing to delete! builds available 40 <= 40 maximum builds
15:30:27 2025-07-14 14:30:27 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\task3
15:30:28 2025-07-14 14:30:28 elipy2 [INFO]: Found 39 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-content-dev-cache
15:30:29 2025-07-14 14:30:29 elipy2 [INFO]: Preserving build \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev\23508806 (drone build)
15:30:29 2025-07-14 14:30:29 elipy2 [INFO]: Deletion plan: 0 orphan builds, 0 regular builds (retention)
15:30:29 2025-07-14 14:30:29 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev
15:30:31 2025-07-14 14:30:31 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24192974
15:30:31 2025-07-14 14:30:31 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24195184
15:30:31 2025-07-14 14:30:31 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24281756
15:30:31 2025-07-14 14:30:31 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24283864
15:30:31 2025-07-14 14:30:31 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24286115
15:30:31 2025-07-14 14:30:31 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24288099
15:30:31 2025-07-14 14:30:31 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24289589
15:30:31 2025-07-14 14:30:31 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24290824
15:30:31 2025-07-14 14:30:31 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24292376
15:30:31 2025-07-14 14:30:31 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24293235
15:30:31 2025-07-14 14:30:31 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24294756
15:30:31 2025-07-14 14:30:31 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24295632
15:30:31 2025-07-14 14:30:31 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24299030
15:30:31 2025-07-14 14:30:31 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24301444
15:30:31 2025-07-14 14:30:31 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24303473
15:30:31 2025-07-14 14:30:31 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24305135
15:30:31 2025-07-14 14:30:31 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24306466
15:30:31 2025-07-14 14:30:31 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24307772
15:30:31 2025-07-14 14:30:31 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24309398
15:30:31 2025-07-14 14:30:31 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24311642
15:30:31 2025-07-14 14:30:31 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24313090
15:30:31 2025-07-14 14:30:31 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24315995
15:30:31 2025-07-14 14:30:31 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24318068
15:30:31 2025-07-14 14:30:31 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24320228
15:30:31 2025-07-14 14:30:31 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24321590
15:30:31 2025-07-14 14:30:31 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24323022
15:30:31 2025-07-14 14:30:31 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24325788
15:30:31 2025-07-14 14:30:31 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24327959
15:30:31 2025-07-14 14:30:31 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24330117
15:30:31 2025-07-14 14:30:31 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24335644
15:30:31 2025-07-14 14:30:31 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24336637
15:30:31 2025-07-14 14:30:31 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24349614
15:30:31 2025-07-14 14:30:31 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24353219
15:30:31 2025-07-14 14:30:31 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24355138
15:30:31 2025-07-14 14:30:31 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24357207
15:30:31 2025-07-14 14:30:31 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24359703
15:30:31 2025-07-14 14:30:31 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24364434
15:30:31 2025-07-14 14:30:31 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24366345
15:30:31 2025-07-14 14:30:31 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24367988
15:30:31 2025-07-14 14:30:31 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24369345
15:30:32 2025-07-14 14:30:32 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24371701
15:30:32 2025-07-14 14:30:32 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24373616
15:30:32 2025-07-14 14:30:32 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24376287
15:30:32 2025-07-14 14:30:32 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24377962
15:30:32 2025-07-14 14:30:32 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24379875
15:30:32 2025-07-14 14:30:32 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24381806
15:30:32 2025-07-14 14:30:32 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24383152
15:30:32 2025-07-14 14:30:32 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24385284
15:30:32 2025-07-14 14:30:32 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24387321
15:30:32 2025-07-14 14:30:32 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24395224
15:30:32 2025-07-14 14:30:32 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24396717
15:30:32 2025-07-14 14:30:32 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24397983
15:30:32 2025-07-14 14:30:32 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24399318
15:30:32 2025-07-14 14:30:32 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24401099
15:30:32 2025-07-14 14:30:32 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24403673
15:30:32 2025-07-14 14:30:32 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24405962
15:30:32 2025-07-14 14:30:32 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24407804
15:30:33 2025-07-14 14:30:33 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24409551
15:30:33 2025-07-14 14:30:33 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24411070
15:30:33 2025-07-14 14:30:33 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24412155
15:30:33 2025-07-14 14:30:33 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24413544
15:30:33 2025-07-14 14:30:33 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24415848
15:30:33 2025-07-14 14:30:33 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24417994
15:30:33 2025-07-14 14:30:33 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24419848
15:30:33 2025-07-14 14:30:33 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24422181
15:30:33 2025-07-14 14:30:33 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24424159
15:30:33 2025-07-14 14:30:33 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24425881
15:30:33 2025-07-14 14:30:33 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24426779
15:30:33 2025-07-14 14:30:33 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24427461
15:30:33 2025-07-14 14:30:33 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24428271
15:30:33 2025-07-14 14:30:33 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24428979
15:30:33 2025-07-14 14:30:33 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24429842
15:30:33 2025-07-14 14:30:33 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24430549
15:30:33 2025-07-14 14:30:33 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24431319
15:30:33 2025-07-14 14:30:33 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24431434
15:30:33 2025-07-14 14:30:33 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24432751
15:30:33 2025-07-14 14:30:33 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24434311
15:30:33 2025-07-14 14:30:33 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24434613
15:30:33 2025-07-14 14:30:33 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24435805
15:30:33 2025-07-14 14:30:33 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24436793
15:30:33 2025-07-14 14:30:33 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24438876
15:30:33 2025-07-14 14:30:33 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24444498
15:30:33 2025-07-14 14:30:33 elipy2 [INFO]: Found sanitizer subdirectory: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\asan
15:30:33 2025-07-14 14:30:33 elipy2 [INFO]: Found sanitizer subdirectory: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan
15:30:33 2025-07-14 14:30:33 elipy2 [INFO]: Found 82 direct CL directories in main sanitizer folder: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers
15:30:33 2025-07-14 14:30:33 elipy2 [INFO]: Found 40 CL directories in sanitizer subdirectory asan: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\asan
15:30:34 2025-07-14 14:30:34 elipy2 [INFO]: Found 40 CL directories in sanitizer subdirectory ubsan: \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan
15:30:34 2025-07-14 14:30:34 elipy2 [INFO]: Sanitizer scan complete for \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers: 162 total builds found (82 direct, 80 in subdirs)
15:30:34 2025-07-14 14:30:34 elipy2 [INFO]: Found 162 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers
15:30:44 2025-07-14 14:30:44 elipy2 [INFO]: Preserving build \\filer.dice.ad.ea.com\builds\Battlefield\code\task2\23138880 (drone build)
15:30:55 2025-07-14 14:30:55 elipy2 [INFO]: Preserving build \\filer.dice.ad.ea.com\builds\Battlefield\code\task2\23141586 (drone build)
15:30:55 2025-07-14 14:30:55 elipy2 [INFO]: Deletion plan: 0 orphan builds, 0 regular builds (retention)
15:30:55 2025-07-14 14:30:55 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\task2
15:30:57 2025-07-14 14:30:57 elipy2 [INFO]: Found 41 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-to-trunk
15:31:06 2025-07-14 14:31:06 elipy2 [INFO]: Nothing to delete! builds available 4 <= 40 maximum builds
15:31:06 2025-07-14 14:31:06 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\CH1-SP-release
15:31:09 2025-07-14 14:31:09 elipy2 [INFO]: Found 40 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\task1
15:32:01 2025-07-14 14:32:01 elipy2 [INFO]: Preserving build \\filer.dice.ad.ea.com\builds\Battlefield\code\ecs-splines\23836410 (drone build)
15:32:14 2025-07-14 14:32:14 elipy2 [INFO]: Preserving build \\filer.dice.ad.ea.com\builds\Battlefield\code\ecs-splines\23853895 (drone build)
15:32:14 2025-07-14 14:32:14 elipy2 [INFO]: Deletion plan: 0 orphan builds, 0 regular builds (retention)
15:32:14 2025-07-14 14:32:14 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\ecs-splines
15:32:16 2025-07-14 14:32:16 elipy2 [INFO]: Found 105 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-marketing-dev
15:33:56 2025-07-14 14:33:56 elipy2 [INFO]: Nothing to delete! builds available 40 <= 40 maximum builds
15:33:56 2025-07-14 14:33:56 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-sanitizers
15:33:58 2025-07-14 14:33:58 elipy2 [INFO]: Found 12 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\task4
15:38:08 2025-07-14 14:38:08 elipy2 [INFO]: Nothing to delete! builds available 12 <= 40 maximum builds
15:38:08 2025-07-14 14:38:08 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\task4
15:38:09 2025-07-14 14:38:09 elipy2 [INFO]: Found 7 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-playtest
15:40:34 2025-07-14 14:40:34 elipy2 [INFO]: Nothing to delete! builds available 7 <= 40 maximum builds
15:40:34 2025-07-14 14:40:34 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-playtest
15:41:26 2025-07-14 14:41:26 elipy2 [INFO]: Preserving build \\filer.dice.ad.ea.com\builds\Battlefield\code\2024_1_dev-bf-to-CH1\22736920 (drone build)
15:41:26 2025-07-14 14:41:26 elipy2 [INFO]: Deletion plan: 0 orphan builds, 0 regular builds (retention)
15:41:26 2025-07-14 14:41:26 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\2024_1_dev-bf-to-CH1
15:41:28 2025-07-14 14:41:28 elipy2 [INFO]: Found 40 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-clean
15:41:32 2025-07-14 14:41:32 elipy2 [INFO]: Nothing to delete! builds available 39 <= 40 maximum builds
15:41:32 2025-07-14 14:41:32 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\glacier-sku-transition
15:42:55 2025-07-14 14:42:55 elipy2 [INFO]: Nothing to delete! builds available 40 <= 40 maximum builds
15:42:55 2025-07-14 14:42:55 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-test
15:42:56 2025-07-14 14:42:56 elipy2 [INFO]: Found 37 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-cache
15:43:25 2025-07-14 14:43:25 elipy2 [INFO]: Nothing to delete! builds available 39 <= 40 maximum builds
15:43:25 2025-07-14 14:43:25 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-content-dev-cache
15:44:24 2025-07-14 14:44:24 elipy2 [INFO]: Preserving build \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-content-dev\23764468 (promoted build)
15:44:26 2025-07-14 14:44:26 elipy2 [INFO]: Nothing to delete! builds available 40 <= 40 maximum builds
15:44:26 2025-07-14 14:44:26 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\task1
15:44:29 2025-07-14 14:44:29 elipy2 [INFO]: Preserving build \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-to-trunk\23569006 (promoted build)
15:44:29 2025-07-14 14:44:29 elipy2 [INFO]: Deletion plan: 0 orphan builds, 0 regular builds (retention)
15:44:29 2025-07-14 14:44:29 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-to-trunk
15:44:30 2025-07-14 14:44:30 elipy2 [INFO]: Found 43 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\dev-na-to-trunk
15:44:34 2025-07-14 14:44:34 elipy2 [INFO]: Preserving build \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-content-dev\23773826 (drone build)
15:44:43 2025-07-14 14:44:43 elipy2 [INFO]: Preserving build \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-content-dev\23851036 (drone build)
15:45:03 2025-07-14 14:45:03 elipy2 [INFO]: Deletion plan: 0 orphan builds, 2 regular builds (retention)
15:45:03 2025-07-14 14:45:03 elipy2 [INFO]: Deleting 2 builds (2 regular, 0 orphan)
15:45:03 2025-07-14 14:45:03 elipy2 [INFO]: Phase 2: Deleting 2 builds based on retention policy
15:45:03 2025-07-14 14:45:03 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-content-dev\23948767
15:45:03 2025-07-14 14:45:03 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-content-dev\23962102
15:45:04 2025-07-14 14:45:04 elipy2 [INFO]: All planned deletions completed successfully
15:45:05 2025-07-14 14:45:05 elipy2 [INFO]: Found 22 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-sp-stage
15:49:03 2025-07-14 14:49:03 elipy2 [INFO]: Preserving build \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev\24179982 (promoted build)
15:49:11 2025-07-14 14:49:11 elipy2 [INFO]: Preserving build \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev\24222845 (drone build)
15:49:19 2025-07-14 14:49:19 elipy2 [INFO]: Preserving build \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev\24223794 (drone build)
15:49:28 2025-07-14 14:49:28 elipy2 [INFO]: Deletion plan: 0 orphan builds, 1 regular builds (retention)
15:49:28 2025-07-14 14:49:28 elipy2 [INFO]: Deleting 1 builds (1 regular, 0 orphan)
15:49:28 2025-07-14 14:49:28 elipy2 [INFO]: Phase 2: Deleting 1 builds based on retention policy
15:49:28 2025-07-14 14:49:28 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev\24405845
15:49:28 2025-07-14 14:49:28 elipy2 [INFO]: All planned deletions completed successfully
15:49:43 2025-07-14 14:49:43 elipy2 [INFO]: Nothing to delete! builds available 22 <= 40 maximum builds
15:49:43 2025-07-14 14:49:43 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-sp-stage
15:50:34 2025-07-14 14:50:34 elipy2 [INFO]: Nothing to delete! builds available 40 <= 40 maximum builds
15:50:34 2025-07-14 14:50:34 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-code-dev-clean
15:50:40 2025-07-14 14:50:40 elipy2 [INFO]: Nothing to delete! builds available 37 <= 40 maximum builds
15:50:40 2025-07-14 14:50:40 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-cache
15:52:02 2025-07-14 14:52:02 elipy2 [INFO]: Preserving build \\filer.dice.ad.ea.com\builds\Battlefield\code\dev-na-to-trunk\23607169 (promoted build)
15:52:06 2025-07-14 14:52:06 elipy2 [INFO]: Preserving build \\filer.dice.ad.ea.com\builds\Battlefield\code\dev-na-to-trunk\23878868 (drone build)
15:52:10 2025-07-14 14:52:10 elipy2 [INFO]: Preserving build \\filer.dice.ad.ea.com\builds\Battlefield\code\dev-na-to-trunk\23880899 (drone build)
15:52:10 2025-07-14 14:52:10 elipy2 [INFO]: Deletion plan: 0 orphan builds, 0 regular builds (retention)
15:52:10 2025-07-14 14:52:10 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\dev-na-to-trunk
15:53:56 2025-07-14 14:53:56 elipy2 [INFO]: Preserving build \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-marketing-dev\24241970 (drone build)
15:53:59 2025-07-14 14:53:59 elipy2 [INFO]: Preserving build \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-marketing-dev\24242417 (drone build)
15:54:07 2025-07-14 14:54:07 elipy2 [INFO]: Deletion plan: 0 orphan builds, 3 regular builds (retention)
15:54:07 2025-07-14 14:54:07 elipy2 [INFO]: Deleting 3 builds (3 regular, 0 orphan)
15:54:07 2025-07-14 14:54:07 elipy2 [INFO]: Phase 2: Deleting 3 builds based on retention policy
15:54:07 2025-07-14 14:54:07 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-marketing-dev\24413928
15:54:07 2025-07-14 14:54:07 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-marketing-dev\24414134
15:54:07 2025-07-14 14:54:07 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-marketing-dev\24414281
15:54:07 2025-07-14 14:54:07 elipy2 [INFO]: All planned deletions completed successfully
15:54:07 2025-07-14 14:54:07 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\23960235
15:54:07 2025-07-14 14:54:07 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\23962304
15:54:07 2025-07-14 14:54:07 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\23963269
15:54:07 2025-07-14 14:54:07 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\23966316
15:54:07 2025-07-14 14:54:07 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\23968940
15:54:07 2025-07-14 14:54:07 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\24023699
15:54:07 2025-07-14 14:54:07 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\24024280
15:54:07 2025-07-14 14:54:07 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\24037412
15:54:07 2025-07-14 14:54:07 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\24049808
15:54:07 2025-07-14 14:54:07 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\24065606
15:54:07 2025-07-14 14:54:07 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\24066631
15:54:07 2025-07-14 14:54:07 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\24067148
15:54:07 2025-07-14 14:54:07 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\24135898
15:54:07 2025-07-14 14:54:07 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\24136311
15:54:07 2025-07-14 14:54:07 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\24178746
15:54:07 2025-07-14 14:54:07 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\24194643
15:54:07 2025-07-14 14:54:07 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\24209162
15:54:07 2025-07-14 14:54:07 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\24224232
15:54:07 2025-07-14 14:54:07 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\24278534
15:54:07 2025-07-14 14:54:07 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\24278823
15:54:07 2025-07-14 14:54:07 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\24322397
15:54:07 2025-07-14 14:54:07 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\24325245
15:54:07 2025-07-14 14:54:07 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\24325976
15:54:07 2025-07-14 14:54:07 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\24353815
15:54:07 2025-07-14 14:54:07 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\24353887
15:54:07 2025-07-14 14:54:07 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\24355906
15:54:07 2025-07-14 14:54:07 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\24380100
15:54:07 2025-07-14 14:54:07 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\24386101
15:54:07 2025-07-14 14:54:07 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\24386516
15:54:07 2025-07-14 14:54:07 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\24387780
15:54:07 2025-07-14 14:54:07 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\24389005
15:54:07 2025-07-14 14:54:07 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\24389401
15:54:07 2025-07-14 14:54:07 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\24389751
15:54:07 2025-07-14 14:54:07 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\24401195
15:54:07 2025-07-14 14:54:07 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\24401749
15:54:07 2025-07-14 14:54:07 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\24403388
15:54:07 2025-07-14 14:54:07 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\24421100
15:54:07 2025-07-14 14:54:07 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\24441309
15:54:07 2025-07-14 14:54:07 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\24441890
15:54:07 2025-07-14 14:54:07 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\24441963
15:54:07 2025-07-14 14:54:07 elipy2 [INFO]: Found sanitizer subdirectory: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\asan
15:54:07 2025-07-14 14:54:07 elipy2 [INFO]: Found sanitizer subdirectory: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\ubsan
15:54:07 2025-07-14 14:54:07 elipy2 [INFO]: Found 40 direct CL directories in main sanitizer folder: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers
15:54:07 2025-07-14 14:54:07 elipy2 [INFO]: Found 40 CL directories in sanitizer subdirectory asan: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\asan
15:54:07 2025-07-14 14:54:07 elipy2 [INFO]: Found 15 CL directories in sanitizer subdirectory ubsan: \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\ubsan
15:54:07 2025-07-14 14:54:07 elipy2 [INFO]: Sanitizer scan complete for \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers: 95 total builds found (40 direct, 55 in subdirs)
15:54:07 2025-07-14 14:54:07 elipy2 [INFO]: Found 95 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers
15:58:01 2025-07-14 14:58:01 elipy2 [INFO]: Preserving build \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24192974 (drone build)
15:58:04 2025-07-14 14:58:04 elipy2 [INFO]: Preserving build \\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24195184 (drone build)
16:02:20 2025-07-14 15:02:20 elipy2 [INFO]: Preserving build \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\23960235 (drone build)
16:02:27 2025-07-14 15:02:27 elipy2 [INFO]: Preserving build \\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\23962304 (drone build)
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: Deletion plan: 0 orphan builds, 120 regular builds (retention)
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: Deleting 120 builds (120 regular, 0 orphan)
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: Phase 2: Deleting 120 builds based on retention policy
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24281756
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24283864
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24286115
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24288099
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24289589
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24290824
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24292376
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24293235
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24294756
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24295632
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24299030
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24301444
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24303473
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24305135
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24306466
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24307772
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24309398
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24311642
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24313090
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24315995
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24318068
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24320228
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24321590
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24323022
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24325788
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24327959
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24330117
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24335644
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24336637
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24349614
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24353219
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24355138
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24357207
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24359703
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24364434
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24366345
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24367988
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24369345
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24371701
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24373616
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24376287
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24377962
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24379875
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24381806
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan\24381806
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24383152
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\asan\24383152
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan\24383152
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24385284
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\asan\24385284
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan\24385284
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24387321
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\asan\24387321
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan\24387321
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\asan\24389566
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan\24389566
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\asan\24391091
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan\24391091
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\asan\24392443
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan\24392443
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\asan\24393779
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan\24393779
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24395224
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\asan\24395224
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan\24395224
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24396717
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\asan\24396717
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan\24396717
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24397983
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\asan\24397983
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan\24397983
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24399318
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\asan\24399318
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan\24399318
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24401099
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\asan\24401099
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan\24401099
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24403673
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\asan\24403673
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan\24403673
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24405962
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\asan\24405962
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan\24405962
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24407804
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\asan\24407804
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan\24407804
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24409551
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\asan\24409551
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan\24409551
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24411070
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\asan\24411070
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan\24411070
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24412155
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\asan\24412155
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan\24412155
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24413544
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\asan\24413544
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan\24413544
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24415848
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\asan\24415848
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan\24415848
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24417994
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\asan\24417994
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan\24417994
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24419848
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\asan\24419848
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan\24419848
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24422181
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\asan\24422181
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan\24422181
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24424159
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\asan\24424159
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan\24424159
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24425881
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\asan\24425881
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan\24425881
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24426779
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\asan\24426779
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\ubsan\24426779
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\ch1-content-dev-sanitizers\24427461
16:05:22 2025-07-14 15:05:22 elipy2 [INFO]: All planned deletions completed successfully
16:05:23 2025-07-14 15:05:23 elipy2 [INFO]: Found 42 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\code\CH1-event
16:05:40 2025-07-14 15:05:40 elipy2 [INFO]: Deletion plan: 0 orphan builds, 53 regular builds (retention)
16:05:40 2025-07-14 15:05:40 elipy2 [INFO]: Deleting 53 builds (53 regular, 0 orphan)
16:05:40 2025-07-14 15:05:40 elipy2 [INFO]: Phase 2: Deleting 53 builds based on retention policy
16:05:40 2025-07-14 15:05:40 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\ubsan\23960235
16:05:40 2025-07-14 15:05:40 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\asan\23962304
16:05:40 2025-07-14 15:05:40 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\ubsan\23962304
16:05:40 2025-07-14 15:05:40 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\23963269
16:05:40 2025-07-14 15:05:40 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\asan\23963269
16:05:40 2025-07-14 15:05:40 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\ubsan\23963269
16:05:40 2025-07-14 15:05:40 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\23966316
16:05:40 2025-07-14 15:05:40 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\asan\23966316
16:05:40 2025-07-14 15:05:40 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\ubsan\23966316
16:05:40 2025-07-14 15:05:40 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\23968940
16:05:40 2025-07-14 15:05:40 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\asan\23968940
16:05:40 2025-07-14 15:05:40 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\ubsan\23968940
16:05:40 2025-07-14 15:05:40 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\24023699
16:05:40 2025-07-14 15:05:40 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\asan\24023699
16:05:40 2025-07-14 15:05:40 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\ubsan\24023699
16:05:40 2025-07-14 15:05:40 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\24024280
16:05:40 2025-07-14 15:05:40 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\asan\24024280
16:05:40 2025-07-14 15:05:40 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\ubsan\24024280
16:05:40 2025-07-14 15:05:40 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\24037412
16:05:40 2025-07-14 15:05:40 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\asan\24037412
16:05:40 2025-07-14 15:05:40 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\ubsan\24037412
16:05:40 2025-07-14 15:05:40 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\asan\24049109
16:05:40 2025-07-14 15:05:40 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\24049808
16:05:40 2025-07-14 15:05:40 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\asan\24049808
16:05:40 2025-07-14 15:05:40 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\24065606
16:05:40 2025-07-14 15:05:40 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\asan\24065606
16:05:40 2025-07-14 15:05:40 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\24066631
16:05:40 2025-07-14 15:05:40 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\asan\24066631
16:05:40 2025-07-14 15:05:40 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\24067148
16:05:40 2025-07-14 15:05:40 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\asan\24067148
16:05:40 2025-07-14 15:05:40 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\24135898
16:05:40 2025-07-14 15:05:40 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\asan\24135898
16:05:40 2025-07-14 15:05:40 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\24136311
16:05:40 2025-07-14 15:05:40 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\asan\24136311
16:05:40 2025-07-14 15:05:40 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\24178746
16:05:40 2025-07-14 15:05:40 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\asan\24178746
16:05:40 2025-07-14 15:05:40 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\24194643
16:05:40 2025-07-14 15:05:40 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\asan\24194643
16:05:40 2025-07-14 15:05:40 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\24209162
16:05:40 2025-07-14 15:05:40 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\asan\24209162
16:05:40 2025-07-14 15:05:40 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\24224232
16:05:40 2025-07-14 15:05:40 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\asan\24224232
16:05:40 2025-07-14 15:05:40 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\24278534
16:05:40 2025-07-14 15:05:40 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\asan\24278534
16:05:40 2025-07-14 15:05:40 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\24278823
16:05:40 2025-07-14 15:05:40 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\asan\24278823
16:05:40 2025-07-14 15:05:40 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\24322397
16:05:40 2025-07-14 15:05:40 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\asan\24322397
16:05:40 2025-07-14 15:05:40 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\24325245
16:05:40 2025-07-14 15:05:40 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\asan\24325245
16:05:40 2025-07-14 15:05:40 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\24325976
16:05:40 2025-07-14 15:05:40 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\asan\24325976
16:05:40 2025-07-14 15:05:40 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\code\trunk-code-dev-sanitizers\24353815
16:05:40 2025-07-14 15:05:40 elipy2 [INFO]: All planned deletions completed successfully
16:08:16 2025-07-14 15:08:16 elipy2 [INFO]: Preserving build \\filer.dice.ad.ea.com\builds\Battlefield\code\CH1-event\24103633 (drone build)
16:08:20 2025-07-14 15:08:20 elipy2 [INFO]: Preserving build \\filer.dice.ad.ea.com\builds\Battlefield\code\CH1-event\24223891 (drone build)
16:08:20 2025-07-14 15:08:20 elipy2 [INFO]: Deletion plan: 0 orphan builds, 0 regular builds (retention)
16:08:20 2025-07-14 15:08:20 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\code\CH1-event
16:08:20 2025-07-14 15:08:20 elipy2 [INFO]: Disk-based discovery found 12 branches: ['0.1.0', '0.15.0', '0.15.1', '********', '0.15.2', '0.16.0', '0.16.1', '0.16.2', '0.17.0', '0.17.1', '*******', '2.0.0']
16:08:20 2025-07-14 15:08:20 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\webexport\0.15.0
16:08:20 2025-07-14 15:08:20 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\webexport\0.16.1
16:08:20 2025-07-14 15:08:20 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\webexport\0.16.2
16:08:20 2025-07-14 15:08:20 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\webexport\0.1.0
16:08:20 2025-07-14 15:08:20 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\webexport\********
16:08:20 2025-07-14 15:08:20 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\webexport\2.0.0
16:08:20 2025-07-14 15:08:20 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\webexport\0.17.1
16:08:20 2025-07-14 15:08:20 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\webexport\*******
16:08:20 2025-07-14 15:08:20 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\webexport\0.15.1
16:08:20 2025-07-14 15:08:20 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\webexport\0.17.0
16:08:20 2025-07-14 15:08:20 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\webexport\0.16.0
16:08:20 2025-07-14 15:08:20 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\webexport\0.15.2
16:08:20 2025-07-14 15:08:20 elipy2 [INFO]: Path does not exist: \\filer.dice.ad.ea.com\builds\Battlefield\webexport\0.15.0
16:08:20 2025-07-14 15:08:20 elipy2 [INFO]: Path does not exist: \\filer.dice.ad.ea.com\builds\Battlefield\webexport\0.16.1
16:08:20 2025-07-14 15:08:20 elipy2 [INFO]: Path does not exist: \\filer.dice.ad.ea.com\builds\Battlefield\webexport\0.16.2
16:08:20 2025-07-14 15:08:20 elipy2 [INFO]: Path does not exist: \\filer.dice.ad.ea.com\builds\Battlefield\webexport\0.1.0
16:08:20 2025-07-14 15:08:20 elipy2 [INFO]: Path does not exist: \\filer.dice.ad.ea.com\builds\Battlefield\webexport\********
16:08:20 2025-07-14 15:08:20 elipy2 [INFO]: Path does not exist: \\filer.dice.ad.ea.com\builds\Battlefield\webexport\2.0.0
16:08:20 2025-07-14 15:08:20 elipy2 [INFO]: Path does not exist: \\filer.dice.ad.ea.com\builds\Battlefield\webexport\0.17.1
16:08:20 2025-07-14 15:08:20 elipy2 [INFO]: Nothing to delete! builds available 0 <= 50 maximum builds
16:08:20 2025-07-14 15:08:20 elipy2 [INFO]: Path does not exist: \\filer.dice.ad.ea.com\builds\Battlefield\webexport\*******
16:08:20 2025-07-14 15:08:20 elipy2 [INFO]: Path does not exist: \\filer.dice.ad.ea.com\builds\Battlefield\webexport\0.15.1
16:08:20 2025-07-14 15:08:20 elipy2 [INFO]: Path does not exist: \\filer.dice.ad.ea.com\builds\Battlefield\webexport\0.17.0
16:08:20 2025-07-14 15:08:20 elipy2 [INFO]: Path does not exist: \\filer.dice.ad.ea.com\builds\Battlefield\webexport\0.16.0
16:08:20 2025-07-14 15:08:20 elipy2 [INFO]: Nothing to delete! builds available 0 <= 50 maximum builds
16:08:20 2025-07-14 15:08:20 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\webexport\0.15.0
16:08:20 2025-07-14 15:08:20 elipy2 [INFO]: Nothing to delete! builds available 0 <= 50 maximum builds
16:08:20 2025-07-14 15:08:20 elipy2 [INFO]: Nothing to delete! builds available 0 <= 50 maximum builds
16:08:20 2025-07-14 15:08:20 elipy2 [INFO]: Nothing to delete! builds available 0 <= 50 maximum builds
16:08:20 2025-07-14 15:08:20 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\webexport\0.16.1
16:08:20 2025-07-14 15:08:20 elipy2 [INFO]: Nothing to delete! builds available 0 <= 50 maximum builds
16:08:20 2025-07-14 15:08:20 elipy2 [INFO]: Nothing to delete! builds available 0 <= 50 maximum builds
16:08:20 2025-07-14 15:08:20 elipy2 [INFO]: Nothing to delete! builds available 0 <= 50 maximum builds
16:08:20 2025-07-14 15:08:20 elipy2 [INFO]: Nothing to delete! builds available 0 <= 50 maximum builds
16:08:20 2025-07-14 15:08:20 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\webexport\0.16.2
16:08:20 2025-07-14 15:08:20 elipy2 [INFO]: Nothing to delete! builds available 0 <= 50 maximum builds
16:08:20 2025-07-14 15:08:20 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\webexport\0.1.0
16:08:20 2025-07-14 15:08:20 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\webexport\********
16:08:20 2025-07-14 15:08:20 elipy2 [INFO]: Nothing to delete! builds available 0 <= 50 maximum builds
16:08:20 2025-07-14 15:08:20 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\webexport\2.0.0
16:08:20 2025-07-14 15:08:20 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\webexport\0.17.1
16:08:20 2025-07-14 15:08:20 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\webexport\*******
16:08:20 2025-07-14 15:08:20 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\webexport\0.15.1
16:08:20 2025-07-14 15:08:20 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\webexport\0.17.0
16:08:20 2025-07-14 15:08:20 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\webexport\0.16.0
16:08:20 2025-07-14 15:08:20 elipy2 [INFO]: Path does not exist: \\filer.dice.ad.ea.com\builds\Battlefield\webexport\0.15.2
16:08:20 2025-07-14 15:08:20 elipy2 [INFO]: Nothing to delete! builds available 0 <= 50 maximum builds
16:08:20 2025-07-14 15:08:20 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\webexport\0.15.2
16:08:21 2025-07-14 15:08:21 elipy2 [INFO]: Disk-based discovery found 29 branches: ['CH1-SP-content-dev', 'CH1-SP-stage', 'CH1-code-dev', 'CH1-code-dev-asan', 'CH1-code-dev-sanitizers', 'CH1-content-dev', 'CH1-content-dev-cache', 'CH1-content-dev-metrics', 'CH1-marketing-dev', 'CH1-playtest', 'CH1-playtest-stage', 'CH1-qol', 'CH1-stage', 'CH1-to-trunk', 'dev-na-to-trunk', 'dev-na-to-trunk-sub', 'media-team', 'task1', 'task2', 'task4', 'trunk-code-dev', 'trunk-code-dev-asan', 'trunk-code-dev-sanitizers', 'trunk-code-dev-skybuild', 'trunk-content-dev', 'trunk-content-dev-cache', 'trunk-content-dev-metrics', 'trunk-playtest', 'trunk-to-dev-na']
16:08:21 2025-07-14 15:08:21 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-playtest-stage
16:08:21 2025-07-14 15:08:21 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-marketing-dev
16:08:21 2025-07-14 15:08:21 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-to-trunk
16:08:21 2025-07-14 15:08:21 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-SP-stage
16:08:21 2025-07-14 15:08:21 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-code-dev-skybuild
16:08:21 2025-07-14 15:08:21 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-content-dev
16:08:21 2025-07-14 15:08:21 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-SP-content-dev
16:08:21 2025-07-14 15:08:21 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-code-dev
16:08:21 2025-07-14 15:08:21 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-code-dev-sanitizers
16:08:21 2025-07-14 15:08:21 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\media-team
16:08:21 2025-07-14 15:08:21 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\dev-na-to-trunk-sub
16:08:21 2025-07-14 15:08:21 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-to-dev-na
16:08:21 2025-07-14 15:08:21 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-code-dev-asan
16:08:21 2025-07-14 15:08:21 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-content-dev-metrics
16:08:21 2025-07-14 15:08:21 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-content-dev-cache
16:08:21 2025-07-14 15:08:21 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-stage
16:08:21 2025-07-14 15:08:21 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\task2
16:08:21 2025-07-14 15:08:21 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-code-dev-asan
16:08:21 2025-07-14 15:08:21 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-content-dev-cache
16:08:21 2025-07-14 15:08:21 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-content-dev-metrics
16:08:21 2025-07-14 15:08:21 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-content-dev
16:08:21 2025-07-14 15:08:21 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-code-dev-sanitizers
16:08:21 2025-07-14 15:08:21 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\task1
16:08:21 2025-07-14 15:08:21 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\dev-na-to-trunk
16:08:21 2025-07-14 15:08:21 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-playtest
16:08:21 2025-07-14 15:08:21 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-code-dev
16:08:21 2025-07-14 15:08:21 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-qol
16:08:21 2025-07-14 15:08:21 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\task4
16:08:21 2025-07-14 15:08:21 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-playtest
16:08:21 2025-07-14 15:08:21 elipy2 [INFO]: Found 1 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-playtest-stage
16:08:21 2025-07-14 15:08:21 elipy2 [INFO]: Found 1 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-SP-stage
16:08:21 2025-07-14 15:08:21 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-code-dev-sanitizers\24003088
16:08:21 2025-07-14 15:08:21 elipy2 [INFO]: Found 5 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-code-dev-skybuild
16:08:21 2025-07-14 15:08:21 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-code-dev-sanitizers\24004119
16:08:21 2025-07-14 15:08:21 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-code-dev-sanitizers\24010679
16:08:21 2025-07-14 15:08:21 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-code-dev-sanitizers\24013106
16:08:21 2025-07-14 15:08:21 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-code-dev-sanitizers\24013423
16:08:21 2025-07-14 15:08:21 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-code-dev-sanitizers\24015920
16:08:21 2025-07-14 15:08:21 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-code-dev-sanitizers\24019386
16:08:21 2025-07-14 15:08:21 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-code-dev-sanitizers\24021916
16:08:21 2025-07-14 15:08:21 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-code-dev-sanitizers\24023774
16:08:21 2025-07-14 15:08:21 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-code-dev-sanitizers\24025232
16:08:21 2025-07-14 15:08:21 elipy2 [INFO]: Found 16 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-to-trunk
16:08:21 2025-07-14 15:08:21 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-code-dev-sanitizers\24027941
16:08:21 2025-07-14 15:08:21 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-code-dev-sanitizers\24029263
16:08:21 2025-07-14 15:08:21 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-code-dev-sanitizers\24031339
16:08:21 2025-07-14 15:08:21 elipy2 [INFO]: Found 13 direct CL directories in main sanitizer folder: \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-code-dev-sanitizers
16:08:21 2025-07-14 15:08:21 elipy2 [INFO]: Sanitizer scan complete for \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-code-dev-sanitizers: 13 total builds found (13 direct, 0 in subdirs)
16:08:21 2025-07-14 15:08:21 elipy2 [INFO]: Found 13 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-code-dev-sanitizers
16:08:21 2025-07-14 15:08:21 elipy2 [INFO]: Found 17 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\dev-na-to-trunk-sub
16:08:21 2025-07-14 15:08:21 elipy2 [INFO]: Found 32 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-code-dev
16:08:21 2025-07-14 15:08:21 elipy2 [INFO]: Found 50 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-SP-content-dev
16:08:21 2025-07-14 15:08:21 elipy2 [INFO]: Found 54 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-marketing-dev
16:08:21 2025-07-14 15:08:21 elipy2 [INFO]: Found 51 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-content-dev
16:08:21 2025-07-14 15:08:21 elipy2 [INFO]: Found 52 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\media-team
16:08:37 2025-07-14 15:08:37 elipy2 [INFO]: Nothing to delete! builds available 1 <= 50 maximum builds
16:08:37 2025-07-14 15:08:37 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-SP-stage
16:08:38 2025-07-14 15:08:38 elipy2 [INFO]: Nothing to delete! builds available 1 <= 50 maximum builds
16:08:38 2025-07-14 15:08:38 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-playtest-stage
16:08:39 2025-07-14 15:08:39 elipy2 [INFO]: Found 12 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-to-dev-na
16:08:40 2025-07-14 15:08:40 elipy2 [INFO]: Found 17 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-code-dev-asan
16:09:59 2025-07-14 15:09:59 elipy2 [INFO]: Nothing to delete! builds available 5 <= 50 maximum builds
16:09:59 2025-07-14 15:09:59 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-code-dev-skybuild
16:10:00 2025-07-14 15:10:00 elipy2 [INFO]: Found 48 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-content-dev-metrics
16:12:39 2025-07-14 15:12:39 elipy2 [INFO]: Nothing to delete! builds available 13 <= 50 maximum builds
16:12:39 2025-07-14 15:12:39 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-code-dev-sanitizers
16:12:41 2025-07-14 15:12:41 elipy2 [INFO]: Found 17 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-content-dev-cache
16:12:57 2025-07-14 15:12:56 elipy2 [INFO]: Nothing to delete! builds available 12 <= 50 maximum builds
16:12:57 2025-07-14 15:12:56 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-to-dev-na
16:12:58 2025-07-14 15:12:57 elipy2 [INFO]: Found 50 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-stage
16:14:08 2025-07-14 15:14:07 elipy2 [INFO]: Nothing to delete! builds available 16 <= 50 maximum builds
16:14:08 2025-07-14 15:14:07 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-to-trunk
16:14:09 2025-07-14 15:14:09 elipy2 [INFO]: Found 24 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\task2
16:14:18 2025-07-14 15:14:18 elipy2 [INFO]: Nothing to delete! builds available 17 <= 50 maximum builds
16:14:18 2025-07-14 15:14:18 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\dev-na-to-trunk-sub
16:14:19 2025-07-14 15:14:19 elipy2 [INFO]: Found 14 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-code-dev-asan
16:14:45 2025-07-14 15:14:44 elipy2 [INFO]: Nothing to delete! builds available 17 <= 50 maximum builds
16:14:45 2025-07-14 15:14:44 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-code-dev-asan
16:14:45 2025-07-14 15:14:45 elipy2 [INFO]: Found 17 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-content-dev-cache
16:19:57 2025-07-14 15:19:57 elipy2 [INFO]: Nothing to delete! builds available 17 <= 50 maximum builds
16:19:57 2025-07-14 15:19:57 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-content-dev-cache
16:19:59 2025-07-14 15:19:58 elipy2 [INFO]: Found 50 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-content-dev-metrics
16:20:24 2025-07-14 15:20:23 elipy2 [INFO]: Nothing to delete! builds available 14 <= 50 maximum builds
16:20:24 2025-07-14 15:20:23 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-code-dev-asan
16:20:25 2025-07-14 15:20:24 elipy2 [INFO]: Found 23 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-content-dev
16:21:04 2025-07-14 15:21:04 elipy2 [INFO]: Nothing to delete! builds available 32 <= 50 maximum builds
16:21:04 2025-07-14 15:21:04 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-code-dev
16:21:06 2025-07-14 15:21:05 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-code-dev-sanitizers\23846862
16:21:06 2025-07-14 15:21:05 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-code-dev-sanitizers\23904765
16:21:06 2025-07-14 15:21:05 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-code-dev-sanitizers\23916815
16:21:06 2025-07-14 15:21:05 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-code-dev-sanitizers\23929926
16:21:06 2025-07-14 15:21:05 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-code-dev-sanitizers\23931126
16:21:06 2025-07-14 15:21:05 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-code-dev-sanitizers\23950869
16:21:06 2025-07-14 15:21:05 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-code-dev-sanitizers\23960229
16:21:06 2025-07-14 15:21:05 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-code-dev-sanitizers\23962304
16:21:06 2025-07-14 15:21:05 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-code-dev-sanitizers\23963269
16:21:06 2025-07-14 15:21:05 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-code-dev-sanitizers\23966316
16:21:06 2025-07-14 15:21:05 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-code-dev-sanitizers\23968940
16:21:06 2025-07-14 15:21:05 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-code-dev-sanitizers\24023699
16:21:06 2025-07-14 15:21:05 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-code-dev-sanitizers\24024280
16:21:06 2025-07-14 15:21:05 elipy2 [INFO]: Found direct CL directory in sanitizer path: \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-code-dev-sanitizers\24037412
16:21:06 2025-07-14 15:21:05 elipy2 [INFO]: Found 14 direct CL directories in main sanitizer folder: \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-code-dev-sanitizers
16:21:06 2025-07-14 15:21:05 elipy2 [INFO]: Sanitizer scan complete for \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-code-dev-sanitizers: 14 total builds found (14 direct, 0 in subdirs)
16:21:06 2025-07-14 15:21:05 elipy2 [INFO]: Found 14 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-code-dev-sanitizers
16:21:56 2025-07-14 15:21:56 elipy2 [INFO]: Nothing to delete! builds available 17 <= 50 maximum builds
16:21:56 2025-07-14 15:21:56 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-content-dev-cache
16:21:58 2025-07-14 15:21:57 elipy2 [INFO]: Found 26 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\task1
16:24:21 2025-07-14 15:24:20 elipy2 [INFO]: Nothing to delete! builds available 24 <= 50 maximum builds
16:24:21 2025-07-14 15:24:20 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\task2
16:24:22 2025-07-14 15:24:21 elipy2 [INFO]: Found 23 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\dev-na-to-trunk
16:27:03 2025-07-14 15:27:02 elipy2 [INFO]: Nothing to delete! builds available 14 <= 50 maximum builds
16:27:03 2025-07-14 15:27:02 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-code-dev-sanitizers
16:27:04 2025-07-14 15:27:03 elipy2 [INFO]: Found 1 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-playtest
16:27:30 2025-07-14 15:27:29 elipy2 [INFO]: Nothing to delete! builds available 1 <= 50 maximum builds
16:27:30 2025-07-14 15:27:29 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-playtest
16:27:32 2025-07-14 15:27:32 elipy2 [INFO]: Found 50 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-code-dev
16:28:38 2025-07-14 15:28:38 elipy2 [INFO]: Nothing to delete! builds available 50 <= 50 maximum builds
16:28:38 2025-07-14 15:28:38 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-SP-content-dev
16:28:39 2025-07-14 15:28:39 elipy2 [INFO]: Found 14 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-qol
16:29:15 2025-07-14 15:29:15 elipy2 [INFO]: Deletion plan: 0 orphan builds, 1 regular builds (retention)
16:29:15 2025-07-14 15:29:15 elipy2 [INFO]: Deleting 1 builds (1 regular, 0 orphan)
16:29:15 2025-07-14 15:29:15 elipy2 [INFO]: Phase 2: Deleting 1 builds based on retention policy
16:29:15 2025-07-14 15:29:15 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-content-dev\24425458
16:29:15 2025-07-14 15:29:15 elipy2 [INFO]: All planned deletions completed successfully
16:29:17 2025-07-14 15:29:16 elipy2 [INFO]: Found 11 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\task4
16:29:38 2025-07-14 15:29:38 elipy2 [INFO]: Nothing to delete! builds available 48 <= 50 maximum builds
16:29:38 2025-07-14 15:29:38 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-content-dev-metrics
16:29:39 2025-07-14 15:29:38 elipy2 [INFO]: Found 6 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-playtest
16:29:55 2025-07-14 15:29:54 elipy2 [INFO]: Deletion plan: 0 orphan builds, 2 regular builds (retention)
16:29:55 2025-07-14 15:29:54 elipy2 [INFO]: Deleting 2 builds (2 regular, 0 orphan)
16:29:55 2025-07-14 15:29:54 elipy2 [INFO]: Phase 2: Deleting 2 builds based on retention policy
16:29:55 2025-07-14 15:29:54 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\media-team\21941890
16:29:55 2025-07-14 15:29:54 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\media-team\21943952
16:29:55 2025-07-14 15:29:55 elipy2 [INFO]: All planned deletions completed successfully
16:30:04 2025-07-14 15:30:04 elipy2 [INFO]: Nothing to delete! builds available 23 <= 50 maximum builds
16:30:04 2025-07-14 15:30:04 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-content-dev
16:30:37 2025-07-14 15:30:36 elipy2 [INFO]: Deletion plan: 0 orphan builds, 4 regular builds (retention)
16:30:37 2025-07-14 15:30:37 elipy2 [INFO]: Deleting 4 builds (4 regular, 0 orphan)
16:30:37 2025-07-14 15:30:37 elipy2 [INFO]: Phase 2: Deleting 4 builds based on retention policy
16:30:37 2025-07-14 15:30:37 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-marketing-dev\24426933
16:30:37 2025-07-14 15:30:37 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-marketing-dev\24427072
16:30:37 2025-07-14 15:30:37 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-marketing-dev\24427459
16:30:37 2025-07-14 15:30:37 elipy2 [INFO]: about to delete: file:\\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-marketing-dev\24427786
16:30:38 2025-07-14 15:30:37 elipy2 [INFO]: All planned deletions completed successfully
16:31:31 2025-07-14 15:31:31 elipy2 [INFO]: Nothing to delete! builds available 6 <= 50 maximum builds
16:31:31 2025-07-14 15:31:31 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\trunk-playtest
16:31:50 2025-07-14 15:31:50 elipy2 [INFO]: Nothing to delete! builds available 26 <= 50 maximum builds
16:31:50 2025-07-14 15:31:50 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\task1
16:32:27 2025-07-14 15:32:26 elipy2 [INFO]: Nothing to delete! builds available 11 <= 50 maximum builds
16:32:27 2025-07-14 15:32:26 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\task4
16:32:27 2025-07-14 15:32:27 elipy2 [INFO]: Nothing to delete! builds available 50 <= 50 maximum builds
16:32:27 2025-07-14 15:32:27 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-stage
16:32:28 2025-07-14 15:32:28 elipy2 [INFO]: Nothing to delete! builds available 23 <= 50 maximum builds
16:32:28 2025-07-14 15:32:28 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\dev-na-to-trunk
16:32:35 2025-07-14 15:32:35 elipy2 [INFO]: Nothing to delete! builds available 14 <= 50 maximum builds
16:32:35 2025-07-14 15:32:35 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-qol
16:33:49 2025-07-14 15:33:48 elipy2 [INFO]: Nothing to delete! builds available 50 <= 50 maximum builds
16:33:49 2025-07-14 15:33:48 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-content-dev-metrics
16:35:02 2025-07-14 15:35:02 elipy2 [INFO]: Nothing to delete! builds available 50 <= 50 maximum builds
16:35:02 2025-07-14 15:35:02 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\BattlefieldGame\CH1-code-dev
16:35:02 2025-07-14 15:35:02 elipy2 [INFO]: Disk-based discovery found 1 branches: ['trunk-to-dev-na']
16:35:02 2025-07-14 15:35:02 elipy2 [INFO]: retaining 50 at file:\\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\Frostbite\trunk-to-dev-na
16:35:02 2025-07-14 15:35:02 elipy2 [INFO]: Found 13 builds on disk at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\Frostbite\trunk-to-dev-na
16:35:52 2025-07-14 15:35:51 elipy2 [INFO]: Nothing to delete! builds available 13 <= 50 maximum builds
16:35:52 2025-07-14 15:35:51 elipy2 [INFO]: No builds to delete at \\filer.dice.ad.ea.com\builds\Battlefield\expressiondebugdata\Frostbite\trunk-to-dev-na
16:35:52 2025-07-14 15:35:51 elipy2 [WARNING]: Path does not exist, skipping branch discovery: \\filer.dice.ad.ea.com\builds\Battlefield\tnt_local
16:35:52 2025-07-14 15:35:51 elipy2 [INFO]: Skipping azure_fileshare_path_retention files
16:35:52 2025-07-14 15:35:51 elipy2 [WARNING]: Skipping Avalanche db deletion.
16:35:52 2025-07-14 15:35:51 elipy2 [WARNING]: Skipping symstore cleanup.

