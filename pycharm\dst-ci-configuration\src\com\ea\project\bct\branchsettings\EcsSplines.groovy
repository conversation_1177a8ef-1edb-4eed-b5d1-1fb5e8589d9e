package com.ea.project.bct.branchsettings

import com.ea.project.bct.Bct

class EcsSplines {
    // Settings for jobs
    static Class project = Bct
    static Map general_settings = [
        dataset                 : project.dataset,
        elipy_call              : project.elipy_call + ' --use-fbenv-core',
        elipy_install_call      : project.elipy_install_call,
        frostbite_licensee      : project.frostbite_licensee,
        workspace_root          : project.workspace_root,
        azure_elipy_call        : project.azure_elipy_call,
        azure_elipy_install_call: project.azure_elipy_install_call,
        azure_workspace_root    : project.azure_workspace_root,
        azure_fileshare          : [
            additional_tools_to_include: ['frostedtests', 'win64'],
            secret_context             : 'glacier_azure_fileshare',
            target_build_share         : 'bfglacier',
        ],
    ]
    static Map code_settings = [
        deploy_frostedtests          : true,
        deploy_tests                 : true,
        skip_code_build_if_no_changes: false,
        slack_channel_code           : [
            channels                  : ['#bct-build-notify'],
            skip_for_multiple_failures: true,
        ],
        fake_ooa_wrapped_symbol      : false,
        statebuild_code              : false,
        sndbs_enabled                : true,
    ]
    static Map standard_jobs_settings = code_settings + [
        asset                     : 'DevLevels',
        skip_icepick_settings_file: true,
        strip_symbols             : false,
        move_location_parallel    : true,
        new_locations             : [
            RippleEffect: [
                elipy_call_new_location: project.elipy_call_eala + ' --use-fbenv-core',
            ],
        ],
    ]
    static Map icepick_settings = [
        ignore_icepick_exit_code: false
    ]
    static Map preflight_settings = [:]

    // Matrix definitions for jobs
    static List code_matrix = [
        [name: 'win64game', configs: ['final']],
        [name: 'win64server', configs: ['final']],
        [name: 'xbsx', configs: ['final']],
        [name: 'ps5', configs: ['final']],
        [name: 'tool', configs: [[name: 'release', compile_unit_tests: true, run_unit_tests: true],]],
    ]
    static List code_nomaster_matrix = []
    static List code_downstream_matrix = [
        [name: '.bilbo.register-bfdata-dronebuild', args: ['code_changelist', 'mirror_changelist']],
        [name: '.code.tool.release.copy-build-to-azure', args: ['code_changelist']],
    ]
    static List data_matrix = []
    static List data_downstream_matrix = []
    static List patchdata_matrix = []
    static List frosty_matrix = []
    static List frosty_downstream_matrix = []
    static List frosty_for_patch_matrix = []
    static List patchfrosty_matrix = []
    static List patchfrosty_downstream_matrix = []
    static List code_preflight_matrix = []
    static List data_preflight_matrix = []
    static List spin_upload_matrix = []
    static List shift_upload_matrix = []
    static List shift_downstream_matrix = []
    static List freestyle_job_trigger_matrix = []
    static List azure_uploads_matrix = [
        [platform: 'tool', content_type: ['code'], config: ['release']]
    ]
}
