package all

import com.ea.lib.LibJobDsl

LibJobDsl.standardPipelineJob(this, 'node-status-influxdb-cobra-etl', 'src/scripts/schedulers/node-status-influxdb.groovy').with {
    logRotator(1, 60)
    description('Sends node and label usage data to influxdb for Cobra monitoring platform.')
    properties {
        pipelineTriggers {
            triggers {
                cron {
                    spec('H/5 * * * *')
                }
            }
        }
    }
}

pipelineJob('retrigger-jobs-with-scm-on-failure') {
    description('Look at not running jobs that implements SCMTriggerItem and see if they are failing ' +
        'with certain strings in their log and then try to trigger them.'
    )
    logRotator(7, 50)
    quietPeriod(0)
    properties {
        disableConcurrentBuilds()
        disableResume()
        pipelineTriggers {
            triggers {
                cron {
                    spec('H * * * 1-5') // Runs every hour per working day, if anything is wrong, we will get slack on Monday morning
                }
            }
        }
    }
    definition {
        cps {
            script(readFileFromWorkspace('src/scripts/schedulers/retriggerJobWithScmOnFailure.groovy'))
            sandbox(true)
        }
    }
}

pipelineJob('restart-nodes-on-failure') {
    description('Look at not running jobs and see if they are failing ' +
        'with certain strings in their log and then restart the nodes.'
    )
    logRotator(7, 50)
    quietPeriod(0)
    properties {
        disableConcurrentBuilds()
        disableResume()
        pipelineTriggers {
            triggers {
                cron {
                    spec('H * * * 1-5') // Runs every hour per working day, if anything is wrong, we will get slack on Monday morning
                }
            }
        }
    }
    definition {
        cps {
            script(readFileFromWorkspace('src/scripts/schedulers/restartAgentOnFailure.groovy'))
            sandbox(true)
        }
    }
}

pipelineJob('reapply-bfa-to-job') {
    description('This job is used to reapply the BFA (Build Failure Analyzer) to a specific build')
    logRotator(7, 50)
    quietPeriod(0)
    properties {
        disableConcurrentBuilds()
        disableResume()
    }
    parameters {
        stringParam {
            name('jobUrl')
            defaultValue('')
            description('The URL of the job to reapply BFA. Example: https://example.com/job/my_job/1234/')
            trim(true)
        }
    }
    definition {
        cps {
            script(readFileFromWorkspace('src/scripts/schedulers/reapplyBfaToJob.groovy'))
            sandbox(true)
        }
    }
}
