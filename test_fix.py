#!/usr/bin/env python
"""Test script to verify the combined_bundles test fixes."""

import subprocess
import sys
import os

def run_test():
    """Run the failing tests to check if they pass now."""
    os.chdir(r"c:\Users\<USER>\vscode")
    
    # Run the specific failing tests
    cmd = [
        sys.executable, "-m", "pytest", 
        r"pycharm\elipy-scripts\dice_elipy_scripts\tests\test_combined_bundles.py::TestCombinedBundles::test_xbsx_platform_uses_smart_delivery_settings",
        r"pycharm\elipy-scripts\dice_elipy_scripts\tests\test_combined_bundles.py::TestCombinedBundles::test_non_xbsx_platform_uses_regular_settings",
        "-v"
    ]
    
    result = subprocess.run(cmd, capture_output=True, text=True)
    
    print("Return code:", result.returncode)
    print("STDOUT:")
    print(result.stdout)
    print("STDERR:")
    print(result.stderr)
    
    return result.returncode == 0

if __name__ == "__main__":
    success = run_test()
    if success:
        print("Tests passed!")
    else:
        print("Tests failed!")
    sys.exit(0 if success else 1)
