security:
  scriptApproval:
    approvedSignatures:
      - field hudson.model.Slave name
      - field hudson.slaves.Cloud name
      - field hudson.slaves.WorkspaceList inUse
      - field java.io.File separatorChar
      - field jenkins.model.Jenkins clouds
      - method com.amazon.jenkins.ec2fleet.AbstractEC2FleetCloud getFleet
      - method com.amazon.jenkins.ec2fleet.AbstractEC2FleetCloud getOldId
      - method com.amazon.jenkins.ec2fleet.AbstractEC2FleetCloud isAlwaysReconnect
      - method com.amazon.jenkins.ec2fleet.AbstractEC2FleetCloud isDisableTaskResubmit
      - method com.amazon.jenkins.ec2fleet.EC2FleetCloud getAwsCredentialsId
      - method com.amazon.jenkins.ec2fleet.EC2FleetCloud getCloudStatusIntervalSec
      - method com.amazon.jenkins.ec2fleet.EC2FleetCloud getComputerConnector
      - method com.amazon.jenkins.ec2fleet.EC2FleetCloud getEndpoint
      - method com.amazon.jenkins.ec2fleet.EC2FleetCloud getFleet
      - method com.amazon.jenkins.ec2fleet.EC2FleetCloud getFsRoot
      - method com.amazon.jenkins.ec2fleet.EC2FleetCloud getInitOnlineCheckIntervalSec
      - method com.amazon.jenkins.ec2fleet.EC2FleetCloud getInitOnlineTimeoutSec
      - method com.amazon.jenkins.ec2fleet.EC2FleetCloud getLabelString
      - method com.amazon.jenkins.ec2fleet.EC2FleetCloud getMaxTotalUses
      - method com.amazon.jenkins.ec2fleet.EC2FleetCloud getNumExecutors
      - method com.amazon.jenkins.ec2fleet.EC2FleetCloud getRegion
      - method com.amazon.jenkins.ec2fleet.EC2FleetCloud getStats
      - method com.amazon.jenkins.ec2fleet.EC2FleetCloud isAddNodeOnlyIfRunning
      - method com.amazon.jenkins.ec2fleet.EC2FleetCloud isNoDelayProvision
      - method com.amazon.jenkins.ec2fleet.EC2FleetCloud isPrivateIpUsed
      - method com.amazon.jenkins.ec2fleet.EC2FleetCloud isRestrictUsage
      - method com.amazon.jenkins.ec2fleet.EC2FleetCloud isScaleExecutorsByWeight
      - method com.amazon.jenkins.ec2fleet.EC2FleetCloud setStats com.amazon.jenkins.ec2fleet.FleetStateStats
      - method com.ea.jenkins.plugins.resourcemanagerplugin.ResourceManagerConfiguration getServiceInterface
      - method com.ea.resourcemanagerclient.AbstractResource getId
      - method com.ea.resourcemanagerclient.AbstractResource getIsReserved
      - method com.ea.resourcemanagerclient.AbstractResource getName
      - method com.ea.resourcemanagerclient.AbstractResource getProperties
      - method com.ea.resourcemanagerclient.AbstractResource getProperty java.lang.String
      - method com.ea.resourcemanagerclient.AbstractResource setProperty java.lang.String java.lang.String
      - method com.microsoft.azure.vmagent.AzureVMAgent getPublicDNSName
      - method com.perforce.p4java.impl.mapbased.server.Server setClientName java.lang.String
      - method com.perforce.p4java.server.IHelixCommandExecutor execMapCmdList java.lang.String java.lang.String[] java.util.Map
      - method com.sonyericsson.jenkins.plugins.bfa.model.FailureCauseBuildAction getFoundFailureCauses
      - method com.sonyericsson.jenkins.plugins.bfa.model.FoundFailureCause getCategories
      - method com.sonyericsson.jenkins.plugins.bfa.model.FoundFailureCause getId
      - method com.sonyericsson.jenkins.plugins.bfa.model.FoundFailureCause getIndications
      - method com.sonyericsson.jenkins.plugins.bfa.model.FoundFailureCause getName
      - method com.sonyericsson.jenkins.plugins.bfa.model.indication.FoundIndication getMatchingHash
      - method com.sonyericsson.jenkins.plugins.bfa.model.primarycause.PrimaryCauseAction getPrimaryCause
      - method groovy.json.JsonSlurper parse java.io.InputStream java.lang.String
      - method groovy.lang.Binding getVariables
      - method groovy.lang.GroovyObject getProperty java.lang.String
      - method groovy.lang.GroovyObject invokeMethod java.lang.String java.lang.Object
      - method groovy.lang.GroovyObject setProperty java.lang.String java.lang.Object
      - method groovy.lang.Script println
      - method groovy.lang.Script println java.lang.Object
      - method groovy.util.XmlParser parseText java.lang.String
      - method hudson.Launcher isUnix
      - method hudson.Launcher launch hudson.Launcher$ProcStarter
      - method hudson.Launcher$ProcStarter cmdAsSingleString java.lang.String
      - method hudson.Launcher$ProcStarter pwd hudson.FilePath
      - method hudson.Launcher$ProcStarter stdout hudson.model.TaskListener
      - method hudson.model.AbstractBuild doStop
      - method hudson.Proc join
      - method hudson.model.AbstractCIBase getNodes
      - method hudson.model.AbstractCIBase getQueue
      - method hudson.model.AbstractProject enable
      - method hudson.model.Actionable getAction java.lang.Class
      - method hudson.model.Actionable getActions
      - method hudson.model.Actionable removeActions java.lang.Class
      - method hudson.model.Actionable replaceAction hudson.model.Action
      - method hudson.model.BuildableItem scheduleBuild hudson.model.Cause
      - method hudson.model.Cause getShortDescription
      - method hudson.model.Cause$UserIdCause getUserId
      - method hudson.model.Cause$UserIdCause getUserName
      - method hudson.model.CauseAction findCause java.lang.Class
      - method hudson.model.Computer countBusy
      - method hudson.model.Computer doDoDelete
      - method hudson.model.Computer doToggleOffline java.lang.String
      - method hudson.model.Computer getBuilds
      - method hudson.model.Computer getExecutors
      - method hudson.model.Computer getName
      - method hudson.model.Computer getNode
      - method hudson.model.Computer getOfflineCauseReason
      - method hudson.model.Computer getWorkspaceList
      - method hudson.model.Computer isIdle
      - method hudson.model.Computer isOffline
      - method hudson.model.Computer isOnline
      - method hudson.model.Computer setTemporarilyOffline boolean hudson.slaves.OfflineCause
      - method hudson.model.Executor getCurrentExecutable
      - method hudson.model.Executor interrupt hudson.model.Result
      - method hudson.model.Item delete
      - method hudson.model.Item getFullDisplayName
      - method hudson.model.Item getName
      - method hudson.model.Item getUrl
      - method hudson.model.ItemGroup getAllItems java.lang.Class
      - method hudson.model.ItemGroup getItem java.lang.String
      - method hudson.model.ItemGroup getItems
      - method hudson.model.Job getBuilds
      - method hudson.model.Job getBuildsByTimestamp long long
      - method hudson.model.Job getBuildByNumber int
      - method hudson.model.Job getLastBuild
      - method hudson.model.Job getLastBuildsOverThreshold int hudson.model.Result
      - method hudson.model.Job getLastCompletedBuild
      - method hudson.model.Job getLastStableBuild
      - method hudson.model.Job getLastSuccessfulBuild
      - method hudson.model.Job getProperty java.lang.Class
      - method hudson.model.Job getProperty java.lang.String
      - method hudson.model.Job isBuildable
      - method hudson.model.Job isBuilding
      - method hudson.model.Job isInQueue
      - method hudson.model.Label getBusyExecutors
      - method hudson.model.Label getIdleExecutors
      - method hudson.model.Label getName
      - method hudson.model.Label getTotalExecutors
      - method hudson.model.Node createLauncher hudson.model.TaskListener
      - method hudson.model.Node getAssignedLabels
      - method hudson.model.Node getLabelString
      - method hudson.model.Node getNodeName
      - method hudson.model.Node getRootPath
      - method hudson.model.Node toComputer
      - method hudson.model.ParametersAction createUpdated java.util.Collection
      - method hudson.model.Queue clear
      - method hudson.model.Queue countBuildableItemsFor hudson.model.Label
      - method hudson.model.queue.SubTask getLastBuiltOn
      - method hudson.model.Result combine hudson.model.Result
      - method hudson.model.Result isWorseOrEqualTo hudson.model.Result
      - method hudson.model.Result isWorseThan hudson.model.Result
      - method hudson.model.Run getCause java.lang.Class
      - method hudson.model.Run getDuration
      - method hudson.model.Run getEnvironment
      - method hudson.model.Run getEnvironment hudson.model.TaskListener
      - method hudson.model.Run getEnvVars
      - method hudson.model.Run getExecutor
      - method hudson.model.Run getId
      - method hudson.model.Run getLog int
      - method hudson.model.Run getNumber
      - method hudson.model.Run getParent
      - method hudson.model.Run getPreviousSuccessfulBuild
      - method hudson.model.Run getResult
      - method hudson.model.Run getStartTimeInMillis
      - method hudson.model.Run getUrl
      - method hudson.model.Run isBuilding
      - method hudson.model.Saveable save
      - method hudson.model.Slave getComputer
      - method hudson.Proc join
      - method hudson.slaves.Cloud provision hudson.model.Label int
      - method hudson.slaves.Cloud provision hudson.slaves.Cloud$CloudState int
      - method hudson.util.RunList byTimestamp long long
      - method hudson.util.RunList getLastBuild
      - method java.io.File getParent
      - method java.io.OutputStream write byte[]
      - method java.lang.Class getDeclaredMethods
      - method java.lang.Class isInstance java.lang.Object
      - method java.lang.Class newInstance
      - method java.lang.Runtime availableProcessors
      - method java.lang.String getBytes java.lang.String
      - method java.lang.Throwable getStackTrace
      - method java.net.HttpURLConnection getErrorStream
      - method java.net.HttpURLConnection getResponseCode
      - method java.net.HttpURLConnection setRequestMethod java.lang.String
      - method java.net.InetAddress getHostAddress
      - method java.net.URL openConnection
      - method java.net.URLConnection connect
      - method java.net.URLConnection getInputStream
      - method java.net.URLConnection getOutputStream
      - method java.net.URLConnection setDoOutput boolean
      - method java.net.URLConnection setRequestProperty java.lang.String java.lang.String
      - method java.text.DateFormat parse java.lang.String java.text.ParsePosition
      - method java.time.chrono.ChronoLocalDateTime format java.time.format.DateTimeFormatter
      - method java.time.Duration toHours
      - method java.time.Duration toMinutes
      - method java.time.Instant toEpochMilli
      - method java.time.chrono.ChronoLocalDateTime format java.time.format.DateTimeFormatter
      - method java.util.Date toInstant
      - method java.util.regex.MatchResult groupCount
      - method java.util.StringJoiner add java.lang.CharSequence
      - method javax.mail.internet.MimeMessage setRecipients javax.mail.Message$RecipientType java.lang.String
      - method javax.mail.Message setSubject java.lang.String
      - method javax.mail.internet.MimeMessage setRecipients javax.mail.Message$RecipientType java.lang.String
      - method jenkins.model.Jenkins getCloud java.lang.String
      - method jenkins.model.Jenkins getComputer java.lang.String
      - method jenkins.model.Jenkins getComputers
      - method jenkins.model.Jenkins getComputers java.lanxg.String
      - method jenkins.model.Jenkins getItemByFullName java.lang.String
      - method jenkins.model.Jenkins getItemByFullName java.lang.String java.lang.Class
      - method jenkins.model.Jenkins getItems java.lang.Class
      - method jenkins.model.Jenkins getNode java.lang.String
      - method jenkins.model.Jenkins getRootUrl
      - method jenkins.model.Jenkins restart
      - method jenkins.model.Jenkins safeRestart
      - method jenkins.model.JenkinsLocationConfiguration getUrl
      - method jenkins.triggers.TriggeredItem getTriggers
      - method jenkins.model.ParameterizedJobMixIn$ParameterizedJob getTriggers
      - method jenkins.model.ParameterizedJobMixIn$ParameterizedJob isDisabled
      - method jenkins.model.ParameterizedJobMixIn$ParameterizedJob scheduleBuild2 int hudson.model.Action[]
      - method jenkins.model.ParameterizedJobMixIn$ParameterizedJob setDisabled boolean
      - method jenkins.triggers.SCMTriggerItem getSCMTrigger
      - method net.bull.javamelody.internal.model.MemoryInformations getUsedMemory
      - method org.apache.http.client.HttpClient execute org.apache.http.client.methods.HttpUriRequest
      - method org.apache.http.client.methods.RequestBuilder addParameter java.lang.String java.lang.String
      - method org.apache.http.client.methods.RequestBuilder build
      - method org.apache.http.client.methods.RequestBuilder setUri java.net.URI
      - method org.apache.http.HttpEntityEnclosingRequest setEntity org.apache.http.HttpEntity
      - method org.apache.http.HttpMessage setHeader java.lang.String java.lang.String
      - method org.apache.http.HttpResponse getEntity
      - method org.apache.http.HttpResponse getStatusLine
      - method org.apache.http.impl.client.HttpClientBuilder build
      - method org.apache.http.StatusLine getStatusCode
      - method org.jenkinsci.plugins.buildmetadata.plugin.BuildMetadata getValue
      - method org.jenkinsci.plugins.p4.client.ConnectionHelper getConnection
      - method org.jenkinsci.plugins.p4.client.ConnectionHelper getUser
      - method org.jenkinsci.plugins.p4.credentials.P4Credentials getP4port
      - method org.jenkinsci.plugins.structs.describable.UninstantiatedDescribable instantiate
      - method org.jenkinsci.plugins.workflow.job.WorkflowRun doKill
      - method org.jenkinsci.plugins.workflow.job.WorkflowRun doStop
      - method org.jenkinsci.plugins.workflow.steps.FlowInterruptedException getResult
      - method org.jenkinsci.plugins.workflow.support.actions.EnvironmentAction getEnvironment
      - method org.jenkinsci.plugins.workflow.support.steps.build.RunWrapper getRawBuild
      - method com.sonyericsson.jenkins.plugins.bfa.model.IFailureCauseMetricData getCategories
      - method com.sonyericsson.jenkins.plugins.bfa.model.IFailureCauseMetricData getName
      - method hudson.model.Cause$UpstreamCause getUpstreamProject
      - method hudson.model.AbstractProject scheduleBuild2 int hudson.model.Cause hudson.model.Action[]
      - new hudson.model.Cause$UpstreamCause hudson.model.Run
      - new hudson.model.Cause$UserIdCause
      - new hudson.model.ParametersAction java.util.List
      - new hudson.model.StreamBuildListener java.io.OutputStream
      - new hudson.util.NullStream
      - new com.amazon.jenkins.ec2fleet.EC2FleetCloud java.lang.String java.lang.String java.lang.String java.lang.String java.lang.String java.lang.String java.lang.String java.lang.String java.lang.String hudson.slaves.ComputerConnector boolean boolean java.lang.Integer int int int boolean boolean java.lang.String boolean java.lang.Integer java.lang.Integer boolean java.lang.Integer boolean
      - new com.amazon.jenkins.ec2fleet.EC2FleetCloud java.lang.String java.lang.String java.lang.String java.lang.String java.lang.String java.lang.String java.lang.String java.lang.String java.lang.String hudson.slaves.ComputerConnector boolean boolean java.lang.Integer int int int int boolean boolean java.lang.String boolean java.lang.Integer java.lang.Integer boolean java.lang.Integer boolean
      - new com.amazon.jenkins.ec2fleet.FleetStateStats com.amazon.jenkins.ec2fleet.FleetStateStats int
      - new groovy.util.XmlParser
      - new hudson.model.Cause$RemoteCause java.lang.String java.lang.String
      - new hudson.model.StringParameterValue java.lang.String java.lang.String
      - new hudson.slaves.Cloud$CloudState hudson.model.Label int
      - new hudson.slaves.OfflineCause$ByCLI java.lang.String
      - new hudson.util.StreamTaskListener java.io.OutputStream
      - new java.io.ByteArrayOutputStream
      - new java.io.File java.lang.String
      - new java.io.PrintStream java.io.OutputStream
      - new java.lang.IllegalArgumentException java.lang.String
      - new java.lang.RuntimeException java.lang.String
      - new java.lang.StringBuilder
      - new java.util.StringJoiner java.lang.CharSequence
      - new java.net.URI java.lang.String
      - new java.text.ParsePosition int
      - new net.bull.javamelody.internal.model.MemoryInformations
      - new org.apache.http.client.methods.HttpPost java.lang.String
      - new org.apache.http.entity.StringEntity java.lang.String
      - new org.apache.http.HttpException java.lang.String
      - new org.jenkinsci.plugins.buildmetadata.plugin.StringArrayBuildMetadata java.lang.String java.lang.String java.util.Collection
      - new org.jenkinsci.plugins.buildmetadata.plugin.StringBuildMetadata java.lang.String java.lang.String java.lang.String
      - new org.jenkinsci.plugins.p4.client.ConnectionHelper java.lang.String hudson.model.TaskListener
      - new org.jenkinsci.plugins.workflow.steps.FlowInterruptedException hudson.model.Result jenkins.model.CauseOfInterruption[]
      - new org.jenkinsci.plugins.workflow.support.steps.build.RunWrapper hudson.model.Run boolean
      - method jenkins.model.HistoricalBuild isBuilding
      - method hudson.model.Job getQueueItem
      - method hudson.model.Queue$Item getInQueueSince
      - method hudson.model.Queue$Item getAssignedLabel
      - method jenkins.model.HistoricalBuild getResult
      - staticMethod hudson.model.Hudson getInstance
      - staticField hudson.model.TaskListener NULL
      - staticMethod com.ea.jenkins.plugins.resourcemanagerplugin.ResourceManagerConfiguration get
      - staticMethod com.sonyericsson.jenkins.plugins.bfa.BuildFailureScanner scanIfNotScanned hudson.model.Run java.io.PrintStream
      - staticMethod groovy.util.Eval me java.lang.String
      - staticMethod hudson.console.ModelHyperlinkNote encodeTo java.lang.String java.lang.String
      - staticMethod hudson.model.Queue getInstance
      - staticMethod hudson.model.Run fromExternalizableId java.lang.String
      - staticMethod hudson.model.User getById java.lang.String boolean
      - staticMethod hudson.tasks.MailAddressResolver resolve hudson.model.User
      - staticMethod java.lang.Double parseDouble java.lang.String
      - staticMethod java.lang.Runtime getRuntime
      - staticMethod java.lang.String valueOf int
      - staticMethod java.lang.System gc
      - staticMethod java.lang.Thread sleep long
      - staticMethod java.net.InetAddress getByName java.lang.String
      - staticMethod java.time.Duration between java.time.temporal.Temporal java.time.temporal.Temporal
      - staticMethod java.time.format.DateTimeFormatter ofPattern java.lang.String
      - staticMethod java.time.Instant now
      - staticMethod java.time.LocalDateTime now
      - staticMethod java.time.format.DateTimeFormatter ofPattern java.lang.String
      - staticMethod java.util.Collections shuffle java.util.List
      - staticMethod jenkins.model.Jenkins get
      - staticMethod jenkins.model.Jenkins getInstance
      - staticMethod jenkins.model.Jenkins getInstanceOrNull
      - staticMethod jenkins.model.JenkinsLocationConfiguration get
      - staticMethod org.apache.http.client.methods.RequestBuilder post
      - staticMethod org.apache.http.impl.client.HttpClientBuilder create
      - staticMethod org.apache.http.impl.client.HttpClients createDefault
      - staticMethod org.apache.http.util.EntityUtils toString org.apache.http.HttpEntity
      - staticMethod org.apache.http.util.EntityUtils toString org.apache.http.HttpEntity java.lang.String
      - staticMethod org.codehaus.groovy.runtime.DateGroovyMethods minus java.util.Date int
      - staticMethod org.codehaus.groovy.runtime.DateGroovyMethods plus java.util.Date int
      - staticMethod org.codehaus.groovy.runtime.DefaultGroovyMethods collectEntries java.lang.Iterable groovy.lang.Closure
      - staticMethod org.codehaus.groovy.runtime.DefaultGroovyMethods count java.lang.String java.lang.String
      - staticMethod org.codehaus.groovy.runtime.DefaultGroovyMethods getAt java.lang.Object java.lang.String
      - staticMethod org.codehaus.groovy.runtime.DefaultGroovyMethods getMetaClass java.lang.Object
      - staticMethod org.codehaus.groovy.runtime.DefaultGroovyMethods getText
      - staticMethod org.codehaus.groovy.runtime.DefaultGroovyMethods inspect java.lang.Object
      - staticMethod org.codehaus.groovy.runtime.DefaultGroovyMethods leftShift java.lang.StringBuilder java.lang.Object
      - staticMethod org.codehaus.groovy.runtime.DefaultGroovyMethods max java.util.Collection
      - staticMethod org.codehaus.groovy.runtime.DefaultGroovyMethods min java.util.Collection
      - staticMethod org.codehaus.groovy.runtime.DefaultGroovyMethods minus java.util.List java.lang.Object
      - staticMethod org.codehaus.groovy.runtime.DefaultGroovyMethods print java.lang.Object java.lang.Object
      - staticMethod org.codehaus.groovy.runtime.DefaultGroovyMethods println groovy.lang.Closure java.lang.Object
      - staticMethod org.codehaus.groovy.runtime.DefaultGroovyMethods println java.lang.Object java.lang.Object
      - staticMethod org.codehaus.groovy.runtime.DefaultGroovyMethods round java.lang.Double
      - staticMethod org.codehaus.groovy.runtime.DefaultGroovyMethods sort java.util.Collection boolean groovy.lang.Closure
      - staticMethod org.codehaus.groovy.runtime.DefaultGroovyMethods sort java.util.Map
      - staticMethod org.codehaus.groovy.runtime.DefaultGroovyMethods toUnique java.util.List groovy.lang.Closure
      - staticMethod org.codehaus.groovy.runtime.DefaultGroovyMethods toURL java.lang.String
      - staticMethod org.codehaus.groovy.runtime.DefaultGroovyMethods toUnique java.util.List groovy.lang.Closure
      - staticMethod org.codehaus.groovy.runtime.EncodingGroovyMethods encodeBase64 byte[]
      - staticMethod org.codehaus.groovy.runtime.ScriptBytecodeAdapter unaryMinus java.lang.Object
      - staticMethod org.codehaus.groovy.runtime.ScriptBytecodeAdapter unaryPlus java.lang.Object
      - staticMethod org.jenkinsci.plugins.p4.client.ConnectionHelper findCredential java.lang.String
      - new com.amazon.jenkins.ec2fleet.EC2FleetCloud java.lang.String java.lang.String java.lang.String java.lang.String java.lang.String java.lang.String java.lang.String java.lang.String java.lang.String hudson.slaves.ComputerConnector boolean boolean java.lang.Integer int int int boolean boolean java.lang.String boolean java.lang.Integer java.lang.Integer boolean java.lang.Integer boolean
