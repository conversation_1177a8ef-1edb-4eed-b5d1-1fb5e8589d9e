package scripts.schedulers.all

import com.ea.lib.LibJenkins
import com.ea.project.GetMasterFile
import hudson.model.Result

def final_result = Result.SUCCESS  // Initialize final_result in the global scope

/**
 * verified_integration_scheduler.groovy
 */
pipeline {
    agent any
    options {
        allowBrokenBuildClaiming()
        timestamps()
    }
    stages {
        stage('Trigger integration jobs using latest verified changelists.') {
            steps {
                script {
                    // Get changelists
                    def last_successful_code_build = LibJenkins.getLastStableCodeChangelist(env.integration_reference_job)
                    def last_successful_data_build = LibJenkins.getLastStableDataChangelist(env.integration_reference_job)
                    def code_changelist = params.code_changelist ?: last_successful_code_build
                    def data_changelist = params.data_changelist ?: last_successful_data_build

                    // Get changelist for the previous build of this job, needed to generate the submit message.
                    // This is used for the integrate_upgrade_one_stream jobs, code_changelist is used for legacy reasons.
                    def last_changelist = LibJenkins.getLastStableCodeChangelist(env.JOB_NAME as String)

                    // Get branches of different types
                    def masterSettings = GetMasterFile.get_masterfile(BUILD_URL)[0]
                    def integrate_branches = masterSettings.integrate_branches

                    def full_branches = []
                    def code_only_branches = []
                    def data_only_branches = []
                    def copyIntegrateBranches = []
                    def integrateCompileUpgradeCookBranches = []
                    def integrateUpgradeOneStream = []

                    for (branch in integrate_branches.keySet()) {
                        def branch_info = integrate_branches[branch]
                        if (branch_info.verified_integration == true
                            && branch_info.data_upgrade != true
                            && branch_info.source_branch == env.source_branch
                            && branch_info.target_branch == env.target_branch) {
                            if (branch_info.code == true) {
                                full_branches += branch_info.data ? branch_info : []
                                code_only_branches += branch_info.data ? [] : branch_info
                            } else {
                                data_only_branches += branch_info.data ? branch_info : []
                                copyIntegrateBranches += branch_info.copy_integrate_compile ? branch_info : []
                                integrateCompileUpgradeCookBranches += branch_info.integrate_compile_upgrade_cook ? branch_info : []
                                integrateUpgradeOneStream += branch_info.integrate_upgrade_one_stream ? branch_info : []
                            }
                        }
                    }
                    // Use changelists
                    if (data_changelist == null) {
                        // Allow data_changelist to be null, if we only have code integrations as downstream jobs.
                        if (full_branches.isEmpty() && data_only_branches.isEmpty()) {
                            data_changelist = 'unset'
                        } else {
                            echo 'Missing data changelist, aborting build!'
                            currentBuild.result = Result.FAILURE.toString()
                            return
                        }
                    }
                    def args = [
                        string(name: 'code_changelist', value: code_changelist),
                        string(name: 'data_changelist', value: data_changelist),
                        string(name: 'last_changelist', value: last_changelist),
                    ]
                    def jobs = [:]

                    def inject_map = [
                        'code_changelist': code_changelist,
                        'data_changelist': data_changelist,
                    ]
                    EnvInject(currentBuild, inject_map)
                    currentBuild.displayName = env.JOB_NAME + '.' + data_changelist + '.' + code_changelist

                    // Create and run integration jobs
                    def retry_limit = env.retry_limit.toInteger()
                    def slack_settings = ''
                    for (def run = 0; run <= retry_limit; run++) {
                        for (branch in full_branches + code_only_branches + data_only_branches) {
                            slack_settings = branch.slack_channel ?: slack_settings
                            String code_job_name = branch.source_branch + '.code.' + IntegrationDirection(branch) + '.' + branch.target_branch
                            String data_job_name = branch.source_branch + '.data.' + IntegrationDirection(branch) + '.' + branch.target_branch

                            if (!data_only_branches.contains(branch)
                                && NeedsRebuildData(code_job_name, code_changelist, data_changelist)) {
                                jobs[code_job_name] = {
                                    def downstream_job = build(job: code_job_name, parameters: args, propagate: false)
                                    final_result = final_result.combine(Result.fromString(downstream_job.result))
                                    LibJenkins.printRunningJobs(this)
                                }
                            }

                            if (!code_only_branches.contains(branch)
                                && NeedsRebuildData(data_job_name, code_changelist, data_changelist)) {
                                jobs[data_job_name] = {
                                    def downstream_job = build(job: data_job_name, parameters: args, propagate: false)
                                    final_result = final_result.combine(Result.fromString(downstream_job.result))
                                    LibJenkins.printRunningJobs(this)
                                }
                            }
                        }

                        for (branch in copyIntegrateBranches) {
                            slack_settings = branch.slack_channel ?: slack_settings
                            String copyIntegrateJob = branch.source_branch + '.code.copy-integrate-to.' + branch.target_branch

                            if (NeedsRebuildData(copyIntegrateJob, code_changelist, data_changelist)) {
                                jobs[copyIntegrateJob] = {
                                    def downstreamJob = build(job: copyIntegrateJob, parameters: args, propagate: false)
                                    final_result = final_result.combine(Result.fromString(downstreamJob.result))
                                    LibJenkins.printRunningJobs(this)
                                }
                            }
                        }

                        for (branch in integrateCompileUpgradeCookBranches) {
                            slack_settings = branch.slack_channel ?: slack_settings
                            String integrateCompileUpgradeCookJob = branch.source_branch + '.code.compile-upgrade-cook.integrate-to.' + branch.target_branch

                            if (NeedsRebuildData(integrateCompileUpgradeCookJob, code_changelist, data_changelist)) {
                                jobs[integrateCompileUpgradeCookJob] = {
                                    def downstreamJob = build(job: integrateCompileUpgradeCookJob, parameters: args, propagate: false)
                                    final_result = final_result.combine(Result.fromString(downstreamJob.result))
                                    LibJenkins.printRunningJobs(this)
                                }
                            }
                        }

                        for (branch in integrateUpgradeOneStream) {
                            slack_settings = branch.slack_channel ?: slack_settings
                            String integrateUpgradeOneStreamJob = branch.source_branch + '.integrate-upgrade-to.' + branch.target_branch

                            if (NeedsRebuildCode(integrateUpgradeOneStreamJob, code_changelist)) {
                                jobs[integrateUpgradeOneStreamJob] = {
                                    def downstreamJob = build(job: integrateUpgradeOneStreamJob, parameters: args, propagate: false)
                                    final_result = final_result.combine(Result.fromString(downstreamJob.result))
                                    LibJenkins.printRunningJobs(this)
                                }
                            }
                        }

                        parallel(jobs)

                        if (final_result == Result.SUCCESS) {
                            break
                        }
                    }
                    currentBuild.result = final_result.toString()

                    // Send Slack notifications if slack_channel is configured
                    if (slack_settings != '') {
                        SlackMessageNew(currentBuild, slack_settings, ProjectClass(env.source_project_name).short_name)
                    }

                    DownstreamErrorReporting(currentBuild)
                }
            }
        }
    }
}
