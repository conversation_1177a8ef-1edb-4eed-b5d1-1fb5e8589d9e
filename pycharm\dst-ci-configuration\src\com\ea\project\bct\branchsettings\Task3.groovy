package com.ea.project.bct.branchsettings

import com.ea.project.bct.Bct

class Task3 {
    // Settings for jobs
    static Class project = Bct
    static Map general_settings = [
        dataset                 : project.dataset,
        elipy_call              : project.elipy_call + ' --use-fbenv-core',
        elipy_install_call      : project.elipy_install_call,
        frostbite_licensee      : project.frostbite_licensee,
        workspace_root          : project.workspace_root,
        azure_elipy_call        : project.azure_elipy_call,
        azure_elipy_install_call: project.azure_elipy_install_call,
        azure_workspace_root    : project.azure_workspace_root,
    ]
    static Map code_settings = [
        deploy_tests               : true,
        fake_ooa_wrapped_symbol    : false,
        extra_code_args            : [' --icepick-run-args --parallel --icepick-run-args true --icepick-run-args --max-parallel-tests --icepick-run-args 13'],
        report_build_version       : ' --reporting-build-version-id %code_changelist%',
        slack_channel_code         : [
            channels                  : ['#bct-build-notify', '#bct-online-task3'],
            skip_for_multiple_failures: true,
        ],
        slack_channel_code_nomaster: [
            channels                  : ['#bct-online-task3'],
            skip_for_multiple_failures: true,
        ],
        sndbs_enabled              : true,
    ]
    static Map data_settings = [
        poolbuild_data    : true,
        slack_channel_data: [
            channels                  : ['#bct-build-notify'],
            skip_for_multiple_failures: true,
        ],
    ]
    static Map frosty_settings = [
        poolbuild_frosty    : true,
        timeout_hours_frosty: 6,
        use_linuxclient     : true,
    ]
    static Map standard_jobs_settings = code_settings + data_settings + frosty_settings + [
        asset                     : 'Task3Levels',
        clean_local               : true,
        enable_lkg_p4_counters    : true,
        custom_tests              : [
            custom_configs: [
                'online-integration-tests.json',
                'online-unit-tests-stress.json',
                'online-unit-tests-stomp.json',
            ],
            trigger_type  : 'none',
        ],
        data_reference_job        : 'task3.code.start',
        extra_data_args           : ['--pipeline-args -Pipeline.MaxConcurrencyThrottleMemoryThreshold --pipeline-args 0.8 --pipeline-args -ContentDatabase.EnableHailstormLocalCache --pipeline-args true '],
        extra_frosty_args         : ['--pipeline-args -Pipeline.MaxConcurrencyThrottleMemoryThreshold --pipeline-args 0.8 --pipeline-args -ContentDatabase.EnableHailstormLocalCache --pipeline-args true '],
        frosty_reference_job      : 'task3.code.start',
        server_asset              : 'Task3Levels',
        skip_icepick_settings_file: true,
        strip_symbols             : false,
    ]
    static Map icepick_settings = [
        ignore_icepick_exit_code: false
    ]
    static Map preflight_settings = [:]

    // Matrix definitions for jobs
    static List code_matrix = [
        [name: 'win64game', configs: ['final', 'release', 'retail', 'performance']],
        [name: 'tool', configs: [[name: 'release', compile_unit_tests: true, run_unit_tests: true],]],
        [name: 'win64server', configs: ['final']],
        [name: 'xbsx', configs: ['final', 'retail', 'release', 'performance']],
        [name: 'ps5', configs: ['final', 'retail', 'release', 'performance']],
        [name: 'linux64server', configs: ['final']],
        [name: 'linux64', configs: ['final']],
    ]
    static List code_nomaster_matrix = [
        [name: 'win64game', configs: ['retail']],
        [name: 'win64server', configs: ['final']],
        [name: 'ps5', configs: ['final']],
        [name: 'xbsx', configs: ['retail']],
        [name: 'linux64server', configs: ['final']],
        [name: 'tool', configs: ['release']],
    ]
    static List code_downstream_matrix = [
        [name: '.custom-tests.start', args: []],
        [name: '.frosty.start', args: []],
    ]
    static List data_matrix = []
    static List data_downstream_matrix = []
    static List patchdata_matrix = []
    static List frosty_matrix = [
        [name: 'win64', variants: [[format: 'files', config: 'final', region: 'ww', args: ''],
                                   [format: 'digital', config: 'final', region: 'ww', args: ''],
                                   [format: 'digital', config: 'retail', region: 'ww', args: ''],
                                   [format: 'files', config: 'performance', region: 'ww', args: ''],
                                   [format: 'files', config: 'release', region: 'ww', args: ''],
                                   [format: 'digital', config: 'release', region: 'ww', args: '']]],
        [name: 'ps5', variants: [[format: 'digital', config: 'final', region: 'dev', args: ''],
                                 [format: 'files', config: 'final', region: 'dev', args: ' --additional-configs release'],
                                 [format: 'files', config: 'performance', region: 'dev', args: '']]],
        [name: 'xbsx', variants: [[format: 'digital', config: 'final', region: 'ww', args: ''],
                                  [format: 'files', config: 'final', region: 'ww', args: ' --additional-configs release'],
                                  [format: 'files', config: 'performance', region: 'ww', args: '']]],
        [name: 'server', variants: [[format: 'files', config: 'final', region: 'ww', args: '']]],
        [name: 'linuxserver', variants: [[format: 'digital', config: 'final', region: 'ww', args: ''],
                                         [format: 'files', config: 'final', region: 'ww', args: '']]],
        [name: 'linux64', variants: [[format: 'files', config: 'final', region: 'ww', args: '']]],
    ]
    static List frosty_downstream_matrix = []
    static List frosty_for_patch_matrix = []
    static List patchfrosty_matrix = []
    static List patchfrosty_downstream_matrix = []
    static List code_preflight_matrix = []
    static List data_preflight_matrix = []
    static List spin_upload_matrix = []
    static List shift_upload_matrix = []
    static List shift_downstream_matrix = []
    static List freestyle_job_trigger_matrix = []
    static List azure_uploads_matrix = []
}
