package scripts.schedulers

import com.sonyericsson.jenkins.plugins.bfa.model.FailureCauseBuildAction
import com.sonyericsson.jenkins.plugins.bfa.BuildFailureScanner
import jenkins.model.Jenkins

/**
* Reapply BFA to a specific job
* This script is used to reapply the BFA (Build Failure Analyzer) to a specific build
*
* reapplyBfaToJob.groovy
*/

pipeline {
    agent any
    parameters {
        string(
            name: 'jobUrl',
            defaultValue: '',
            description: 'The URL of the job to reapply BFA. Example: https://example.com/job/my_job/1234/'
        )
    }
    stages {
        stage('Reapply BFA') {
            steps {
                script {
                    // Input parameters
                    String jobUrl = params.jobUrl
                    if (!jobUrl?.trim()) {
                        error "Parameter 'jobUrl' must not be empty."
                    }
                    String jobName = ''
                    int buildNumber = 0

                    // Extract jobName and buildNumber from jobUrl (support nested jobs)
                    def matcher = jobUrl =~ /job\/(.+?)\/(\d+)(?:\/|$)/
                    if (matcher.find()) {
                        jobName = matcher.group(1).replaceAll('/job/', '/')
                        buildNumber = matcher.group(2).toInteger()
                    } else {
                        error "Invalid jobUrl format: $jobUrl. Expected format: .../job/my_job/1234/"
                    }

                    // Find the job
                    def jenkins = Jenkins.get()
                    def job = jenkins.getItemByFullName(jobName)
                    if (!job) {
                        error "Job not found: $jobName"
                    }
                    def build = job.getBuildByNumber(buildNumber)
                    if (!build) {
                        error "Build number $buildNumber not found for job $jobName"
                    }

                    // Remove old cause
                    build.removeActions(FailureCauseBuildAction)

                    // Scan and add new cause
                    def baos = new ByteArrayOutputStream()
                    def stream = new PrintStream(baos)
                    BuildFailureScanner.scanIfNotScanned(build, stream)
                    build.save()
                    stream.flush()

                    echo baos.toString()
                    echo 'Done'
                    echo "Job url: ${jobUrl}"
                }
            }
        }
    }
}
