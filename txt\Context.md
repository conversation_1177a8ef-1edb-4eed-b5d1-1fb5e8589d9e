Problem
We are currently producing combined bundles in the frosty and patchfrosty jobs. A problem with this is that we are producing the same set of bundles multiple times if we are building more than one config or format for the same platform. e.g. final/retail or files/digital for win64. We only copy one of the produced sets of combined bundles to the network share, from the build that gets to this step first. The other set of bundles isn't copied.
We should solve this by moving the creation of the combined bundles to a separate build job. This build job would create combined bundles for a certain platform, then the frosty/patchfrosty jobs for one or more config would copy these bundles from the network share, similar to how we handle this for non-combined bundles.
For patchfrosty jobs, this separate build job should also involve creating the delta bundles from the combined bundles.
🉑 Acceptance Criteria
We have created an Elipy script that produces combined bundles.
This is currently done in the frosty and patchfrosty scripts, and we should be able to use the same logic here.
The script allows producing both head bundles and, if needed, also delta bundles.
Producing delta bundles for combined builds is currently done in the patchfrosty script, and we should be able to reuse the same logic here.
The step to produce delta bundles should only run if they are needed for the stream the job runs on. See the patchdata script for guidance.
The output from this job should be copied to the network share, since the produced combined bundles will be used in frosty/patchfrosty jobs running on other machines.
In the frosty/patchfrosty scripts, we need to add a feature flag to switch between the current behaviour (combining bundles in these scripts) and the new behaviour (combingin in the new separate script).
The default behaviour, if we don't send this flag from Jenkins, should be to run the job the same way as we do today.
If we are using the new setup, we should skip the combine step and instead copy the result of the separate job from the network share. This is similar to how we do for non-combined frosty/patchfrosty jobs.
We should also skip the step where we produce delta bundles, since this step will also be moved to the new script. The delta bundles should also be copied from the network share.


In filer.py:
Line 351-355: the bundle should be copied from the network path to the local of the VM so 
´frostbite_core.get_tnt_root(), "local"´ is not correct, we do not want to copy from a local path in VM to another path in the VM
I think we can still use the old version from master branch as below
line 327-336: 
        combined_path = filer_paths.get_head_bundles_path(
            data_branch=data_branch,
            data_changelist=data_changelist,
            code_branch=code_branch,
            code_changelist=code_changelist,
            platform=platform,
            bundles_dir_name=bundles_dir_name,
        ).replace("/bundles/", "/bundles_combine/")

        core.robocopy(combined_path, dest, purge=True)
but we need to update the get_head_bundles_path so it can return the correct path for the combined bundles with our current combined_output_folder structure
And combined_output_folder also need to use folder separation instead of underscore

So the flow would be:
1. Create a new Elipy script that produces combined bundles. (done?) 
   - This script should be able to produce both head bundles and delta bundles if needed.
   - The logic for producing delta bundles should be reused from the existing patchfrosty script.
   - The output from this job should be copied to the network share.
2. Update the `filer.py` to copy the combined bundles from the network share to the local VM path.
3. Update the `get_head_bundles_path` function to return the correct path for combined bundles (and can be used in filer.py)
