import sys
import os
sys.path.insert(0, r'pycharm\elipy-scripts')

from unittest.mock import patch, MagicMock
from click.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>
from dice_elipy_scripts.combined_bundles import cli

# Run the test with full mocks to see what's causing the failure
runner = CliRunner()

# Setup mocks like in the working test
with patch("dice_elipy_scripts.combined_bundles.add_sentry_tags") as mock_sentry, \
     patch("elipy2.running_processes.kill") as mock_kill, \
     patch("elipy2.frostbite.icepick.IcepickUtils.clean_local_frosty") as mock_clean_frosty, \
     patch("elipy2.avalanche.combine") as mock_combine, \
     patch("shutil.copytree") as mock_copytree, \
     patch("shutil.rmtree") as mock_rmtree, \
     patch("os.path.split") as mock_split, \
     patch("os.path.exists") as mock_exists, \
     patch("elipy2.frostbite_core.get_tnt_root") as mock_get_tnt_root, \
     patch("elipy2.local_paths.get_local_bundles_path") as mock_get_local_bundles_path, \
     patch("elipy2.filer.FilerUtils") as mock_filer_utils:
    
    # Setup mocks like in the working test
    mock_split.side_effect = lambda path: (os.path.dirname(path), os.path.basename(path))
    mock_get_local_bundles_path.side_effect = [
        "/path/to/deployed_bundles_combine",
        "/path/to/deployed_bundles_main",
        "/path/to/deployed_bundles_combine_second",
    ]
    mock_filer_instance = MagicMock()
    mock_filer_utils.return_value = mock_filer_instance
    
    args = [
        "xbsx",
        "--data-directory", "Data",
        "--code-branch", "build-main-dre",
        "--code-changelist", "436418",
        "--data-branch", "build-main-dre",
        "--data-changelist", "436418",
        "--combine-code-branch", "build-release",
        "--combine-code-changelist", "436400",
        "--combine-data-branch", "build-release",
        "--combine-data-changelist", "436400",
    ]
    
    print("Running test with xbsx platform...")
    result = runner.invoke(cli, args)
    
    print(f"Exit code: {result.exit_code}")
    print(f"Output: {result.output}")
    if result.exception:
        print(f"Exception: {result.exception}")
        import traceback
        traceback.print_exception(type(result.exception), result.exception, result.exception.__traceback__)
