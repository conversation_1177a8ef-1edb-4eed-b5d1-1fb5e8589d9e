#!/usr/bin/env python
"""Verify test fix works."""

import subprocess
import sys
import os

def main():
    """Run the specific failing tests to verify they now pass."""
    os.chdir(r"c:\Users\<USER>\vscode")
    
    # Test just the two failing tests with a timeout
    cmd = [
        sys.executable, "-m", "pytest", 
        r"pycharm\elipy-scripts\dice_elipy_scripts\tests\test_combined_bundles.py",
        "-k", "test_xbsx_platform_uses_smart_delivery_settings or test_non_xbsx_platform_uses_regular_settings",
        "-v", "--tb=short"
    ]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
        print(f"Exit code: {result.returncode}")
        print("STDOUT:")
        print(result.stdout)
        if result.stderr:
            print("STDERR:")
            print(result.stderr)
        
        # Check if tests passed
        if "2 passed" in result.stdout or "PASSED" in result.stdout:
            print("\n✓ Tests appear to be passing!")
            return True
        else:
            print("\n✗ Tests still failing")
            return False
    except subprocess.TimeoutExpired:
        print("Test run timed out")
        return False
    except Exception as e:
        print(f"Error running tests: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
