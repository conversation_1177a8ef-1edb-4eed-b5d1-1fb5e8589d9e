#!/usr/bin/env python
"""Final verification of the combined_bundles test fix.

This script verifies that the two failing tests now pass with the applied fixes.
"""

import os
import sys
import subprocess
import time

def run_tests():
    """Run the specific tests that were failing."""
    print("=== Combined Bundles Test Fix Verification ===")
    print("Time started:", time.strftime("%H:%M:%S"))
    
    os.chdir(r"c:\Users\<USER>\vscode")
    
    # Run the two specific tests that were failing
    test_commands = [
        "test_xbsx_platform_uses_smart_delivery_settings",
        "test_non_xbsx_platform_uses_regular_settings"
    ]
    
    results = {}
    
    for test_name in test_commands:
        print(f"\nRunning {test_name}...")
        
        cmd = [
            sys.executable, "-m", "pytest", 
            f"pycharm\\elipy-scripts\\dice_elipy_scripts\\tests\\test_combined_bundles.py::TestCombinedBundles::{test_name}",
            "-v", "--tb=short"
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
            results[test_name] = {
                'exit_code': result.returncode,
                'stdout': result.stdout,
                'stderr': result.stderr
            }
            
            if result.returncode == 0:
                print(f"✓ {test_name} PASSED")
            else:
                print(f"✗ {test_name} FAILED (exit code: {result.returncode})")
                
        except subprocess.TimeoutExpired:
            print(f"✗ {test_name} TIMED OUT")
            results[test_name] = {'exit_code': -1, 'stdout': '', 'stderr': 'Timeout'}
    
    # Summary
    print("\n" + "="*60)
    print("SUMMARY:")
    passed = sum(1 for r in results.values() if r['exit_code'] == 0)
    total = len(results)
    
    print(f"Passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 All tests are now passing!")
        print("\nThe fix successfully resolved the test failures.")
    else:
        print("❌ Some tests are still failing.")
        print("\nDetailed output:")
        for test_name, result in results.items():
            if result['exit_code'] != 0:
                print(f"\n{test_name}:")
                print(result['stdout'])
                if result['stderr']:
                    print("STDERR:")
                    print(result['stderr'])
    
    print("\nTime completed:", time.strftime("%H:%M:%S"))
    return passed == total

if __name__ == "__main__":
    success = run_tests()
    sys.exit(0 if success else 1)
